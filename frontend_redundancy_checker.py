#!/usr/bin/env python3
"""
Frontend Redundancy Checker
Identifies duplicate and redundant files in the frontend codebase
"""

import os
import json
import hashlib
import ast
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple
from collections import defaultdict

class FrontendRedundancyChecker:
    def __init__(self, frontend_path: str = "frontend"):
        self.frontend_path = Path(frontend_path)
        self.duplicates = defaultdict(list)
        self.similar_files = []
        self.unused_imports = defaultdict(list)
        self.redundant_configs = []

    def find_duplicate_files(self) -> Dict[str, List[str]]:
        """Find files with identical content."""
        file_hashes = defaultdict(list)

        for file_path in self.frontend_path.rglob("*"):
            if file_path.is_file() and not self._should_ignore(file_path):
                try:
                    with open(file_path, 'rb') as f:
                        file_hash = hashlib.md5(f.read()).hexdigest()
                        file_hashes[file_hash].append(str(file_path))
                except (<PERSON><PERSON><PERSON><PERSON>, OSError):
                    continue

        # Only keep groups with duplicates
        duplicates = {h: files for h, files in file_hashes.items() if len(files) > 1}
        return duplicates

    def find_similar_components(self, threshold: float = 0.8) -> List[Tuple[str, str, float]]:
        """Find similar React components based on structure."""
        components = []

        # Find all React component files
        for file_path in self.frontend_path.rglob("*.tsx"):
            if self._is_component_file(file_path):
                components.append(file_path)

        similar_pairs = []
        for i, comp1 in enumerate(components):
            for comp2 in components[i+1:]:
                similarity = self._calculate_component_similarity(comp1, comp2)
                if similarity >= threshold:
                    similar_pairs.append((str(comp1), str(comp2), similarity))

        return similar_pairs

    def find_unused_imports(self) -> Dict[str, List[str]]:
        """Find unused imports in TypeScript/JavaScript files."""
        unused = defaultdict(list)

        for file_path in self.frontend_path.rglob("*.{ts,tsx,js,jsx}"):
            if self._should_ignore(file_path):
                continue

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                imports = self._extract_imports(content)
                used_symbols = self._extract_used_symbols(content)

                for imp in imports:
                    if imp not in used_symbols and not self._is_type_import(imp, content):
                        unused[str(file_path)].append(imp)

            except (IOError, OSError, UnicodeDecodeError):
                continue

        return dict(unused)

    def find_redundant_configs(self) -> List[Dict[str, str]]:
        """Find redundant configuration files."""
        config_patterns = [
            ("playwright.config.*", "Playwright configuration"),
            ("jest.config.*", "Jest configuration"),
            ("vite.config.*", "Vite configuration"),
            ("tsconfig*.json", "TypeScript configuration"),
            ("eslint.config.*", "ESLint configuration"),
            (".env*", "Environment configuration")
        ]

        redundant = []
        for pattern, desc in config_patterns:
            files = list(self.frontend_path.rglob(pattern))
            if len(files) > 1:
                redundant.append({
                    "type": desc,
                    "files": [str(f) for f in files],
                    "recommendation": f"Consolidate {desc} files"
                })

        return redundant

    def _should_ignore(self, file_path: Path) -> bool:
        """Check if file should be ignored."""
        ignore_patterns = [
            "node_modules", ".git", "dist", "build",
            "__pycache__", ".pytest_cache", "coverage",
            "test-results", "playwright-report"
        ]

        return any(pattern in str(file_path) for pattern in ignore_patterns)

    def _is_component_file(self, file_path: Path) -> bool:
        """Check if file is a React component."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Look for React component patterns
            patterns = [
                r'export\s+default\s+function\s+\w+',
                r'export\s+const\s+\w+\s*=\s*\(',
                r'function\s+\w+\s*\([^)]*\)\s*{[^}]*return\s*<',
                r'const\s+\w+\s*=\s*\([^)]*\)\s*=>\s*{[^}]*return\s*<'
            ]

            return any(re.search(pattern, content) for pattern in patterns)
        except:
            return False

    def _calculate_component_similarity(self, file1: Path, file2: Path) -> float:
        """Calculate similarity between two component files."""
        try:
            with open(file1, 'r', encoding='utf-8') as f:
                content1 = f.read()
            with open(file2, 'r', encoding='utf-8') as f:
                content2 = f.read()

            # Extract structural elements
            structure1 = self._extract_component_structure(content1)
            structure2 = self._extract_component_structure(content2)

            # Calculate similarity based on common elements
            common = len(structure1.intersection(structure2))
            total = len(structure1.union(structure2))

            return common / total if total > 0 else 0.0
        except:
            return 0.0

    def _extract_component_structure(self, content: str) -> Set[str]:
        """Extract structural elements from component."""
        elements = set()

        # Extract imports
        imports = re.findall(r'import\s+.*?from\s+[\'"]([^\'"]+)[\'"]', content)
        elements.update(f"import:{imp}" for imp in imports)

        # Extract hooks
        hooks = re.findall(r'use\w+\s*\(', content)
        elements.update(f"hook:{hook[:-1]}" for hook in hooks)

        # Extract JSX elements
        jsx_elements = re.findall(r'<(\w+)', content)
        elements.update(f"jsx:{elem}" for elem in jsx_elements)

        return elements

    def _extract_imports(self, content: str) -> List[str]:
        """Extract import statements."""
        imports = []

        # Match various import patterns
        patterns = [
            r'import\s+(\w+)\s+from',
            r'import\s+{\s*([^}]+)\s*}\s+from',
            r'import\s+\*\s+as\s+(\w+)\s+from'
        ]

        for pattern in patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                if isinstance(match, str):
                    if '{' in match:
                        # Handle destructured imports
                        imports.extend([imp.strip() for imp in match.split(',')])
                    else:
                        imports.append(match.strip())

        return imports

    def _extract_used_symbols(self, content: str) -> Set[str]:
        """Extract symbols used in the code."""
        # Remove import statements and comments
        content_no_imports = re.sub(r'import\s+.*?;', '', content)
        content_no_comments = re.sub(r'//.*?$|/\*.*?\*/', '', content_no_imports, flags=re.MULTILINE | re.DOTALL)

        # Find all identifiers
        identifiers = re.findall(r'\b[a-zA-Z_$][a-zA-Z0-9_$]*\b', content_no_comments)
        return set(identifiers)

    def _is_type_import(self, symbol: str, content: str) -> bool:
        """Check if import is used as a type."""
        type_patterns = [
            f':\\s*{symbol}',
            f'<{symbol}>',
            f'as\\s+{symbol}',
            f'extends\\s+{symbol}'
        ]

        return any(re.search(pattern, content) for pattern in type_patterns)

    def generate_report(self) -> Dict:
        """Generate comprehensive redundancy report."""
        print("🔍 Analyzing frontend for redundancies...")

        duplicates = self.find_duplicate_files()
        similar_components = self.find_similar_components()
        unused_imports = self.find_unused_imports()
        redundant_configs = self.find_redundant_configs()

        report = {
            "summary": {
                "duplicate_files": len(duplicates),
                "similar_components": len(similar_components),
                "files_with_unused_imports": len(unused_imports),
                "redundant_configs": len(redundant_configs)
            },
            "duplicate_files": duplicates,
            "similar_components": similar_components,
            "unused_imports": unused_imports,
            "redundant_configs": redundant_configs,
            "recommendations": self._generate_recommendations(duplicates, similar_components, unused_imports, redundant_configs)
        }

        return report

    def _generate_recommendations(self, duplicates, similar_components, unused_imports, redundant_configs) -> List[str]:
        """Generate actionable recommendations."""
        recommendations = []

        if duplicates:
            recommendations.append(f"🗑️ Remove {len(duplicates)} groups of duplicate files")

        if similar_components:
            recommendations.append(f"🔄 Refactor {len(similar_components)} pairs of similar components")

        if unused_imports:
            total_unused = sum(len(imports) for imports in unused_imports.values())
            recommendations.append(f"🧹 Clean up {total_unused} unused imports across {len(unused_imports)} files")

        if redundant_configs:
            recommendations.append(f"⚙️ Consolidate {len(redundant_configs)} types of redundant configuration files")

        return recommendations

def main():
    checker = FrontendRedundancyChecker()
    report = checker.generate_report()

    # Save report
    with open('frontend_redundancy_report.json', 'w') as f:
        json.dump(report, f, indent=2)

    # Print summary
    print("\n📊 Frontend Redundancy Analysis Complete!")
    print("=" * 50)
    for key, value in report["summary"].items():
        print(f"{key.replace('_', ' ').title()}: {value}")

    print("\n💡 Recommendations:")
    for rec in report["recommendations"]:
        print(f"  {rec}")

    print(f"\n📄 Detailed report saved to: frontend_redundancy_report.json")

if __name__ == "__main__":
    main()
