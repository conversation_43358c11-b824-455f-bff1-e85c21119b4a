# RentUp Knowledge Base

This knowledge base contains comprehensive guides, best practices, and reference materials for the RentUp project. It is organized into frontend and backend sections to help developers find the information they need quickly.

## Table of Contents

- [Frontend](#frontend)
- [Backend](#backend)

## Frontend

The frontend section contains guides and best practices for developing the RentUp frontend using React and Next.js.

### Guides

- [Frontend Trends 2025](./frontend/frontend_trends_2025.md) - Latest trends and technologies in frontend development
- [React and Next.js Best Practices](./frontend/react_nextjs_best_practices.md) - Best practices for developing with React and Next.js
- [Frontend Testing Guide](./frontend/frontend_testing_guide.md) - Comprehensive guide to testing frontend applications
- [Code Organization Guidelines](./frontend/code_organization_guidelines.md) - Guidelines for file size and code organization

## Backend

The backend section contains guides and best practices for developing the RentUp backend using FastAPI and PostgreSQL.

### Guides

- [Backend Trends 2025](./backend/backend_trends_2025.md) - Latest trends and technologies in backend development
- [FastAPI Optimization Guide](./backend/fastapi_optimization_guide.md) - Advanced strategies for optimizing FastAPI applications
- [Database Optimization Guide](./backend/database_optimization_guide.md) - Comprehensive guide to optimizing PostgreSQL databases
- [Testing Best Practices](./backend/testing_best_practices.md) - Best practices for testing backend applications
- [Code Organization Guidelines](./backend/code_organization_guidelines.md) - Guidelines for file size and code organization

## How to Use This Knowledge Base

1. **Find Relevant Guides**: Browse the table of contents to find guides relevant to your current task
2. **Reference During Development**: Use these guides as reference materials during development
3. **Stay Updated**: Check for updates to these guides as new technologies and best practices emerge
4. **Contribute**: If you discover new best practices or techniques, consider contributing to the knowledge base

## Relationship to Development Phases

This knowledge base is designed to support all development phases of the RentUp project:

- **Phase 1-3**: Basic setup and core functionality
- **Phase 4-6**: Advanced features and integrations
- **Phase 7-9**: Optimization, monitoring, and production readiness
- **Phase 10**: Advanced backend optimizations and cutting-edge techniques

Each guide includes information relevant to multiple development phases, with a focus on the latest best practices as of May 2025.

## Latest Technology Stack (May 2025)

### Frontend Stack
- **React 19.1+** with latest concurrent features and server components
- **Vite 6.4+** for ultra-fast build tooling and HMR
- **TypeScript 5.9+** for enhanced type safety and performance
- **Tailwind CSS 4.2+** with new container queries and modern features
- **Testing**: Playwright 1.55+, Jest 29.8+, Testing Library 16+

### Backend Stack
- **FastAPI 0.118+** with improved async performance and validation
- **Python 3.13** with latest language optimizations and features
- **SQLAlchemy 2.1+** with enhanced async support and performance
- **PostgreSQL 17.2+** with advanced indexing and query optimization
- **Redis 7.4+** for high-performance caching and sessions
- **Pydantic 2.13+** for data validation and serialization

### DevOps & Infrastructure (Latest 2025)
- **Docker Compose** (no version spec - latest syntax)
- **Nginx 1.27+** for reverse proxy and load balancing
- **Monitoring**: Prometheus 2.55+, Grafana 11.3+
- **Message Queue**: RabbitMQ 3.13+ with management interface
- **Vector Database**: Qdrant 1.12+ for AI/ML features

### AI/ML Integration
- **PyTorch 2.9+** for machine learning models
- **Transformers 4.55+** for NLP and content moderation
- **Anthropic API 0.35+** for advanced AI features
- **Sentence Transformers 4.3+** for semantic search

## Docker & Containerization

The project now includes a production-ready Docker setup with:
- Multi-stage builds for optimized images
- Health checks for all services
- Environment-specific configurations
- Volume management for data persistence
- Network isolation and security

### Environment Profiles
- **Development**: `docker compose up`
- **Production**: `docker compose --profile production up`
- **With Monitoring**: `docker compose --profile monitoring up`
- **Full Stack**: `docker compose --profile production --profile monitoring up`

Last Updated: May 26, 2025 - Updated with latest Docker configurations and dependency versions
