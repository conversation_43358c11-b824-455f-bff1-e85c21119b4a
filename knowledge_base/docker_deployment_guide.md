# Docker Deployment Guide - RentUP (May 2025)

This guide covers the latest Docker deployment strategies for RentUP using modern Docker Compose syntax and best practices.

## Overview

The RentUP project uses a modern Docker setup with:
- **No version specification** (latest Docker Compose v2 syntax)
- **Multi-environment support** with profiles
- **Health checks** for all services
- **Production-ready configurations**
- **Latest stable images** (May 2025)

## Architecture

### Core Services
- **Frontend**: React 19.1 + Vite 6.4 (Node.js 22 LTS)
- **Backend**: FastAPI 0.118 + Python 3.13
- **Database**: PostgreSQL 17.2 Alpine
- **Cache**: Redis 7.4 Stack
- **Vector DB**: Qdrant 1.12.1
- **Proxy**: Nginx 1.27 Alpine

### Production Services (Profiles)
- **Message Queue**: RabbitMQ 3.13 with Management
- **Monitoring**: Prometheus 2.55 + Grafana 11.3

## Environment Profiles

### Development (Default)
```bash
docker compose up
```
Includes: frontend, backend, db, redis, qdrant, nginx

### Production
```bash
docker compose --profile production up
```
Includes: all development services + rabbitmq

### Monitoring
```bash
docker compose --profile monitoring up
```
Includes: all development services + prometheus + grafana

### Full Production
```bash
docker compose --profile production --profile monitoring up -d
```
Includes: all services for complete production deployment

## Service Configuration

### Frontend Service
- **Image**: Node.js 22 LTS Alpine
- **Port**: 5173 (Vite dev server)
- **Features**: Hot reload, file watching, health checks
- **Volumes**: Source code mounting for development

### Backend Service
- **Image**: Python 3.13 Alpine
- **Port**: 8000 (FastAPI)
- **Features**: Auto-reload, health checks, log management
- **Dependencies**: Database, Redis, Qdrant

### Database Service
- **Image**: PostgreSQL 17.2 Alpine
- **Features**: Optimized configuration, health checks
- **Security**: SCRAM-SHA-256 authentication
- **Performance**: Tuned for development and production

### Redis Service
- **Image**: Redis Stack 7.4.0
- **Features**: RedisInsight web UI, persistence
- **Ports**: 6379 (Redis), 8001 (RedisInsight)

### Qdrant Service
- **Image**: Qdrant 1.12.1
- **Features**: Vector search, REST + gRPC APIs
- **Ports**: 6333 (HTTP), 6334 (gRPC)

## Health Checks

All services include comprehensive health checks:

```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 60s
```

## Volume Management

### Application Volumes
- `frontend_node_modules`: Node.js dependencies
- `frontend_dist`: Built frontend assets
- `backend_uploads`: User uploaded files
- `backend_logs`: Application logs

### Data Volumes
- `postgres_data`: Database data persistence
- `redis_data`: Redis data persistence
- `qdrant_data`: Vector database storage

### Monitoring Volumes
- `prometheus_data`: Metrics storage
- `grafana_data`: Dashboard configurations

## Network Configuration

Custom bridge network with:
- **Subnet**: **********/16
- **Gateway**: **********
- **MTU**: 1500
- **Name**: rentup_network

## Environment Variables

### Required Variables
```bash
# Application
ENVIRONMENT=development
NODE_ENV=development
SECRET_KEY=your-secret-key

# Database
POSTGRES_USER=rentup
POSTGRES_PASSWORD=rentup123
POSTGRES_DB=rentup_db

# Redis
REDIS_PASSWORD=redis123

# External Services
GOOGLE_CLIENT_ID=your-google-client-id
STRIPE_SECRET_KEY=your-stripe-key
```

### Production Variables
```bash
# Security
SECRET_KEY=generate-secure-32-char-key
POSTGRES_PASSWORD=secure-production-password
REDIS_PASSWORD=secure-redis-password

# Monitoring
GRAFANA_PASSWORD=secure-grafana-password
PROMETHEUS_RETENTION=30d
```

## Development Workflow

### Initial Setup
```bash
# Clone repository
git clone <repo-url>
cd rentup

# Copy environment file
cp .env.example .env

# Edit environment variables
nano .env

# Start development environment
docker compose up
```

### Development Commands
```bash
# Start with logs
docker compose up

# Start in background
docker compose up -d

# View logs
docker compose logs -f [service-name]

# Restart service
docker compose restart [service-name]

# Stop all services
docker compose down

# Stop and remove volumes
docker compose down -v
```

## Production Deployment

### Pre-deployment Checklist
- [ ] Update all environment variables
- [ ] Generate secure passwords
- [ ] Configure SSL certificates
- [ ] Set up external monitoring
- [ ] Configure backup strategies

### Deployment Commands
```bash
# Build production images
docker compose --profile production build

# Deploy with monitoring
docker compose --profile production --profile monitoring up -d

# Check service health
docker compose ps

# View production logs
docker compose logs -f --tail=100
```

## Monitoring and Maintenance

### Service Monitoring
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3001
- **RedisInsight**: http://localhost:8001
- **RabbitMQ Management**: http://localhost:15672

### Maintenance Commands
```bash
# Update images
docker compose pull

# Restart with new images
docker compose up -d

# Clean up unused resources
docker system prune -f

# Backup database
docker compose exec db pg_dump -U rentup rentup_db > backup.sql

# Restore database
docker compose exec -T db psql -U rentup rentup_db < backup.sql
```

## Troubleshooting

### Common Issues

1. **Port Conflicts**
   ```bash
   # Check port usage
   netstat -tulpn | grep :5432
   
   # Change ports in .env
   POSTGRES_PORT=5433
   ```

2. **Permission Issues**
   ```bash
   # Fix volume permissions
   sudo chown -R $USER:$USER ./data
   ```

3. **Memory Issues**
   ```bash
   # Increase Docker memory limit
   # Docker Desktop: Settings > Resources > Memory
   ```

4. **Network Issues**
   ```bash
   # Recreate network
   docker compose down
   docker network prune
   docker compose up
   ```

## Security Considerations

### Development Security
- Use strong passwords in .env
- Don't commit .env files
- Limit exposed ports
- Use health checks

### Production Security
- Enable SSL/TLS
- Use secrets management
- Implement rate limiting
- Regular security updates
- Monitor access logs

## Performance Optimization

### Database Optimization
- Tune PostgreSQL configuration
- Use connection pooling
- Implement query optimization
- Regular VACUUM and ANALYZE

### Application Optimization
- Use Redis for caching
- Implement background tasks
- Optimize Docker images
- Use multi-stage builds

## Best Practices

1. **Always use health checks**
2. **Implement proper logging**
3. **Use environment-specific configurations**
4. **Regular backups and testing**
5. **Monitor resource usage**
6. **Keep images updated**
7. **Use least privilege principles**
8. **Document all configurations**

## Version Compatibility

| Component | Minimum Version | Recommended |
|-----------|----------------|-------------|
| Docker | 25.0+ | Latest |
| Docker Compose | 2.20+ | Latest |
| Node.js | 22 LTS | 22 LTS |
| Python | 3.13+ | 3.13 |

Last Updated: May 26, 2025
