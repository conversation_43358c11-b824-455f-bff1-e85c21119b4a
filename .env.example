# =============================================================================
# RentUP Docker Environment Configuration - May 2025
# Copy this file to .env and update the values for your environment
# =============================================================================

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
ENVIRONMENT=development
BUILD_TARGET=development
NODE_ENV=development
PYTHON_VERSION=3.13

# =============================================================================
# FRONTEND SETTINGS
# =============================================================================
VITE_API_URL=http://localhost:8000
VITE_APP_VERSION=1.0.0

# =============================================================================
# BACKEND SETTINGS
# =============================================================================
SECRET_KEY=your-secret-key-change-in-production-use-openssl-rand-hex-32
CORS_ORIGINS=http://localhost:5173,http://localhost:3000
ACCESS_TOKEN_EXPIRE_MINUTES=60
API_V1_STR=/api/v1
API_HOST=0.0.0.0
API_PORT=8000

# =============================================================================
# DATABASE SETTINGS
# =============================================================================
DATABASE_URL=*************************************/rentup_db
POSTGRES_USER=rentup
POSTGRES_PASSWORD=rentup123
POSTGRES_DB=rentup_db
POSTGRES_PORT=5432

# =============================================================================
# REDIS SETTINGS
# =============================================================================
REDIS_URL=redis://redis:6379/0
REDIS_PASSWORD=redis123
REDIS_PORT=6379
REDIS_INSIGHT_PORT=8001

# =============================================================================
# QDRANT VECTOR DATABASE SETTINGS
# =============================================================================
QDRANT_URL=http://qdrant:6333
QDRANT_HOST=qdrant
QDRANT_PORT=6333
QDRANT_GRPC_PORT=6334

# =============================================================================
# RABBITMQ SETTINGS (Production)
# =============================================================================
RABBITMQ_USER=admin
RABBITMQ_PASSWORD=admin123
RABBITMQ_VHOST=/
RABBITMQ_PORT=5672
RABBITMQ_MANAGEMENT_PORT=15672

# =============================================================================
# MONITORING SETTINGS (Production)
# =============================================================================
PROMETHEUS_PORT=9090
PROMETHEUS_RETENTION=15d
GRAFANA_PORT=3001
GRAFANA_USER=admin
GRAFANA_PASSWORD=admin123

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
VITE_GOOGLE_CLIENT_ID=your_google_client_id_here

# Facebook OAuth
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
VITE_FACEBOOK_APP_ID=your_facebook_app_id_here

# Apple OAuth
APPLE_CLIENT_ID=your-apple-client-id
APPLE_TEAM_ID=your-apple-team-id
APPLE_KEY_ID=your-apple-key-id

# Stripe Payment
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key

# AWS Services
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
S3_BUCKET=rentup-uploads

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_specific_password

# =============================================================================
# AI/ML SERVICES
# =============================================================================
# Hugging Face token for downloading models
HF_TOKEN=your_huggingface_token

# Anthropic API for content moderation and user analysis
ANTHROPIC_API_KEY=your_anthropic_api_key
ANTHROPIC_MODEL=claude-3-5-sonnet-20241022

# AI Feature Flags
ENABLE_ADVANCED_CONTENT_MODERATION=false

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Set to true to enable debug mode
DEBUG=true

# Set to true to enable hot reloading
HOT_RELOAD=true

# Set to true to enable verbose logging
VERBOSE_LOGGING=false

# For local development without Docker, use these URLs instead:
# DATABASE_URL=postgresql://rentup:rentup123@localhost:5432/rentup_db
# REDIS_URL=redis://localhost:6379/0
# QDRANT_URL=http://localhost:6333
