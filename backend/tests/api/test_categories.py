import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.models.category import Category
from app.core.database import get_db, Base, engine

client = TestClient(app)


@pytest.fixture(scope="module")
def db():
    # Create the tables
    Base.metadata.create_all(bind=engine)
    
    # Create a connection
    connection = engine.connect()
    
    # Begin a transaction
    transaction = connection.begin()
    
    # Create a session
    session = Session(bind=connection)
    
    # Override the get_db dependency
    app.dependency_overrides[get_db] = lambda: session
    
    # Create test data
    create_test_categories(session)
    
    yield session
    
    # Rollback the transaction
    transaction.rollback()
    
    # Close the connection
    connection.close()
    
    # Remove the override
    app.dependency_overrides.clear()


def create_test_categories(db: Session):
    # Create test categories
    electronics = Category(
        id="electronics",
        name="Electronics",
        description="Electronic devices and gadgets",
        icon="📱",
        is_prohibited=False
    )
    db.add(electronics)
    
    # Create subcategories
    smartphones = Category(
        id="smartphones",
        name="Smartphones",
        description="Mobile phones and accessories",
        icon="📱",
        parent_id="electronics",
        is_prohibited=False
    )
    db.add(smartphones)
    
    laptops = Category(
        id="laptops",
        name="Laptops",
        description="Portable computers",
        icon="💻",
        parent_id="electronics",
        is_prohibited=False
    )
    db.add(laptops)
    
    # Create a prohibited category
    prohibited = Category(
        id="prohibited",
        name="Prohibited Items",
        description="Items that are not allowed",
        icon="⛔",
        is_prohibited=True
    )
    db.add(prohibited)
    
    db.commit()


def test_get_categories(db):
    response = client.get("/api/v1/categories/")
    assert response.status_code == 200
    
    data = response.json()
    assert len(data) > 0
    assert "electronics" in [category["id"] for category in data]
    
    # Check that prohibited categories are not included by default
    assert "prohibited" not in [category["id"] for category in data]


def test_get_categories_with_prohibited(db):
    response = client.get("/api/v1/categories/?include_prohibited=true")
    assert response.status_code == 200
    
    data = response.json()
    assert "prohibited" in [category["id"] for category in data]


def test_get_subcategories(db):
    response = client.get("/api/v1/categories/?parent_id=electronics")
    assert response.status_code == 200
    
    data = response.json()
    assert len(data) > 0
    assert "smartphones" in [category["id"] for category in data]
    assert "laptops" in [category["id"] for category in data]


def test_get_category_by_id(db):
    response = client.get("/api/v1/categories/electronics")
    assert response.status_code == 200
    
    data = response.json()
    assert data["id"] == "electronics"
    assert data["name"] == "Electronics"
    assert len(data["subcategories"]) == 2
    assert "smartphones" in [sub["id"] for sub in data["subcategories"]]
    assert "laptops" in [sub["id"] for sub in data["subcategories"]]


def test_get_prohibited_category(db):
    # Should return 403 when trying to access a prohibited category
    response = client.get("/api/v1/categories/prohibited")
    assert response.status_code == 403
    
    # Should return the category when include_prohibited is true
    response = client.get("/api/v1/categories/prohibited?include_prohibited=true")
    assert response.status_code == 200
    assert response.json()["id"] == "prohibited"


def test_get_category_tree(db):
    response = client.get("/api/v1/categories/tree")
    assert response.status_code == 200
    
    data = response.json()
    assert len(data) > 0
    
    # Find the electronics category
    electronics = next((cat for cat in data if cat["id"] == "electronics"), None)
    assert electronics is not None
    assert len(electronics["subcategories"]) == 2
    
    # Check that prohibited categories are not included by default
    assert not any(cat["id"] == "prohibited" for cat in data)


def test_get_category_tree_with_prohibited(db):
    response = client.get("/api/v1/categories/tree?include_prohibited=true")
    assert response.status_code == 200
    
    data = response.json()
    assert any(cat["id"] == "prohibited" for cat in data)
