import pytest
from unittest.mock import patch

@pytest.fixture
def mock_redis():
    """Mock Redis client for testing"""
    with patch("app.core.redis.redis_client") as mock_client:
        # Configure the mock
        mock_client.exists.return_value = 0  # Token not blacklisted by default
        mock_client.setex.return_value = True
        mock_client.get.return_value = "test-user-id"
        mock_client.sadd.return_value = 1
        mock_client.smembers.return_value = ["token1", "token2"]
        mock_client.srem.return_value = 1
        mock_client.delete.return_value = 1
        mock_client.ping.return_value = True

        yield mock_client
