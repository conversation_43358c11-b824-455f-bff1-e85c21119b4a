import pytest
from unittest.mock import patch, MagicMock
from fastapi.testclient import <PERSON><PERSON>lient
from sqlalchemy.orm import Session

from app.main import app
from app.models.user import User
from app.core.oauth import get_facebook_user_info, get_google_user_info, get_apple_user_info


@pytest.fixture
def mock_db_session(mocker):
    """Mock database session for testing"""
    mock_session = MagicMock(spec=Session)
    
    # Mock the query method to return a query object
    mock_query = MagicMock()
    mock_session.query.return_value = mock_query
    
    # Mock the filter method to return itself
    mock_query.filter.return_value = mock_query
    
    # Mock the first method to return None by default (no user found)
    mock_query.first.return_value = None
    
    return mock_session


@pytest.fixture
def client():
    """Test client for the FastAPI app"""
    return TestClient(app)


@pytest.fixture
def mock_redis(mocker):
    """Mock Redis for testing"""
    mock = MagicMock()
    mocker.patch("app.core.redis.redis_client", mock)
    return mock


def test_social_login_google_success(client, mock_db_session, mock_redis):
    """Test successful Google social login"""
    # Mock the get_db dependency
    app.dependency_overrides = {}
    
    def override_get_db():
        return mock_db_session
    
    app.dependency_overrides["get_db"] = override_get_db
    
    # Mock the get_google_user_info function
    google_user_info = {
        "id": "123456789",
        "email": "<EMAIL>",
        "name": "Test User",
        "picture": "https://example.com/picture.jpg"
    }
    
    # Create a mock user
    mock_user = MagicMock(spec=User)
    mock_user.id = "user-123"
    mock_user.email = "<EMAIL>"
    mock_user.is_admin = False
    
    # Configure the mock_db_session to return the mock user
    mock_db_session.query().filter().first.return_value = mock_user
    
    # Patch the get_google_user_info function
    with patch("app.api.v1.endpoints.auth.get_google_user_info", return_value=google_user_info):
        # Make the request
        response = client.post(
            "/api/v1/auth/social-login",
            json={"provider": "google", "token": "fake-token"}
        )
        
        # Check the response
        assert response.status_code == 200
        assert "access_token" in response.json()
        assert "refresh_token" in response.json()
        assert response.json()["token_type"] == "bearer"
        
        # Verify that the user's last_login was updated
        assert mock_user.last_login is not None


def test_social_login_facebook_success(client, mock_db_session, mock_redis):
    """Test successful Facebook social login"""
    # Mock the get_db dependency
    app.dependency_overrides = {}
    
    def override_get_db():
        return mock_db_session
    
    app.dependency_overrides["get_db"] = override_get_db
    
    # Mock the get_facebook_user_info function
    facebook_user_info = {
        "id": "123456789",
        "email": "<EMAIL>",
        "name": "Test User",
        "picture": {
            "data": {
                "url": "https://example.com/picture.jpg"
            }
        }
    }
    
    # Create a mock user
    mock_user = MagicMock(spec=User)
    mock_user.id = "user-123"
    mock_user.email = "<EMAIL>"
    mock_user.is_admin = False
    
    # Configure the mock_db_session to return the mock user
    mock_db_session.query().filter().first.return_value = mock_user
    
    # Patch the get_facebook_user_info function
    with patch("app.api.v1.endpoints.auth.get_facebook_user_info", return_value=facebook_user_info):
        # Make the request
        response = client.post(
            "/api/v1/auth/social-login",
            json={"provider": "facebook", "token": "fake-token"}
        )
        
        # Check the response
        assert response.status_code == 200
        assert "access_token" in response.json()
        assert "refresh_token" in response.json()
        assert response.json()["token_type"] == "bearer"
        
        # Verify that the user's last_login was updated
        assert mock_user.last_login is not None


def test_social_login_invalid_provider(client):
    """Test social login with invalid provider"""
    # Make the request with an invalid provider
    response = client.post(
        "/api/v1/auth/social-login",
        json={"provider": "invalid", "token": "fake-token"}
    )
    
    # Check the response
    assert response.status_code == 400
    assert "Unsupported provider" in response.json()["detail"]
