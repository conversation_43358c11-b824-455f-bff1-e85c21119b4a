import pytest
import os
import io
from fastapi.testclient import Test<PERSON><PERSON>
from sqlalchemy.orm import Session
from PIL import Image

from app.main import app
from app.core.database import get_db, Base, engine
from app.core.security import create_access_token, get_current_user
from app.models.user import User

client = TestClient(app)


@pytest.fixture(scope="module")
def db():
    # Create the tables
    Base.metadata.create_all(bind=engine)

    # Create a connection
    connection = engine.connect()

    # Begin a transaction
    transaction = connection.begin()

    # Create a session
    session = Session(bind=connection)

    # Override the get_db dependency
    app.dependency_overrides[get_db] = lambda: session

    # Create test user
    test_user = create_test_user(session)

    # Override the get_current_user dependency
    app.dependency_overrides[get_current_user] = lambda: test_user

    yield session

    # Rollback the transaction
    transaction.rollback()

    # Close the connection
    connection.close()

    # Remove the override
    app.dependency_overrides.clear()


def create_test_user(db: Session):
    # Check if the user already exists
    existing_user = db.query(User).filter(User.email == "<EMAIL>").first()
    if existing_user:
        return existing_user

    # Create a test user
    user = User(
        id="test-user-id",
        email="<EMAIL>",
        hashed_password="hashed_password",
        full_name="Test User",
        is_active=True,
        verification_level="none",
        is_email_verified=False,
        is_admin=False,
        risk_score=0.5
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    return user


@pytest.fixture
def token():
    # Create a token for the test user
    return create_access_token(
        subject="test-user-id",
        roles=["user"],
        expires_delta=None
    )


def create_test_image():
    # Create a test image
    image = Image.new('RGB', (100, 100), color='red')
    img_byte_arr = io.BytesIO()
    image.save(img_byte_arr, format='JPEG')
    img_byte_arr.seek(0)
    return img_byte_arr


def test_upload_image_unauthorized():
    # Test uploading an image without authentication
    response = client.post("/api/v1/uploads/images/")
    assert response.status_code == 401


def test_upload_image(db, token, mock_redis):
    # Test uploading an image with authentication
    img_byte_arr = create_test_image()

    response = client.post(
        "/api/v1/uploads/images/",
        files={"files": ("test.jpg", img_byte_arr, "image/jpeg")},
        headers={"Authorization": f"Bearer {token}"}
    )

    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) == 1
    assert response.json()[0].startswith("/uploads/")


def test_upload_multiple_images(db, token, mock_redis):
    # Test uploading multiple images with authentication
    img_byte_arr1 = create_test_image()
    img_byte_arr2 = create_test_image()

    response = client.post(
        "/api/v1/uploads/images/",
        files=[
            ("files", ("test1.jpg", img_byte_arr1, "image/jpeg")),
            ("files", ("test2.jpg", img_byte_arr2, "image/jpeg"))
        ],
        headers={"Authorization": f"Bearer {token}"}
    )

    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) == 2
    assert all(url.startswith("/uploads/") for url in response.json())


def test_upload_invalid_file_type(db, token, mock_redis):
    # Test uploading a file with an invalid type
    response = client.post(
        "/api/v1/uploads/images/",
        files={"files": ("test.txt", b"This is a text file", "text/plain")},
        headers={"Authorization": f"Bearer {token}"}
    )

    assert response.status_code == 400
    assert "File type not allowed" in response.json()["detail"]


def test_upload_empty_file_list(db, token, mock_redis):
    # Test uploading an empty file list
    response = client.post(
        "/api/v1/uploads/images/",
        headers={"Authorization": f"Bearer {token}"}
    )

    assert response.status_code == 422
    # Check for validation error message in the response
    assert "detail" in response.json()
    assert any("files" in field.get("loc", []) for field in response.json().get("detail", []))
