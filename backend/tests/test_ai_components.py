"""
Tests for AI components.

This module contains tests for the AI components of the RentUp platform,
including the recommendation, content moderation, and fraud detection agents.
"""

import pytest
import asyncio
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.ai.models import (
    AIRequest,
    AIResponse,
    ContentModerationRequest,
    ContentModerationResponse,
    FlaggedContent
)
from app.ai.content_moderation.agent import ContentModerationAgent
from app.ai.content_moderation.fallback import FallbackContentModerationAgent
from app.ai.router import AIRouter

# Create test client
client = TestClient(app)


# Fixtures
@pytest.fixture
def db_session():
    """Create a mock database session."""
    return MagicMock(spec=Session)


@pytest.fixture
def content_moderation_agent():
    """Create a ContentModerationAgent instance."""
    return ContentModerationAgent()


@pytest.fixture
def fallback_content_moderation_agent():
    """Create a FallbackContentModerationAgent instance."""
    return FallbackContentModerationAgent()


# Tests for Content Moderation Agent
@pytest.mark.asyncio
async def test_content_moderation_agent_text(content_moderation_agent, db_session):
    """Test the content moderation agent with text content."""
    # Create a request with text content
    request = ContentModerationRequest(
        content_type="text",
        text_content="This is a normal text that should pass moderation.",
        user_id="user123",
        context="item_listing"
    )
    
    # Process the request
    result = await content_moderation_agent.process_text_moderation(
        text=request.text_content,
        context=request.context
    )
    
    # Check the result
    assert result is not None
    assert "toxicity_score" in result
    assert "flagged_segments" in result
    assert isinstance(result["toxicity_score"], float)
    assert 0 <= result["toxicity_score"] <= 1


@pytest.mark.asyncio
async def test_content_moderation_agent_text_with_profanity(content_moderation_agent, db_session):
    """Test the content moderation agent with text containing profanity."""
    # Create a request with text content containing profanity
    request = ContentModerationRequest(
        content_type="text",
        text_content="This product is damn good but the shipping is terrible!",
        user_id="user123",
        context="review"
    )
    
    # Process the request
    result = await content_moderation_agent.process_text_moderation(
        text=request.text_content,
        context=request.context
    )
    
    # Check the result
    assert result is not None
    assert "toxicity_score" in result
    assert "flagged_segments" in result
    assert isinstance(result["toxicity_score"], float)
    assert result["toxicity_score"] > 0  # Should detect some toxicity


@pytest.mark.asyncio
async def test_fallback_content_moderation(fallback_content_moderation_agent, db_session):
    """Test the fallback content moderation agent."""
    # Create a request
    request = ContentModerationRequest(
        content_type="text",
        text_content="This is a test message with some bad words like idiot and stupid.",
        user_id="user123",
        context="user_message"
    )
    
    # Process the request
    result = await fallback_content_moderation_agent.process(request.dict(), db_session)
    
    # Check the result
    assert result is not None
    assert "moderation_result" in result
    assert "flagged_content" in result
    
    # The fallback should detect the bad words
    assert len(result["flagged_content"]) > 0


# Tests for AI Router
@pytest.mark.asyncio
async def test_ai_router_content_moderation():
    """Test the AI router with a content moderation request."""
    # Create a request
    request_data = {
        "request_id": "test-123",
        "request_type": "content_moderation",
        "context": {
            "content_type": "text",
            "text_content": "This is a test message.",
            "user_id": "user123",
            "context": "item_listing"
        }
    }
    
    # Send the request to the API
    response = client.post("/ai/process", json=request_data)
    
    # Check the response
    assert response.status_code == 200
    data = response.json()
    assert data["request_id"] == "test-123"
    assert data["agent_selected"] == "content_moderation"
    assert "result" in data
    assert "metadata" in data
    assert "processing_time_ms" in data["metadata"]


# Tests for AI Health Check
def test_ai_health_check():
    """Test the AI health check endpoint."""
    # Send a request to the health check endpoint
    response = client.get("/ai/health")
    
    # Check the response
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert "agents" in data
    assert "timestamp" in data


# Tests for AI Metrics
def test_ai_metrics():
    """Test the AI metrics endpoint."""
    # Send a request to the metrics endpoint
    response = client.get("/ai/metrics")
    
    # Check the response
    assert response.status_code == 200
    data = response.json()
    assert "metrics" in data
    assert "since" in data
    assert "current_time" in data


# Integration test for the full AI pipeline
@pytest.mark.asyncio
async def test_content_moderation_integration():
    """Test the full content moderation pipeline."""
    # Create a content moderation request
    request_data = {
        "content_type": "text",
        "text_content": "This is a test message for content moderation.",
        "user_id": "user123",
        "context": "item_listing"
    }
    
    # Send the request to the API
    response = client.post("/api/content-moderation", json=request_data)
    
    # Check the response
    assert response.status_code == 200
    data = response.json()
    assert "moderation_result" in data
    assert "flagged_content" in data
    assert "metadata" in data
