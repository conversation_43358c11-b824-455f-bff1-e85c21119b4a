"""Agreement enhancements

Revision ID: 202407_agreement_enhancements
Revises: 202407_auction_enhancements
Create Date: 2024-07-15

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID, JSONB


# revision identifiers, used by Alembic.
revision = '202407_agreement_enhancements'
down_revision = '202407_auction_enhancements'
branch_labels = None
depends_on = None


def upgrade():
    # Add digital_signature_data column to agreements table
    op.add_column('agreements', sa.Column('digital_signature_data', JSONB, nullable=True))
    
    # Add signature_hash column to agreements table
    op.add_column('agreements', sa.Column('owner_signature_hash', sa.String(), nullable=True))
    op.add_column('agreements', sa.Column('renter_signature_hash', sa.String(), nullable=True))
    
    # Add pdf_generated_at column to agreements table
    op.add_column('agreements', sa.Column('pdf_generated_at', sa.DateTime(timezone=True), nullable=True))
    
    # Add pdf_path column to agreements table
    op.add_column('agreements', sa.Column('pdf_path', sa.String(), nullable=True))
    
    # Create clause_library table if it doesn't exist
    op.create_table(
        'clause_library',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('category', sa.String(), nullable=False),
        sa.Column('jurisdiction', sa.String(), nullable=True),
        sa.Column('clause_type', sa.String(), nullable=False),
        sa.Column('clause_content', sa.Text(), nullable=False),
        sa.Column('plain_language_explanation', sa.Text(), nullable=True),
        sa.Column('is_mandatory', sa.Boolean(), nullable=False, server_default='true'),
        sa.Column('version', sa.String(), nullable=False, server_default='1.0'),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Add index on category and jurisdiction
    op.create_index(op.f('ix_clause_library_category'), 'clause_library', ['category'], unique=False)
    op.create_index(op.f('ix_clause_library_jurisdiction'), 'clause_library', ['jurisdiction'], unique=False)
    
    # Add agreement_type column to clause_library table
    op.add_column('clause_library', sa.Column('agreement_type', sa.String(), nullable=True))
    
    # Add index on agreement_type
    op.create_index(op.f('ix_clause_library_agreement_type'), 'clause_library', ['agreement_type'], unique=False)


def downgrade():
    # Drop indexes
    op.drop_index(op.f('ix_clause_library_agreement_type'), table_name='clause_library')
    op.drop_index(op.f('ix_clause_library_jurisdiction'), table_name='clause_library')
    op.drop_index(op.f('ix_clause_library_category'), table_name='clause_library')
    
    # Drop clause_library table
    op.drop_table('clause_library')
    
    # Drop columns from agreements table
    op.drop_column('agreements', 'pdf_path')
    op.drop_column('agreements', 'pdf_generated_at')
    op.drop_column('agreements', 'renter_signature_hash')
    op.drop_column('agreements', 'owner_signature_hash')
    op.drop_column('agreements', 'digital_signature_data')
