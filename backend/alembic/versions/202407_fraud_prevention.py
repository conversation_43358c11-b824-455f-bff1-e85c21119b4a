"""Fraud prevention system

Revision ID: 202407_fraud_prevention
Revises: 202407_agreement_enhancements
Create Date: 2024-07-15

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID, JSONB


# revision identifiers, used by Alembic.
revision = '202407_fraud_prevention'
down_revision = '202407_agreement_enhancements'
branch_labels = None
depends_on = None


def upgrade():
    # Create fraud_alerts table
    op.create_table(
        'fraud_alerts',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=True),
        sa.Column('item_id', sa.String(), nullable=True),
        sa.Column('auction_id', sa.String(), nullable=True),
        sa.Column('rental_id', sa.String(), nullable=True),
        sa.Column('alert_type', sa.Enum(
            'suspicious_user', 
            'suspicious_item', 
            'suspicious_auction', 
            'suspicious_rental',
            'suspicious_login',
            'suspicious_payment',
            'suspicious_behavior',
            name='alert_type'
        ), nullable=False),
        sa.Column('severity', sa.Enum('low', 'medium', 'high', 'critical', name='alert_severity'), 
                  nullable=False, server_default='medium'),
        sa.Column('risk_score', sa.Float(), nullable=False),
        sa.Column('details', JSONB, nullable=True),
        sa.Column('status', sa.Enum('new', 'investigating', 'resolved', 'false_positive', name='alert_status'), 
                  nullable=False, server_default='new'),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.Column('resolved_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('resolved_by', sa.String(), nullable=True),
        sa.Column('resolution_notes', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['auction_id'], ['auctions.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['item_id'], ['items.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['rental_id'], ['rentals.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['resolved_by'], ['users.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Add indexes
    op.create_index(op.f('ix_fraud_alerts_user_id'), 'fraud_alerts', ['user_id'], unique=False)
    op.create_index(op.f('ix_fraud_alerts_item_id'), 'fraud_alerts', ['item_id'], unique=False)
    op.create_index(op.f('ix_fraud_alerts_auction_id'), 'fraud_alerts', ['auction_id'], unique=False)
    op.create_index(op.f('ix_fraud_alerts_rental_id'), 'fraud_alerts', ['rental_id'], unique=False)
    op.create_index(op.f('ix_fraud_alerts_alert_type'), 'fraud_alerts', ['alert_type'], unique=False)
    op.create_index(op.f('ix_fraud_alerts_severity'), 'fraud_alerts', ['severity'], unique=False)
    op.create_index(op.f('ix_fraud_alerts_status'), 'fraud_alerts', ['status'], unique=False)
    
    # Add risk_score column to users table if it doesn't exist
    op.execute("SELECT column_name FROM information_schema.columns WHERE table_name='users' AND column_name='risk_score'")
    result = op.get_bind().fetchone()
    if not result:
        op.add_column('users', sa.Column('risk_score', sa.Float(), nullable=False, server_default='0.5'))


def downgrade():
    # Drop indexes
    op.drop_index(op.f('ix_fraud_alerts_status'), table_name='fraud_alerts')
    op.drop_index(op.f('ix_fraud_alerts_severity'), table_name='fraud_alerts')
    op.drop_index(op.f('ix_fraud_alerts_alert_type'), table_name='fraud_alerts')
    op.drop_index(op.f('ix_fraud_alerts_rental_id'), table_name='fraud_alerts')
    op.drop_index(op.f('ix_fraud_alerts_auction_id'), table_name='fraud_alerts')
    op.drop_index(op.f('ix_fraud_alerts_item_id'), table_name='fraud_alerts')
    op.drop_index(op.f('ix_fraud_alerts_user_id'), table_name='fraud_alerts')
    
    # Drop fraud_alerts table
    op.drop_table('fraud_alerts')
    
    # We'll leave the risk_score column in users table for backward compatibility
