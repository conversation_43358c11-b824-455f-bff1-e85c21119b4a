"""Comprehensive schema update

Revision ID: 202403_comprehensive
Revises: previous_revision
Create Date: 2024-03-XX

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB, UUID

def upgrade():
    # Auction System
    op.create_table(
        'auctions',
        sa.Column('id', UUID(as_uuid=True), primary_key=True),
        sa.Column('item_id', UUID(as_uuid=True), nullable=False),
        sa.Column('start_price', sa.Numeric(10, 2), nullable=False),
        sa.Column('reserve_price', sa.Numeric(10, 2)),
        sa.Column('current_price', sa.Numeric(10, 2)),
        sa.Column('start_time', sa.DateTime(timezone=True), nullable=False),
        sa.Column('end_time', sa.DateTime(timezone=True), nullable=False),
        sa.Column('status', sa.String(20), nullable=False),
        sa.Column('settings', JSONB),
        sa.ForeignKeyConstraint(['item_id'], ['items.id'])
    )

    # Financial Management
    op.create_table(
        'wallets',
        sa.Column('id', UUID(as_uuid=True), primary_key=True),
        sa.Column('user_id', UUID(as_uuid=True), nullable=False),
        sa.Column('balance', sa.Numeric(10, 2), default=0),
        sa.Column('currency', sa.String(3), nullable=False),
        sa.Column('status', sa.String(20), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'])
    )

    # Verification System
    op.create_table(
        'verifications',
        sa.Column('id', UUID(as_uuid=True), primary_key=True),
        sa.Column('user_id', UUID(as_uuid=True), nullable=False),
        sa.Column('type', sa.String(50), nullable=False),
        sa.Column('status', sa.String(20), nullable=False),
        sa.Column('verified_at', sa.DateTime(timezone=True)),
        sa.Column('documents', JSONB),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'])
    )

    # Enhanced User Profiles
    op.add_column('users', sa.Column('trust_score', sa.Float))
    op.add_column('users', sa.Column('verification_level', sa.Integer))
    op.add_column('users', sa.Column('preferences', JSONB))

def downgrade():
    op.drop_table('auctions')
    op.drop_table('wallets')
    op.drop_table('verifications')
    op.drop_column('users', 'trust_score')
    op.drop_column('users', 'verification_level')
    op.drop_column('users', 'preferences')