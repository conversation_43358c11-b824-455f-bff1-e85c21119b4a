"""Auction enhancements

Revision ID: 202407_auction_enhancements
Revises: 202403_comprehensive_update
Create Date: 2024-07-15

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID, JSONB


# revision identifiers, used by Alembic.
revision = '202407_auction_enhancements'
down_revision = '202403_comprehensive_update'
branch_labels = None
depends_on = None


def upgrade():
    # Create auction_configs table
    op.create_table(
        'auction_configs',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('auction_id', sa.String(), nullable=False),
        sa.Column('auction_type', sa.Enum('standard', 'reserve', 'dutch', 'sealed', 'proxy', name='auction_type'), nullable=False, server_default='standard'),
        sa.Column('auto_extend', sa.Boolean(), nullable=False, server_default='true'),
        sa.Column('extend_minutes', sa.Integer(), nullable=False, server_default='2'),
        sa.Column('allow_proxy_bidding', sa.<PERSON>(), nullable=False, server_default='false'),
        sa.Column('allow_auto_bidding', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('bid_increment_strategy', sa.Enum('fixed', 'percentage', 'tiered', name='bid_increment_strategy'), nullable=False, server_default='fixed'),
        sa.Column('bid_increment_tiers', JSONB, nullable=True),
        sa.Column('visibility', sa.Enum('public', 'private', 'invite_only', name='auction_visibility'), nullable=False, server_default='public'),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.ForeignKeyConstraint(['auction_id'], ['auctions.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Add index on auction_id
    op.create_index(op.f('ix_auction_configs_auction_id'), 'auction_configs', ['auction_id'], unique=True)
    
    # Add new columns to bids table
    op.add_column('bids', sa.Column('max_amount', sa.Float(), nullable=True))
    op.add_column('bids', sa.Column('is_proxy', sa.Boolean(), nullable=False, server_default='false'))
    op.add_column('bids', sa.Column('is_auto', sa.Boolean(), nullable=False, server_default='false'))
    op.add_column('bids', sa.Column('bid_type', sa.Enum('manual', 'proxy', 'auto', 'sealed', name='bid_type'), nullable=False, server_default='manual'))
    op.add_column('bids', sa.Column('outbid_at', sa.DateTime(timezone=True), nullable=True))
    
    # Add new status to bid_status enum
    op.execute("ALTER TYPE bid_status ADD VALUE IF NOT EXISTS 'pending'")


def downgrade():
    # Drop new columns from bids table
    op.drop_column('bids', 'max_amount')
    op.drop_column('bids', 'is_proxy')
    op.drop_column('bids', 'is_auto')
    op.drop_column('bids', 'bid_type')
    op.drop_column('bids', 'outbid_at')
    
    # Drop auction_configs table
    op.drop_index(op.f('ix_auction_configs_auction_id'), table_name='auction_configs')
    op.drop_table('auction_configs')
    
    # We can't remove values from enums in PostgreSQL, so we'll leave the 'pending' status
