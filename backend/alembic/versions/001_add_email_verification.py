"""Add is_email_verified column to users table

Revision ID: 001
Revises: 
Create Date: 2023-05-03 05:27:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('users', sa.Column('is_email_verified', sa.<PERSON>(), nullable=False, server_default='false'))


def downgrade():
    op.drop_column('users', 'is_email_verified')
