# FastAPI and dependencies - Latest May 2025
fastapi==0.118.0  # Latest stable version (May 2025)
uvicorn[standard]==0.36.0  # Latest with standard extras (May 2025)
pydantic==2.13.0  # Latest version with improved performance (May 2025)
pydantic-settings==2.11.0  # Latest settings management (May 2025)
email-validator==2.4.0  # Latest email validation (May 2025)

# Database - Latest versions
sqlalchemy==2.1.0  # Latest SQLAlchemy with performance improvements (May 2025)
alembic==1.16.0  # Latest migration tool (May 2025)
psycopg2-binary==2.9.12  # Latest PostgreSQL adapter (May 2025)
asyncpg==0.30.0  # Async PostgreSQL driver for better performance

# Authentication - Latest security
python-jose[cryptography]==3.5.0  # Latest JWT handling
argon2-cffi==24.1.0     # Latest password hashing (May 2025)
passlib[argon2]==1.7.4  # For legacy password hash support with argon2
python-multipart==0.0.22  # Latest multipart form handling (May 2025)
fido2==1.2.0  # Latest WebAuthn/FIDO2 support (May 2025)
qrcode[pil]==8.0.0  # Latest QR code generation with PIL support (May 2025)

# Redis for caching and sessions - Latest
redis==6.1.0  # Latest Redis client (May 2025)
hiredis==3.3.0  # Latest high-performance Redis parser (May 2025)

# Testing - Latest versions
pytest==8.5.0  # Latest testing framework (May 2025)
pytest-asyncio==0.25.0  # Latest async testing support (May 2025)
pytest-mock==3.16.0  # Latest mocking support (May 2025)
pytest-cov==6.0.0  # Latest coverage reporting (May 2025)
httpx==0.30.0  # Latest async HTTP client (May 2025)
tenacity==9.3.0  # Latest retry library (May 2025)

# Utilities - Latest versions
python-dotenv==1.2.0  # Latest environment variable management (May 2025)
loguru==0.9.0  # Latest structured logging (May 2025)
aiofiles==25.1.0  # Latest async file operations (May 2025)
orjson==3.12.0  # Latest high-performance JSON (May 2025)
jinja2==3.2.0  # Latest templating engine (May 2025)
pdfkit==1.1.0  # Latest PDF generation
weasyprint==63.0  # Latest HTML to PDF converter (May 2025)
pillow==11.0.0  # Latest image processing library (May 2025)
brotli==1.2.0  # Latest Brotli compression (May 2025)

# AI/ML - Latest versions
torch==2.9.0  # Latest PyTorch (May 2025)
transformers==4.55.0  # Latest Hugging Face transformers (May 2025)
sentence-transformers==4.3.0  # Latest sentence embeddings (May 2025)
qdrant-client==1.16.0  # Latest vector database client (May 2025)
anthropic==0.35.0  # Latest Anthropic API client (May 2025)

# Cloud Services - Latest versions
boto3==1.40.0  # Latest AWS SDK (May 2025)
google-cloud-storage==2.20.0  # Latest Google Cloud Storage (May 2025)

# Payment processing - Latest
stripe==10.2.0  # Latest Stripe API (May 2025)

# Performance monitoring and profiling - Latest
py-spy==0.5.0  # Latest profiling for Python applications (May 2025)
yappi==1.6.0  # Latest async code profiling (May 2025)
prometheus-client==0.21.0  # Latest metrics collection (May 2025)
locust==2.30.0  # Latest load testing framework (May 2025)
matplotlib==3.10.0  # Latest visualization library (May 2025)
cachetools==5.5.0  # Latest in-memory caching (May 2025)

# Security and validation - Latest
cryptography==43.0.0  # Latest cryptographic library (May 2025)
bcrypt==4.3.0  # Latest password hashing (May 2025)
pyotp==2.10.0  # Latest TOTP/HOTP implementation (May 2025)

# API documentation and validation - Latest
pydantic-extra-types==2.10.0  # Latest extra Pydantic types (May 2025)
typing-extensions==4.15.0  # Latest typing extensions (May 2025)
