"""
JOIN optimization module for RentUp backend.

This module provides comprehensive JOIN operation optimization including:
- JOIN pattern analysis
- JOIN strategy optimization
- Relationship loading optimization
- JOIN performance monitoring
- Index recommendations for JOINs
"""

import logging
from typing import Dict, List, Any, Optional, Tuple, Union, Set
from dataclasses import dataclass
from enum import Enum
import time

from sqlalchemy import text, inspect, MetaData, Table
from sqlalchemy.orm import Query, Session, selectinload, joinedload, subqueryload
from sqlalchemy.sql import Select
from sqlalchemy.engine import Engine

logger = logging.getLogger(__name__)


class JoinStrategy(Enum):
    """Enumeration of JOIN loading strategies."""
    LAZY = "lazy"
    EAGER = "eager"
    SELECTIN = "selectin"
    SUBQUERY = "subquery"
    JOINED = "joined"


class JoinType(Enum):
    """Enumeration of JOIN types."""
    INNER = "inner"
    LEFT = "left"
    RIGHT = "right"
    OUTER = "outer"


@dataclass
class JoinPattern:
    """Represents a JOIN pattern in a query."""
    table_name: str
    join_type: JoinType
    join_condition: str
    estimated_rows: int
    selectivity: float
    cost_estimate: float


@dataclass
class JoinAnalysis:
    """Analysis results for JOIN operations."""
    query_hash: str
    join_patterns: List[JoinPattern]
    recommended_strategy: JoinStrategy
    estimated_improvement: float
    index_recommendations: List[str]
    execution_time: float


def analyze_join_patterns(session: Session, query: Query[Any]) -> List[JoinPattern]:
    """
    Analyze JOIN patterns in a query.

    Args:
        session: Database session
        query: SQLAlchemy query to analyze

    Returns:
        List of JoinPattern objects
    """
    patterns = []

    try:
        # Get query execution plan
        query_str = str(query.statement.compile(compile_kwargs={"literal_binds": True}))
        explain_query = f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {query_str}"

        result = session.execute(text(explain_query))
        plan_data = result.fetchone()[0]

        # Extract JOIN information from plan
        def extract_joins(node, patterns_list):
            node_type = node.get('Node Type', '')

            if 'Join' in node_type:
                # Extract JOIN details
                table_name = node.get('Relation Name', 'unknown')
                join_type_str = node_type.replace(' Join', '').lower()

                # Map node type to JoinType enum
                join_type = JoinType.INNER  # default
                if 'nested loop' in node_type.lower():
                    join_type = JoinType.INNER
                elif 'hash' in node_type.lower():
                    join_type = JoinType.INNER
                elif 'merge' in node_type.lower():
                    join_type = JoinType.INNER

                pattern = JoinPattern(
                    table_name=table_name,
                    join_type=join_type,
                    join_condition=node.get('Join Filter', ''),
                    estimated_rows=node.get('Plan Rows', 0),
                    selectivity=node.get('Plan Rows', 0) / max(node.get('Total Cost', 1), 1),
                    cost_estimate=node.get('Total Cost', 0)
                )
                patterns_list.append(pattern)

            # Recursively process child nodes
            for child in node.get('Plans', []):
                extract_joins(child, patterns_list)

        if plan_data and len(plan_data) > 0:
            extract_joins(plan_data[0]['Plan'], patterns)

    except Exception as e:
        logger.warning(f"Failed to analyze JOIN patterns: {e}")

    return patterns


def get_join_loading_strategy(
    query: Query[Any],
    model_class: type,
    relationship_names: List[str]
) -> JoinStrategy:
    """
    Determine the optimal loading strategy for relationships.

    Args:
        query: SQLAlchemy query
        model_class: Model class being queried
        relationship_names: List of relationship names to optimize

    Returns:
        Recommended JoinStrategy
    """
    # Simple heuristics for strategy selection
    # In a production system, this would be more sophisticated

    if len(relationship_names) == 1:
        # Single relationship - use joined loading
        return JoinStrategy.JOINED
    elif len(relationship_names) <= 3:
        # Few relationships - use selectin loading
        return JoinStrategy.SELECTIN
    else:
        # Many relationships - use subquery loading
        return JoinStrategy.SUBQUERY


def optimize_relationship_loading(
    query: Query[Any],
    model_class: type,
    relationship_names: List[str],
    strategy: Optional[JoinStrategy] = None
) -> Query[Any]:
    """
    Optimize relationship loading for a query.

    Args:
        query: SQLAlchemy query to optimize
        model_class: Model class being queried
        relationship_names: List of relationship names to optimize
        strategy: Optional specific strategy to use

    Returns:
        Optimized query with relationship loading
    """
    if not relationship_names:
        return query

    # Determine strategy if not provided
    if strategy is None:
        strategy = get_join_loading_strategy(query, model_class, relationship_names)

    # Apply the appropriate loading strategy
    optimized_query = query

    try:
        for rel_name in relationship_names:
            if strategy == JoinStrategy.JOINED:
                optimized_query = optimized_query.options(joinedload(getattr(model_class, rel_name)))
            elif strategy == JoinStrategy.SELECTIN:
                optimized_query = optimized_query.options(selectinload(getattr(model_class, rel_name)))
            elif strategy == JoinStrategy.SUBQUERY:
                optimized_query = optimized_query.options(subqueryload(getattr(model_class, rel_name)))
            # LAZY and EAGER are handled by default SQLAlchemy behavior

    except AttributeError as e:
        logger.warning(f"Failed to optimize relationship loading: {e}")
        return query

    return optimized_query


def optimize_join_query(query: Query[Any], session: Session) -> Query[Any]:
    """
    Optimize a query with JOIN operations.

    Args:
        query: SQLAlchemy query to optimize
        session: Database session

    Returns:
        Optimized query
    """
    try:
        # Analyze JOIN patterns
        patterns = analyze_join_patterns(session, query)

        if not patterns:
            return query

        # Apply optimizations based on patterns
        optimized_query = query

        # Log optimization opportunities
        for pattern in patterns:
            if pattern.cost_estimate > 1000:
                logger.info(f"High-cost JOIN detected on {pattern.table_name}: {pattern.cost_estimate}")

            if pattern.selectivity < 0.1:
                logger.info(f"Low selectivity JOIN on {pattern.table_name}: {pattern.selectivity}")

        return optimized_query

    except Exception as e:
        logger.warning(f"JOIN optimization failed: {e}")
        return query


def create_join_indexes(session: Session, patterns: List[JoinPattern]) -> List[str]:
    """
    Create indexes to optimize JOIN operations.

    Args:
        session: Database session
        patterns: List of JOIN patterns to optimize

    Returns:
        List of created index names
    """
    created_indexes = []

    for pattern in patterns:
        if pattern.cost_estimate > 1000 or pattern.selectivity < 0.1:
            # Suggest index creation (implementation would depend on specific schema)
            index_name = f"idx_{pattern.table_name}_join_opt"
            logger.info(f"Recommend creating index: {index_name}")
            created_indexes.append(index_name)

    return created_indexes


def analyze_join_performance(
    session: Session,
    query: Query[Any],
    iterations: int = 5
) -> JoinAnalysis:
    """
    Analyze JOIN performance for a query.

    Args:
        session: Database session
        query: Query to analyze
        iterations: Number of iterations for performance testing

    Returns:
        JoinAnalysis with performance metrics
    """
    import hashlib

    # Generate query hash
    query_str = str(query.statement.compile(compile_kwargs={"literal_binds": True}))
    query_hash = hashlib.md5(query_str.encode()).hexdigest()

    # Analyze JOIN patterns
    patterns = analyze_join_patterns(session, query)

    # Measure execution time
    start_time = time.time()
    for _ in range(iterations):
        try:
            # Execute query without fetching results to measure planning time
            session.execute(query.statement).fetchone()
        except Exception:
            pass
    execution_time = (time.time() - start_time) / iterations

    # Determine recommended strategy
    recommended_strategy = JoinStrategy.SELECTIN  # Default recommendation
    if len(patterns) == 1:
        recommended_strategy = JoinStrategy.JOINED
    elif len(patterns) > 3:
        recommended_strategy = JoinStrategy.SUBQUERY

    # Estimate improvement potential
    estimated_improvement = 0.0
    for pattern in patterns:
        if pattern.cost_estimate > 1000:
            estimated_improvement += 0.2  # 20% improvement potential
        if pattern.selectivity < 0.1:
            estimated_improvement += 0.3  # 30% improvement potential

    # Generate index recommendations
    index_recommendations = []
    for pattern in patterns:
        if pattern.cost_estimate > 500:
            index_recommendations.append(f"CREATE INDEX ON {pattern.table_name} (join_column)")

    return JoinAnalysis(
        query_hash=query_hash,
        join_patterns=patterns,
        recommended_strategy=recommended_strategy,
        estimated_improvement=min(estimated_improvement, 0.8),  # Cap at 80%
        index_recommendations=index_recommendations,
        execution_time=execution_time
    )


# Compatibility functions for backward compatibility
def optimize_joins(query: Query[Any], session: Session) -> Query[Any]:
    """Alias for optimize_join_query for backward compatibility."""
    return optimize_join_query(query, session)
