"""
Database configuration module for RentUp backend.

This module provides centralized database configuration management including:
- Connection pool settings
- Query optimization settings
- Caching configuration
- Monitoring settings
- Performance tuning parameters
"""

import os
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from enum import Enum

# Temporarily comment out to avoid config issues
# from app.core.config import settings
settings = None


class DatabaseEngine(Enum):
    """Supported database engines."""
    POSTGRESQL = "postgresql"
    SQLITE = "sqlite"
    MYSQL = "mysql"


@dataclass
class ConnectionPoolConfig:
    """Connection pool configuration."""
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    pool_recycle: int = 3600
    pool_pre_ping: bool = True
    echo: bool = False
    echo_pool: bool = False


@dataclass
class QueryOptimizationConfig:
    """Query optimization configuration."""
    enabled: bool = True
    slow_query_threshold: float = 1.0  # seconds
    query_plan_cache_enabled: bool = True
    query_plan_cache_size: int = 1000
    auto_explain_enabled: bool = False
    auto_explain_threshold: float = 2.0  # seconds


@dataclass
class CachingConfig:
    """Caching configuration."""
    enabled: bool = True
    default_ttl: int = 300  # 5 minutes
    max_cache_size: int = 1000
    strategy: str = "SMART"  # NONE, SIMPLE, SMART, ADAPTIVE, MULTI_LEVEL
    redis_enabled: bool = False
    redis_url: Optional[str] = None


@dataclass
class MonitoringConfig:
    """Database monitoring configuration."""
    enabled: bool = True
    log_slow_queries: bool = True
    collect_query_stats: bool = True
    performance_schema_enabled: bool = False
    metrics_collection_interval: int = 60  # seconds


@dataclass
class DatabaseConfig:
    """Complete database configuration."""
    engine: DatabaseEngine = DatabaseEngine.POSTGRESQL
    url: Optional[str] = None
    connection_pool: ConnectionPoolConfig = field(default_factory=ConnectionPoolConfig)
    query_optimization: QueryOptimizationConfig = field(default_factory=QueryOptimizationConfig)
    caching: CachingConfig = field(default_factory=CachingConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)

    def __post_init__(self):
        """Initialize configuration from environment variables."""
        if self.url is None:
            self.url = self._build_database_url()

        # Override with environment variables if present
        self._load_from_environment()

    def _build_database_url(self) -> str:
        """Build database URL from settings."""
        # Try environment variable first
        database_url = os.getenv("DATABASE_URL")
        if database_url:
            return database_url

        # Fallback to building from components
        postgres_server = os.getenv("POSTGRES_SERVER", "localhost")
        postgres_user = os.getenv("POSTGRES_USER", "postgres")
        postgres_password = os.getenv("POSTGRES_PASSWORD", "postgres")
        postgres_db = os.getenv("POSTGRES_DB", "rentup")

        if postgres_server:
            return (
                f"postgresql://{postgres_user}:{postgres_password}"
                f"@{postgres_server}/{postgres_db}"
            )

        # Default to SQLite for development
        return "sqlite:///./app.db"

    def _load_from_environment(self):
        """Load configuration from environment variables."""
        # Connection pool settings
        self.connection_pool.pool_size = int(os.getenv("DB_POOL_SIZE", self.connection_pool.pool_size))
        self.connection_pool.max_overflow = int(os.getenv("DB_MAX_OVERFLOW", self.connection_pool.max_overflow))
        self.connection_pool.pool_timeout = int(os.getenv("DB_POOL_TIMEOUT", self.connection_pool.pool_timeout))
        self.connection_pool.pool_recycle = int(os.getenv("DB_POOL_RECYCLE", self.connection_pool.pool_recycle))
        self.connection_pool.echo = os.getenv("DB_ECHO", "false").lower() == "true"

        # Query optimization settings
        self.query_optimization.enabled = os.getenv("QUERY_OPTIMIZATION_ENABLED", "true").lower() == "true"
        self.query_optimization.slow_query_threshold = float(os.getenv("SLOW_QUERY_THRESHOLD", self.query_optimization.slow_query_threshold))

        # Caching settings
        self.caching.enabled = os.getenv("QUERY_CACHING_ENABLED", "true").lower() == "true"
        self.caching.default_ttl = int(os.getenv("QUERY_CACHE_TTL", self.caching.default_ttl))
        self.caching.strategy = os.getenv("QUERY_CACHE_STRATEGY", self.caching.strategy)
        self.caching.redis_enabled = os.getenv("REDIS_CACHE_ENABLED", "false").lower() == "true"
        self.caching.redis_url = os.getenv("REDIS_URL", self.caching.redis_url)

        # Monitoring settings
        self.monitoring.enabled = os.getenv("QUERY_MONITORING_ENABLED", "true").lower() == "true"
        self.monitoring.log_slow_queries = os.getenv("LOG_SLOW_QUERIES", "true").lower() == "true"

    def get_sqlalchemy_config(self) -> Dict[str, Any]:
        """Get SQLAlchemy engine configuration."""
        config = {
            "url": self.url,
            "pool_size": self.connection_pool.pool_size,
            "max_overflow": self.connection_pool.max_overflow,
            "pool_timeout": self.connection_pool.pool_timeout,
            "pool_recycle": self.connection_pool.pool_recycle,
            "pool_pre_ping": self.connection_pool.pool_pre_ping,
            "echo": self.connection_pool.echo,
            "echo_pool": self.connection_pool.echo_pool,
        }

        # Add engine-specific configurations
        if self.engine == DatabaseEngine.POSTGRESQL:
            config.update({
                "connect_args": {
                    "options": "-c timezone=utc",
                    "application_name": "rentup_backend"
                }
            })
        elif self.engine == DatabaseEngine.SQLITE:
            config.update({
                "connect_args": {
                    "check_same_thread": False,
                    "timeout": 20
                }
            })

        return config

    def is_postgresql(self) -> bool:
        """Check if using PostgreSQL."""
        return self.engine == DatabaseEngine.POSTGRESQL or "postgresql" in (self.url or "")

    def is_sqlite(self) -> bool:
        """Check if using SQLite."""
        return self.engine == DatabaseEngine.SQLITE or "sqlite" in (self.url or "")

    def is_mysql(self) -> bool:
        """Check if using MySQL."""
        return self.engine == DatabaseEngine.MYSQL or "mysql" in (self.url or "")


# Global database configuration instance
db_config = DatabaseConfig()


def get_database_config() -> DatabaseConfig:
    """Get the global database configuration."""
    return db_config


def update_database_config(**kwargs) -> None:
    """
    Update database configuration.

    Args:
        **kwargs: Configuration parameters to update
    """
    global db_config

    for key, value in kwargs.items():
        if hasattr(db_config, key):
            setattr(db_config, key, value)
        elif hasattr(db_config.connection_pool, key):
            setattr(db_config.connection_pool, key, value)
        elif hasattr(db_config.query_optimization, key):
            setattr(db_config.query_optimization, key, value)
        elif hasattr(db_config.caching, key):
            setattr(db_config.caching, key, value)
        elif hasattr(db_config.monitoring, key):
            setattr(db_config.monitoring, key, value)


def get_connection_pool_config() -> ConnectionPoolConfig:
    """Get connection pool configuration."""
    return db_config.connection_pool


def get_query_optimization_config() -> QueryOptimizationConfig:
    """Get query optimization configuration."""
    return db_config.query_optimization


def get_caching_config() -> CachingConfig:
    """Get caching configuration."""
    return db_config.caching


def get_monitoring_config() -> MonitoringConfig:
    """Get monitoring configuration."""
    return db_config.monitoring


# Configuration validation
def validate_config() -> List[str]:
    """
    Validate database configuration.

    Returns:
        List of validation errors
    """
    errors = []

    if not db_config.url:
        errors.append("Database URL is not configured")

    if db_config.connection_pool.pool_size <= 0:
        errors.append("Pool size must be greater than 0")

    if db_config.connection_pool.max_overflow < 0:
        errors.append("Max overflow must be non-negative")

    if db_config.query_optimization.slow_query_threshold <= 0:
        errors.append("Slow query threshold must be greater than 0")

    if db_config.caching.default_ttl <= 0:
        errors.append("Cache TTL must be greater than 0")

    return errors


# Environment-specific configurations
def get_development_config() -> DatabaseConfig:
    """Get development database configuration."""
    config = DatabaseConfig()
    config.connection_pool.echo = True
    config.monitoring.enabled = True
    config.query_optimization.auto_explain_enabled = True
    return config


def get_production_config() -> DatabaseConfig:
    """Get production database configuration."""
    config = DatabaseConfig()
    config.connection_pool.pool_size = 20
    config.connection_pool.max_overflow = 40
    config.connection_pool.echo = False
    config.monitoring.enabled = True
    config.caching.enabled = True
    config.caching.redis_enabled = True
    return config


def get_testing_config() -> DatabaseConfig:
    """Get testing database configuration."""
    config = DatabaseConfig()
    config.url = "sqlite:///:memory:"
    config.connection_pool.pool_size = 1
    config.connection_pool.max_overflow = 0
    config.caching.enabled = False
    config.monitoring.enabled = False
    return config
