"""
Enhanced database connection management for RentUp backend.

This module provides advanced database connection management including:
- Connection pooling with monitoring
- Session management with context managers
- Transaction handling with retry logic
- Connection health checks
- Performance monitoring
"""

import logging
import time
from typing import Generator, Optional, Dict, Any
from contextlib import contextmanager
from dataclasses import dataclass, field
from datetime import datetime

from sqlalchemy import create_engine, event
from sqlalchemy.engine import Engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import DisconnectionError, OperationalError

from app.core.db_config import get_database_config
# Temporarily comment out to avoid config issues
# from app.core.database import Base
Base = None

logger = logging.getLogger(__name__)


@dataclass
class ConnectionStats:
    """Connection pool statistics."""
    total_connections: int = 0
    active_connections: int = 0
    idle_connections: int = 0
    checked_out_connections: int = 0
    overflow_connections: int = 0
    invalidated_connections: int = 0
    connection_errors: int = 0
    total_checkouts: int = 0
    total_checkins: int = 0

    def to_dict(self) -> Dict[str, Any]:
        """Convert stats to dictionary."""
        return {
            "total_connections": self.total_connections,
            "active_connections": self.active_connections,
            "idle_connections": self.idle_connections,
            "checked_out_connections": self.checked_out_connections,
            "overflow_connections": self.overflow_connections,
            "invalidated_connections": self.invalidated_connections,
            "connection_errors": self.connection_errors,
            "total_checkouts": self.total_checkouts,
            "total_checkins": self.total_checkins,
        }


class DatabaseConnectionManager:
    """
    Advanced database connection manager with monitoring and health checks.
    """

    def __init__(self):
        """Initialize the connection manager."""
        self.config = get_database_config()
        self._engine: Optional[Engine] = None
        self._session_factory: Optional[sessionmaker] = None
        self._stats = ConnectionStats()
        self._setup_engine()
        self._setup_event_listeners()

    def _setup_engine(self) -> None:
        """Set up the SQLAlchemy engine with optimized configuration."""
        engine_config = self.config.get_sqlalchemy_config()

        # Create engine with connection pooling
        self._engine = create_engine(
            engine_config["url"],
            poolclass=QueuePool,
            pool_size=engine_config["pool_size"],
            max_overflow=engine_config["max_overflow"],
            pool_timeout=engine_config["pool_timeout"],
            pool_recycle=engine_config["pool_recycle"],
            pool_pre_ping=engine_config["pool_pre_ping"],
            echo=engine_config["echo"],
            echo_pool=engine_config["echo_pool"],
            connect_args=engine_config.get("connect_args", {})
        )

        # Create session factory
        self._session_factory = sessionmaker(
            bind=self._engine,
            autocommit=False,
            autoflush=False,
            expire_on_commit=False
        )

        logger.info(f"Database engine initialized: {engine_config['url']}")

    def _setup_event_listeners(self) -> None:
        """Set up SQLAlchemy event listeners for monitoring."""
        if not self._engine:
            return

        @event.listens_for(self._engine, "connect")
        def on_connect(dbapi_connection, connection_record):
            """Handle new database connections."""
            self._stats.total_connections += 1
            logger.debug("New database connection established")

        @event.listens_for(self._engine, "checkout")
        def on_checkout(dbapi_connection, connection_record, connection_proxy):
            """Handle connection checkout from pool."""
            self._stats.total_checkouts += 1
            self._stats.checked_out_connections += 1

        @event.listens_for(self._engine, "checkin")
        def on_checkin(dbapi_connection, connection_record):
            """Handle connection checkin to pool."""
            self._stats.total_checkins += 1
            self._stats.checked_out_connections = max(0, self._stats.checked_out_connections - 1)

        @event.listens_for(self._engine, "invalidate")
        def on_invalidate(dbapi_connection, connection_record, exception):
            """Handle connection invalidation."""
            self._stats.invalidated_connections += 1
            if exception:
                self._stats.connection_errors += 1
                logger.warning(f"Database connection invalidated: {exception}")

        # Query performance monitoring
        if self.config.monitoring.enabled:
            @event.listens_for(self._engine, "before_cursor_execute")
            def before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
                """Record query start time."""
                context._query_start_time = time.time()

            @event.listens_for(self._engine, "after_cursor_execute")
            def after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
                """Log slow queries."""
                if hasattr(context, '_query_start_time'):
                    execution_time = time.time() - context._query_start_time

                    if (self.config.query_optimization.enabled and
                        execution_time > self.config.query_optimization.slow_query_threshold):
                        logger.warning(
                            f"Slow query detected: {execution_time:.3f}s - {statement[:200]}..."
                        )

    @property
    def engine(self) -> Engine:
        """Get the SQLAlchemy engine."""
        if not self._engine:
            self._setup_engine()
        return self._engine

    @property
    def session_factory(self) -> sessionmaker:
        """Get the session factory."""
        if not self._session_factory:
            self._setup_engine()
        return self._session_factory

    def create_session(self) -> Session:
        """Create a new database session."""
        return self.session_factory()

    @contextmanager
    def session_scope(self) -> Generator[Session, None, None]:
        """
        Provide a transactional scope around a series of operations.

        Yields:
            Database session
        """
        session = self.create_session()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            session.close()

    @contextmanager
    def transaction_scope(self, session: Optional[Session] = None) -> Generator[Session, None, None]:
        """
        Provide a transaction scope with automatic rollback on error.

        Args:
            session: Optional existing session to use

        Yields:
            Database session
        """
        if session is not None:
            # Use existing session
            try:
                yield session
            except Exception:
                session.rollback()
                raise
        else:
            # Create new session
            with self.session_scope() as new_session:
                yield new_session

    def health_check(self) -> bool:
        """
        Perform a database health check.

        Returns:
            True if database is healthy, False otherwise
        """
        try:
            with self.session_scope() as session:
                session.execute("SELECT 1")
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False

    def get_pool_status(self) -> Dict[str, Any]:
        """
        Get connection pool status.

        Returns:
            Dictionary with pool statistics
        """
        if not self._engine or not hasattr(self._engine.pool, 'status'):
            return {}

        pool = self._engine.pool

        # Update stats from pool
        self._stats.active_connections = getattr(pool, 'checkedout', 0)
        self._stats.idle_connections = getattr(pool, 'checkedin', 0)
        self._stats.overflow_connections = getattr(pool, 'overflow', 0)

        return {
            "pool_size": getattr(pool, 'size', 0),
            "checked_out": getattr(pool, 'checkedout', 0),
            "checked_in": getattr(pool, 'checkedin', 0),
            "overflow": getattr(pool, 'overflow', 0),
            "invalid": getattr(pool, 'invalid', 0),
            **self._stats.to_dict()
        }

    def close_all_connections(self) -> None:
        """Close all database connections."""
        if self._engine:
            self._engine.dispose()
            logger.info("All database connections closed")


# Global connection manager instance
_connection_manager = DatabaseConnectionManager()


def get_engine() -> Engine:
    """Get the global database engine."""
    return _connection_manager.engine


def get_session_factory() -> sessionmaker:
    """Get the global session factory."""
    return _connection_manager.session_factory


def create_session() -> Session:
    """Create a new database session."""
    return _connection_manager.create_session()


@contextmanager
def session_scope() -> Generator[Session, None, None]:
    """
    Provide a transactional scope around a series of operations.

    Yields:
        Database session
    """
    with _connection_manager.session_scope() as session:
        yield session


@contextmanager
def transaction_scope(session: Optional[Session] = None) -> Generator[Session, None, None]:
    """
    Provide a transaction scope with automatic rollback on error.

    Args:
        session: Optional existing session to use

    Yields:
        Database session
    """
    with _connection_manager.transaction_scope(session) as session:
        yield session


def get_db() -> Generator[Session, None, None]:
    """
    Dependency for FastAPI to get database session.

    Yields:
        Database session
    """
    with session_scope() as session:
        yield session


def get_db_with_transaction() -> Generator[Session, None, None]:
    """
    Dependency for FastAPI to get database session with transaction.

    Yields:
        Database session with transaction
    """
    with transaction_scope() as session:
        yield session


def get_read_only_db() -> Generator[Session, None, None]:
    """
    Dependency for FastAPI to get read-only database session.

    Yields:
        Read-only database session
    """
    # For now, this is the same as regular session
    # In a production system, this could connect to a read replica
    with session_scope() as session:
        yield session


def health_check() -> bool:
    """Perform a database health check."""
    return _connection_manager.health_check()


def get_pool_status() -> Dict[str, Any]:
    """Get connection pool status."""
    return _connection_manager.get_pool_status()


def close_all_connections() -> None:
    """Close all database connections."""
    _connection_manager.close_all_connections()


# Compatibility aliases
engine = get_engine()
SessionLocal = get_session_factory()
