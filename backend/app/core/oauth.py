"""
OAuth configuration and utilities for RentUP Backend
"""
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
import jwt
import secrets


class OAuthConfig:
    """OAuth configuration"""
    
    def __init__(self):
        self.google_client_id = "your-google-client-id"
        self.google_client_secret = "your-google-client-secret"
        self.facebook_app_id = "your-facebook-app-id"
        self.facebook_app_secret = "your-facebook-app-secret"
        self.apple_client_id = "your-apple-client-id"
        self.apple_team_id = "your-apple-team-id"
        self.apple_key_id = "your-apple-key-id"
        self.apple_private_key = "your-apple-private-key"
        self.jwt_secret = "your-jwt-secret-key"
        self.jwt_algorithm = "HS256"
        self.token_expire_minutes = 30


class OAuthProvider:
    """Base OAuth provider"""
    
    def __init__(self, config: OAuthConfig):
        self.config = config
    
    def generate_state(self) -> str:
        """Generate OAuth state parameter"""
        return secrets.token_urlsafe(32)
    
    def verify_state(self, state: str, expected_state: str) -> bool:
        """Verify OAuth state parameter"""
        return state == expected_state
    
    def create_jwt_token(self, user_data: Dict[str, Any]) -> str:
        """Create JWT token for user"""
        payload = {
            "user_id": user_data.get("id"),
            "email": user_data.get("email"),
            "exp": datetime.utcnow() + timedelta(minutes=self.config.token_expire_minutes),
            "iat": datetime.utcnow()
        }
        
        return jwt.encode(payload, self.config.jwt_secret, algorithm=self.config.jwt_algorithm)
    
    def verify_jwt_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify JWT token"""
        try:
            payload = jwt.decode(token, self.config.jwt_secret, algorithms=[self.config.jwt_algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None


class GoogleOAuthProvider(OAuthProvider):
    """Google OAuth provider"""
    
    def get_authorization_url(self, redirect_uri: str, state: str) -> str:
        """Get Google OAuth authorization URL"""
        base_url = "https://accounts.google.com/o/oauth2/auth"
        params = {
            "client_id": self.config.google_client_id,
            "redirect_uri": redirect_uri,
            "scope": "openid email profile",
            "response_type": "code",
            "state": state
        }
        
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        return f"{base_url}?{query_string}"
    
    def exchange_code_for_token(self, code: str, redirect_uri: str) -> Optional[Dict[str, Any]]:
        """Exchange authorization code for access token"""
        # Placeholder implementation
        return {
            "access_token": "mock_access_token",
            "token_type": "Bearer",
            "expires_in": 3600
        }
    
    def get_user_info(self, access_token: str) -> Optional[Dict[str, Any]]:
        """Get user information from Google"""
        # Placeholder implementation
        return {
            "id": "google_user_123",
            "email": "<EMAIL>",
            "name": "Test User",
            "picture": "https://example.com/avatar.jpg"
        }


class FacebookOAuthProvider(OAuthProvider):
    """Facebook OAuth provider"""
    
    def get_authorization_url(self, redirect_uri: str, state: str) -> str:
        """Get Facebook OAuth authorization URL"""
        base_url = "https://www.facebook.com/v18.0/dialog/oauth"
        params = {
            "client_id": self.config.facebook_app_id,
            "redirect_uri": redirect_uri,
            "scope": "email,public_profile",
            "response_type": "code",
            "state": state
        }
        
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        return f"{base_url}?{query_string}"


class AppleOAuthProvider(OAuthProvider):
    """Apple OAuth provider"""
    
    def get_authorization_url(self, redirect_uri: str, state: str) -> str:
        """Get Apple OAuth authorization URL"""
        base_url = "https://appleid.apple.com/auth/authorize"
        params = {
            "client_id": self.config.apple_client_id,
            "redirect_uri": redirect_uri,
            "scope": "name email",
            "response_type": "code",
            "response_mode": "form_post",
            "state": state
        }
        
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        return f"{base_url}?{query_string}"


# Global OAuth configuration and providers
oauth_config = OAuthConfig()
google_provider = GoogleOAuthProvider(oauth_config)
facebook_provider = FacebookOAuthProvider(oauth_config)
apple_provider = AppleOAuthProvider(oauth_config)
