"""
Database index optimization utilities
"""
from enum import Enum
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, ConfigDict


class IndexType(Enum):
    """Types of database indexes"""
    BTREE = "btree"
    HASH = "hash"
    GIN = "gin"
    GIST = "gist"
    PARTIAL = "partial"
    UNIQUE = "unique"
    COMPOSITE = "composite"


class IndexDefinition(BaseModel):
    """Definition of a database index"""
    name: str
    table: str
    columns: List[str]
    index_type: IndexType = IndexType.BTREE
    unique: bool = False
    partial_condition: Optional[str] = None
    include_columns: Optional[List[str]] = None

    model_config = ConfigDict(use_enum_values=True)


class IndexOptimizer:
    """Database index optimization service"""

    def __init__(self):
        self.recommended_indexes = self._get_recommended_indexes()

    def _get_recommended_indexes(self) -> List[IndexDefinition]:
        """Get list of recommended indexes for the application"""
        return [
            # User table indexes
            IndexDefinition(
                name="idx_users_email",
                table="users",
                columns=["email"],
                unique=True
            ),
            IndexDefinition(
                name="idx_users_created_at",
                table="users",
                columns=["created_at"]
            ),

            # Item table indexes
            IndexDefinition(
                name="idx_items_owner_id",
                table="items",
                columns=["owner_id"]
            ),
            IndexDefinition(
                name="idx_items_category",
                table="items",
                columns=["category"]
            ),
            IndexDefinition(
                name="idx_items_location",
                table="items",
                columns=["location"]
            ),
            IndexDefinition(
                name="idx_items_price",
                table="items",
                columns=["price"]
            ),
            IndexDefinition(
                name="idx_items_available",
                table="items",
                columns=["available"],
                partial_condition="available = true"
            ),

            # Composite indexes
            IndexDefinition(
                name="idx_items_category_location",
                table="items",
                columns=["category", "location"],
                index_type=IndexType.COMPOSITE
            ),
            IndexDefinition(
                name="idx_items_owner_available",
                table="items",
                columns=["owner_id", "available"],
                index_type=IndexType.COMPOSITE
            ),
        ]

    def get_index_creation_sql(self, index_def: IndexDefinition) -> str:
        """Generate SQL for creating an index"""
        sql_parts = ["CREATE"]

        if index_def.unique:
            sql_parts.append("UNIQUE")

        sql_parts.append("INDEX")
        sql_parts.append(f'"{index_def.name}"')
        sql_parts.append("ON")
        sql_parts.append(f'"{index_def.table}"')

        if index_def.index_type != IndexType.BTREE:
            sql_parts.append(f"USING {index_def.index_type.value}")

        # Column list
        columns_str = ", ".join(f'"{col}"' for col in index_def.columns)
        sql_parts.append(f"({columns_str})")

        # Include columns (PostgreSQL specific)
        if index_def.include_columns:
            include_str = ", ".join(f'"{col}"' for col in index_def.include_columns)
            sql_parts.append(f"INCLUDE ({include_str})")

        # Partial condition
        if index_def.partial_condition:
            sql_parts.append(f"WHERE {index_def.partial_condition}")

        return " ".join(sql_parts) + ";"

    def get_all_index_creation_sql(self) -> List[str]:
        """Get SQL for creating all recommended indexes"""
        return [self.get_index_creation_sql(idx) for idx in self.recommended_indexes]

    def analyze_query_performance(self, query: str) -> Dict[str, Any]:
        """Analyze query performance (placeholder implementation)"""
        return {
            "query": query,
            "estimated_cost": 100.0,
            "suggested_indexes": [],
            "analysis": "Query analysis not implemented"
        }
