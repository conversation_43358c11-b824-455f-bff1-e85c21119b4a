"""
Query optimization module for RentUp backend.

This module provides comprehensive query optimization functionality including:
- Query performance tracking and monitoring
- Query plan analysis and optimization
- Pagination utilities
- Query execution optimization
- Performance metrics collection
"""

import logging
import time
from typing import Dict, List, Any, Optional, Tuple, Union, TypeVar, Generic
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from functools import wraps
import hashlib
import json

from sqlalchemy import text, inspect, func
from sqlalchemy.orm import Query, Session
from sqlalchemy.engine import Engine
from sqlalchemy.sql import Select
from sqlalchemy.dialects import postgresql

# Type aliases for better type hints
QueryType = Query[Any]
SelectType = Select[Any]

# Type variables for generic typing
M = TypeVar('M')  # Model type
T = TypeVar('T')  # Generic type

logger = logging.getLogger(__name__)


@dataclass
class QueryStats:
    """Statistics for a specific query."""
    query_name: str
    count: int = 0
    total_time: float = 0.0
    min_time: float = float('inf')
    max_time: float = 0.0
    total_rows: int = 0
    first_seen: datetime = field(default_factory=datetime.now)
    last_seen: datetime = field(default_factory=datetime.now)

    @property
    def avg_time(self) -> float:
        """Calculate average execution time."""
        return self.total_time / self.count if self.count > 0 else 0.0

    @property
    def avg_rows(self) -> float:
        """Calculate average number of rows returned."""
        return self.total_rows / self.count if self.count > 0 else 0.0


@dataclass
class QueryAnalysis:
    """Analysis results for a query."""
    query_hash: str
    execution_plan: str
    estimated_cost: float
    execution_time: float
    rows_returned: int
    indexes_used: List[str]
    recommendations: List[str]
    timestamp: datetime = field(default_factory=datetime.now)


class QueryPerformanceTracker:
    """
    Tracks query performance and provides analytics.

    This class monitors database query execution times, collects statistics,
    and identifies slow queries for optimization.
    """

    def __init__(self, slow_query_threshold: float = 1.0):
        """
        Initialize the performance tracker.

        Args:
            slow_query_threshold: Threshold in seconds for considering a query slow
        """
        self.slow_query_threshold = slow_query_threshold
        self.query_stats: Dict[str, QueryStats] = {}
        self._lock = None  # Would use threading.Lock() in production

    def record_query(self, query_name: str, execution_time: float, rows_returned: int = 0) -> None:
        """
        Record a query execution.

        Args:
            query_name: Name or identifier for the query
            execution_time: Time taken to execute the query in seconds
            rows_returned: Number of rows returned by the query
        """
        now = datetime.now()

        if query_name not in self.query_stats:
            self.query_stats[query_name] = QueryStats(
                query_name=query_name,
                first_seen=now
            )

        stats = self.query_stats[query_name]
        stats.count += 1
        stats.total_time += execution_time
        stats.min_time = min(stats.min_time, execution_time)
        stats.max_time = max(stats.max_time, execution_time)
        stats.total_rows += rows_returned
        stats.last_seen = now

        if execution_time > self.slow_query_threshold:
            logger.warning(
                f"Slow query detected: {query_name} took {execution_time:.3f}s "
                f"(threshold: {self.slow_query_threshold}s)"
            )

    def track_query(self, query_name: str, execution_time: float, rows_returned: int = 0) -> None:
        """
        Track a query execution (alias for record_query for backward compatibility).

        Args:
            query_name: Name or identifier for the query
            execution_time: Time taken to execute the query in seconds
            rows_returned: Number of rows returned by the query
        """
        return self.record_query(query_name, execution_time, rows_returned)

    def get_slow_queries(self, threshold_seconds: Optional[float] = None) -> List[QueryStats]:
        """
        Get queries that exceed the slow query threshold.

        Args:
            threshold_seconds: Custom threshold, uses instance default if None

        Returns:
            List of QueryStats for slow queries
        """
        threshold = threshold_seconds or self.slow_query_threshold
        return [
            stats for stats in self.query_stats.values()
            if stats.avg_time > threshold
        ]

    def get_statistics(self) -> Dict[str, Any]:
        """
        Get comprehensive statistics about query performance.

        Returns:
            Dictionary containing performance statistics
        """
        if not self.query_stats:
            return {"total_queries": 0, "total_executions": 0}

        total_executions = sum(stats.count for stats in self.query_stats.values())
        total_time = sum(stats.total_time for stats in self.query_stats.values())

        return {
            "total_queries": len(self.query_stats),
            "total_executions": total_executions,
            "total_time": total_time,
            "avg_time_per_execution": total_time / total_executions if total_executions > 0 else 0,
            "slow_queries_count": len(self.get_slow_queries()),
            "most_frequent_query": max(
                self.query_stats.values(),
                key=lambda s: s.count,
                default=None
            ),
            "slowest_query": max(
                self.query_stats.values(),
                key=lambda s: s.avg_time,
                default=None
            )
        }

    def log_statistics(self) -> None:
        """Log current performance statistics."""
        stats = self.get_statistics()
        logger.info(f"Query Performance Statistics: {json.dumps(stats, indent=2, default=str)}")

    def reset_statistics(self) -> None:
        """Reset all collected statistics."""
        self.query_stats.clear()
        logger.info("Query performance statistics reset")


# Global performance tracker instance
query_tracker = QueryPerformanceTracker()


def track_query_performance(query_name: str):
    """
    Decorator to track query performance.

    Args:
        query_name: Name to identify the query in statistics
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time

                # Try to determine number of rows returned
                rows_returned = 0
                if hasattr(result, '__len__'):
                    try:
                        rows_returned = len(result)
                    except (TypeError, AttributeError):
                        pass
                elif hasattr(result, 'rowcount'):
                    rows_returned = result.rowcount or 0

                query_tracker.record_query(query_name, execution_time, rows_returned)
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                query_tracker.record_query(f"{query_name}_error", execution_time, 0)
                raise
        return wrapper
    return decorator


def analyze_query(query: Union[QueryType, SelectType], session: Session) -> QueryAnalysis:
    """
    Analyze a query and provide optimization recommendations.

    Args:
        query: SQLAlchemy query to analyze
        session: Database session

    Returns:
        QueryAnalysis object with analysis results
    """
    start_time = time.time()

    # Generate query hash for caching
    query_str = str(query.statement.compile(dialect=postgresql.dialect(), compile_kwargs={"literal_binds": True}))
    query_hash = hashlib.md5(query_str.encode()).hexdigest()

    # Get execution plan
    explain_query = f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {query_str}"

    try:
        result = session.execute(text(explain_query))
        plan_data = result.fetchone()[0]
        execution_plan = json.dumps(plan_data, indent=2)

        # Extract metrics from plan
        plan_info = plan_data[0]['Plan']
        estimated_cost = plan_info.get('Total Cost', 0)
        actual_time = plan_info.get('Actual Total Time', 0)
        rows_returned = plan_info.get('Actual Rows', 0)

        # Extract indexes used
        indexes_used = []
        def extract_indexes(node):
            if 'Index Name' in node:
                indexes_used.append(node['Index Name'])
            for child in node.get('Plans', []):
                extract_indexes(child)

        extract_indexes(plan_info)

        # Generate recommendations
        recommendations = []
        if estimated_cost > 1000:
            recommendations.append("High cost query - consider adding indexes")
        if actual_time > 100:  # 100ms
            recommendations.append("Slow execution time - review query structure")
        if not indexes_used:
            recommendations.append("No indexes used - consider adding appropriate indexes")
        if 'Seq Scan' in execution_plan:
            recommendations.append("Sequential scan detected - consider adding indexes")

    except Exception as e:
        logger.warning(f"Failed to analyze query: {e}")
        execution_plan = f"Analysis failed: {e}"
        estimated_cost = 0
        actual_time = time.time() - start_time
        rows_returned = 0
        indexes_used = []
        recommendations = ["Query analysis failed - manual review recommended"]

    return QueryAnalysis(
        query_hash=query_hash,
        execution_plan=execution_plan,
        estimated_cost=estimated_cost,
        execution_time=actual_time / 1000.0,  # Convert to seconds
        rows_returned=rows_returned,
        indexes_used=indexes_used,
        recommendations=recommendations
    )


def optimize_query(query: QueryType, session: Session) -> QueryType:
    """
    Apply optimization techniques to a query.

    Args:
        query: SQLAlchemy query to optimize
        session: Database session

    Returns:
        Optimized query
    """
    # Start with the original query
    optimized_query = query

    try:
        # Analyze the query first
        analysis = analyze_query(query, session)

        # Apply optimizations based on analysis
        if "No indexes used" in analysis.recommendations:
            # Could suggest eager loading or query restructuring
            logger.info(f"Query {analysis.query_hash} could benefit from indexes")

        if analysis.estimated_cost > 1000:
            # For high-cost queries, try to optimize joins
            logger.info(f"High-cost query {analysis.query_hash} detected")

        # Add query hints if using PostgreSQL
        if hasattr(session.bind, 'dialect') and 'postgresql' in str(session.bind.dialect):
            # Could add PostgreSQL-specific optimizations here
            pass

    except Exception as e:
        logger.warning(f"Query optimization failed: {e}")

    return optimized_query


def paginate_query(
    query: QueryType,
    page: int = 1,
    page_size: int = 20,
    count_query: Optional[QueryType] = None
) -> Tuple[QueryType, int]:
    """
    Apply pagination to a query efficiently.

    Args:
        query: SQLAlchemy query to paginate
        page: Page number (1-based)
        page_size: Number of items per page
        count_query: Optional custom count query for better performance

    Returns:
        Tuple of (paginated_query, total_count)
    """
    # Validate inputs
    if page < 1:
        page = 1
    if page_size < 1:
        page_size = 20
    if page_size > 1000:  # Prevent excessive page sizes
        page_size = 1000

    # Get total count efficiently
    if count_query is not None:
        total_count = count_query.scalar() or 0
    else:
        # Use query.count() for simplicity and compatibility
        total_count = query.count()

    # Apply pagination
    offset = (page - 1) * page_size
    paginated_query = query.offset(offset).limit(page_size)

    return paginated_query, total_count


class OptimizedQuery(Generic[M]):
    """
    Wrapper for optimized query execution with caching and performance tracking.
    """

    def __init__(self, query: QueryType, session: Session, model_class: Optional[type] = None):
        """
        Initialize optimized query wrapper.

        Args:
            query: SQLAlchemy query to wrap
            session: Database session
            model_class: Optional model class for type hints
        """
        self.query = query
        self.session = session
        self.model_class = model_class
        self._optimized = False
        self._cached = False
        self._cache_ttl = 300  # 5 minutes default

    def optimize(self) -> 'OptimizedQuery[M]':
        """Apply query optimizations."""
        if not self._optimized:
            self.query = optimize_query(self.query, self.session)
            self._optimized = True
        return self

    def cache(self, ttl: int = 300) -> 'OptimizedQuery[M]':
        """Enable caching for this query."""
        self._cached = True
        self._cache_ttl = ttl
        return self

    def paginate(self, page: int = 1, page_size: int = 20) -> 'OptimizedQuery[M]':
        """Apply pagination to the query."""
        self.query, self._total_count = paginate_query(self.query, page, page_size)
        return self

    def get_page(self) -> Tuple[List[M], int]:
        """Get paginated results with total count."""
        results = self.all()
        return results, getattr(self, '_total_count', len(results))

    @track_query_performance("optimized_query_all")
    def all(self) -> List[M]:
        """Execute query and return all results."""
        return self.query.all()

    @track_query_performance("optimized_query_first")
    def first(self) -> Optional[M]:
        """Execute query and return first result."""
        return self.query.first()

    @track_query_performance("optimized_query_count")
    def count(self) -> int:
        """Get count of results."""
        return self.query.count()


# Compatibility aliases for backward compatibility
JoinOptimizer = OptimizedQuery  # Alias for backward compatibility
optimize_joins = optimize_query  # Alias for backward compatibility
