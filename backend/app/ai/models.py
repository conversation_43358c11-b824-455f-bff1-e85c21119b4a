"""
AI Models for RentUP Backend
"""
from pydantic import BaseModel, ConfigDict
from typing import Optional, List, Dict, Any, Union
from datetime import datetime


class FlaggedContent(BaseModel):
    """Model for flagged content detection"""
    id: Optional[str] = None
    content_type: str  # 'listing', 'message', 'review', etc.
    content_id: str
    content_text: str
    flag_reason: str
    confidence_score: float
    flagged_at: datetime
    reviewed: bool = False
    reviewer_id: Optional[str] = None
    action_taken: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class RecommendationRequest(BaseModel):
    """Model for recommendation requests"""
    user_id: str
    category: Optional[str] = None
    location: Optional[str] = None
    price_range: Optional[tuple[float, float]] = None
    limit: int = 10


class RecommendationResponse(BaseModel):
    """Model for recommendation responses"""
    items: List[Dict[str, Any]]
    confidence_scores: List[float]
    explanation: Optional[str] = None


class AIInsight(BaseModel):
    """Model for AI-generated insights"""
    id: Optional[str] = None
    user_id: str
    insight_type: str  # 'preference', 'trend', 'recommendation'
    title: str
    description: str
    data: Dict[str, Any]
    confidence: float
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)


class ContentModerationRequest(BaseModel):
    """Model for content moderation requests"""
    content_type: str = "text"  # 'text', 'image', 'video'
    text_content: Optional[str] = None
    content: Optional[str] = None  # For backward compatibility
    user_id: Optional[str] = None
    context: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)

    def dict(self, **kwargs: Any) -> Dict[str, Any]:
        """Override dict method for compatibility"""
        data = super().model_dump(**kwargs)
        # Ensure text_content is available as content for backward compatibility
        if self.text_content and not self.content:
            data['content'] = self.text_content
        return data


class ContentModerationResponse(BaseModel):
    """Model for content moderation responses"""
    approved: bool
    risk_score: float
    flags: List[str]
    issues: List[str]
    moderated_at: datetime
    message: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class AIRequest(BaseModel):
    """Model for general AI requests"""
    request_id: str
    request_type: str  # 'content_moderation', 'recommendation', 'insight'
    context: Dict[str, Any]
    user_id: Optional[str] = None
    priority: int = 1

    model_config = ConfigDict(from_attributes=True)


class AIResponse(BaseModel):
    """Model for general AI responses"""
    request_id: str
    agent_selected: str
    result: Dict[str, Any]
    metadata: Dict[str, Any]
    processing_time_ms: float
    success: bool = True
    error_message: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)
