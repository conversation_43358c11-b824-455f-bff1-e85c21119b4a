"""
Text moderation service
"""
import re
from typing import Dict, List, Tuple, Any
from datetime import datetime, timezone


class TextModerationService:
    """Service for moderating text content"""

    def __init__(self):
        self.profanity_patterns = [
            r'\b(damn|hell|crap)\b',  # Mild profanity
            r'\b(shit|fuck|bitch)\b',  # Strong profanity
        ]
        self.spam_patterns = [
            r'(click here|buy now|limited time)',
            r'(free money|get rich quick)',
            r'(urgent|act now|don\'t wait)',
        ]
        self.compiled_profanity = [re.compile(p, re.IGNORECASE) for p in self.profanity_patterns]
        self.compiled_spam = [re.compile(p, re.IGNORECASE) for p in self.spam_patterns]

    def moderate_text(self, text: str) -> Dict[str, Any]:
        """
        Moderate text content

        Args:
            text: Text to moderate

        Returns:
            Moderation result
        """
        if not text or not isinstance(text, str):
            return {
                "approved": True,
                "risk_score": 0.0,
                "flags": [],
                "issues": []
            }

        flags = []
        issues = []
        risk_score = 0.0

        # Check for profanity
        profanity_score, profanity_flags = self._check_profanity(text)
        if profanity_flags:
            flags.extend(profanity_flags)
            issues.append("Contains profanity")
            risk_score += profanity_score

        # Check for spam
        spam_score, spam_flags = self._check_spam(text)
        if spam_flags:
            flags.extend(spam_flags)
            issues.append("Potential spam content")
            risk_score += spam_score

        # Check text quality
        quality_score, quality_flags = self._check_quality(text)
        if quality_flags:
            flags.extend(quality_flags)
            issues.append("Poor text quality")
            risk_score += quality_score

        return {
            "approved": risk_score < 0.5,
            "risk_score": min(risk_score, 1.0),
            "flags": flags,
            "issues": issues,
            "moderated_at": datetime.now(timezone.utc).isoformat()
        }

    def _check_profanity(self, text: str) -> Tuple[float, List[str]]:
        """Check for profanity in text"""
        flags = []
        score = 0.0

        for i, pattern in enumerate(self.compiled_profanity):
            matches = pattern.findall(text)
            if matches:
                flags.append(f"profanity_level_{i+1}")
                score += 0.2 * (i + 1) * len(matches)

        return min(score, 0.8), flags

    def _check_spam(self, text: str) -> Tuple[float, List[str]]:
        """Check for spam patterns in text"""
        flags = []
        score = 0.0

        for i, pattern in enumerate(self.compiled_spam):
            if pattern.search(text):
                flags.append(f"spam_pattern_{i}")
                score += 0.3

        return min(score, 0.6), flags

    def _check_quality(self, text: str) -> Tuple[float, List[str]]:
        """Check text quality"""
        flags = []
        score = 0.0

        # Check for excessive repetition
        words = text.lower().split()
        if len(words) > 5:
            word_counts = {}
            for word in words:
                word_counts[word] = word_counts.get(word, 0) + 1

            max_repetition = max(word_counts.values()) if word_counts else 0
            if max_repetition > len(words) * 0.3:
                flags.append("excessive_repetition")
                score += 0.2

        # Check for excessive caps
        if len(text) > 10:
            caps_ratio = sum(1 for c in text if c.isupper()) / len(text)
            if caps_ratio > 0.7:
                flags.append("excessive_caps")
                score += 0.1

        return score, flags
