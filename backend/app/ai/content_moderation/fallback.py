"""
Fallback Content Moderation Agent for RentUP Backend
"""
import asyncio
from typing import Dict, Any, List
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from .modules.content_checkers import ContentCheckersService


class FallbackContentModerationAgent:
    """Fallback content moderation agent when main AI services are unavailable"""

    def __init__(self):
        self.content_checker = ContentCheckersService()
        self.fallback_keywords = [
            "idiot", "stupid", "dumb", "moron", "fool",
            "hate", "kill", "die", "murder", "violence",
            "scam", "fraud", "fake", "stolen", "illegal"
        ]

    async def process(self, request_data: Dict[str, Any], db_session: Session) -> Dict[str, Any]:
        """
        Process content moderation request using fallback methods

        Args:
            request_data: Request data dictionary
            db_session: Database session

        Returns:
            Moderation result dictionary
        """
        start_time = datetime.now(timezone.utc)

        # Simulate async processing
        await asyncio.sleep(0.005)

        text_content = request_data.get("text_content", request_data.get("content", ""))
        context = request_data.get("context", "unknown")

        # Use content checker service
        risk_score, flags = self.content_checker.check_prohibited_content(text_content)

        # Check for fallback keywords
        flagged_content = self._check_fallback_keywords(text_content)

        # Combine results
        all_flags = flags + [f"keyword_{word}" for word in flagged_content]
        final_risk_score = max(risk_score, len(flagged_content) * 0.2)

        result = {
            "moderation_result": {
                "approved": final_risk_score < 0.5,
                "risk_score": min(final_risk_score, 1.0),
                "flags": all_flags,
                "agent": "fallback_content_moderation",
                "method": "keyword_based",
                "processed_at": start_time.isoformat()
            },
            "flagged_content": [
                {
                    "word": word,
                    "reason": "prohibited_keyword",
                    "severity": "medium"
                }
                for word in flagged_content
            ],
            "metadata": {
                "processing_time_ms": (datetime.now(timezone.utc) - start_time).total_seconds() * 1000,
                "context": context,
                "fallback_used": True
            }
        }

        return result

    def _check_fallback_keywords(self, text: str) -> List[str]:
        """
        Check text for fallback keywords

        Args:
            text: Text to check

        Returns:
            List of flagged keywords found
        """
        if not text:
            return []

        text_lower = text.lower()
        flagged = []

        for keyword in self.fallback_keywords:
            if keyword in text_lower:
                flagged.append(keyword)

        return flagged

    async def health_check(self) -> Dict[str, Any]:
        """Check health of fallback agent"""
        return {
            "status": "healthy",
            "agent": "fallback_content_moderation",
            "available_keywords": len(self.fallback_keywords),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
