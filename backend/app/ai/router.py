"""
AI Router for RentUP Backend
"""
import asyncio
from typing import Dict, Any, Optional
from datetime import datetime, timezone
from .content_moderation.agent import ContentModerationAgent
from .content_moderation.fallback import FallbackContentModerationAgent


class AIRouter:
    """Router for AI requests to appropriate agents"""

    def __init__(self):
        self.content_moderation_agent = ContentModerationAgent()
        self.fallback_agent = FallbackContentModerationAgent()
        self.request_history = []
        self.agent_stats = {
            "content_moderation": {"requests": 0, "success": 0, "errors": 0},
            "fallback": {"requests": 0, "success": 0, "errors": 0}
        }

    async def route_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Route AI request to appropriate agent

        Args:
            request_data: Request data containing request_type and context

        Returns:
            Response from the selected agent
        """
        start_time = datetime.now(timezone.utc)
        request_id = request_data.get("request_id", f"req_{int(start_time.timestamp())}")
        request_type = request_data.get("request_type", "unknown")
        context = request_data.get("context", {})

        try:
            if request_type == "content_moderation":
                agent_name = "content_moderation"
                self.agent_stats[agent_name]["requests"] += 1

                # Extract content from context
                text_content = context.get("text_content", context.get("content", ""))
                content_context = context.get("context", "unknown")

                # Process with content moderation agent
                result = await self.content_moderation_agent.process_text_moderation(
                    text=text_content,
                    context=content_context
                )

                self.agent_stats[agent_name]["success"] += 1

            else:
                # Default to content moderation for unknown types
                agent_name = "content_moderation"
                self.agent_stats[agent_name]["requests"] += 1

                result = {
                    "error": f"Unknown request type: {request_type}",
                    "supported_types": ["content_moderation"],
                    "agent": agent_name
                }

                self.agent_stats[agent_name]["errors"] += 1

            # Calculate processing time
            processing_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000

            # Build response
            response = {
                "request_id": request_id,
                "agent_selected": agent_name,
                "result": result,
                "metadata": {
                    "processing_time_ms": processing_time,
                    "timestamp": start_time.isoformat(),
                    "request_type": request_type
                }
            }

            # Store in history
            self.request_history.append({
                "request_id": request_id,
                "request_type": request_type,
                "agent": agent_name,
                "success": "error" not in result,
                "timestamp": start_time,
                "processing_time_ms": processing_time
            })

            return response

        except Exception as e:
            # Handle errors
            processing_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000

            error_response = {
                "request_id": request_id,
                "agent_selected": "error_handler",
                "result": {
                    "error": str(e),
                    "error_type": type(e).__name__,
                    "agent": "error_handler"
                },
                "metadata": {
                    "processing_time_ms": processing_time,
                    "timestamp": start_time.isoformat(),
                    "request_type": request_type,
                    "error": True
                }
            }

            return error_response

    def get_health_status(self) -> Dict[str, Any]:
        """Get health status of all agents"""
        return {
            "status": "healthy",
            "agents": {
                "content_moderation": "available",
                "fallback": "available"
            },
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "stats": self.agent_stats
        }

    def get_metrics(self) -> Dict[str, Any]:
        """Get metrics for all agents"""
        total_requests = sum(stats["requests"] for stats in self.agent_stats.values())
        total_success = sum(stats["success"] for stats in self.agent_stats.values())
        total_errors = sum(stats["errors"] for stats in self.agent_stats.values())

        return {
            "metrics": {
                "total_requests": total_requests,
                "total_success": total_success,
                "total_errors": total_errors,
                "success_rate": total_success / total_requests if total_requests > 0 else 0.0,
                "agent_stats": self.agent_stats,
                "recent_requests": len([r for r in self.request_history if
                                     (datetime.now(timezone.utc) - r["timestamp"]).total_seconds() < 3600])
            },
            "since": datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0).isoformat(),
            "current_time": datetime.now(timezone.utc).isoformat()
        }
