"""
Chatbot Agent for RentUP Backend
"""
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
import asyncio


class ChatbotAgent:
    """Main chatbot agent for handling user conversations"""
    
    def __init__(self):
        self.conversation_history = {}
        self.default_responses = {
            "greeting": "Hello! I'm here to help you with rentals. What are you looking for?",
            "search": "I can help you search for rental items. What category are you interested in?",
            "help": "I can assist you with finding rentals, booking items, and answering questions about our platform.",
            "goodbye": "Thank you for using RentUP! Have a great day!",
            "default": "I'm here to help with your rental needs. Could you please be more specific?"
        }
    
    async def process_message(self, message: str, user_id: str, session_id: str) -> Dict[str, Any]:
        """
        Process user message and generate response
        
        Args:
            message: User's message
            user_id: User identifier
            session_id: Session identifier
            
        Returns:
            Response dictionary with bot reply and metadata
        """
        # Simulate processing time
        await asyncio.sleep(0.1)
        
        # Store message in conversation history
        if session_id not in self.conversation_history:
            self.conversation_history[session_id] = []
        
        self.conversation_history[session_id].append({
            "type": "user",
            "message": message,
            "timestamp": datetime.now(timezone.utc),
            "user_id": user_id
        })
        
        # Generate response based on message content
        response = self._generate_response(message.lower())
        
        # Store bot response
        self.conversation_history[session_id].append({
            "type": "bot",
            "message": response,
            "timestamp": datetime.now(timezone.utc)
        })
        
        return {
            "response": response,
            "session_id": session_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "confidence": self._calculate_confidence(message),
            "intent": self._detect_intent(message),
            "metadata": {
                "message_count": len(self.conversation_history[session_id]),
                "user_id": user_id
            }
        }
    
    def _generate_response(self, message: str) -> str:
        """Generate response based on message content"""
        message = message.lower().strip()
        
        # Check for greetings
        if any(word in message for word in ["hello", "hi", "hey", "good morning", "good afternoon"]):
            return self.default_responses["greeting"]
        
        # Check for search intent
        if any(word in message for word in ["search", "find", "looking for", "need", "want"]):
            return self.default_responses["search"]
        
        # Check for help request
        if any(word in message for word in ["help", "assist", "support", "how"]):
            return self.default_responses["help"]
        
        # Check for goodbye
        if any(word in message for word in ["bye", "goodbye", "see you", "thanks", "thank you"]):
            return self.default_responses["goodbye"]
        
        # Default response
        return self.default_responses["default"]
    
    def _detect_intent(self, message: str) -> str:
        """Detect user intent from message"""
        message = message.lower()
        
        if any(word in message for word in ["hello", "hi", "hey"]):
            return "greeting"
        elif any(word in message for word in ["search", "find", "looking"]):
            return "search"
        elif any(word in message for word in ["help", "assist", "support"]):
            return "help"
        elif any(word in message for word in ["bye", "goodbye", "thanks"]):
            return "goodbye"
        else:
            return "general"
    
    def _calculate_confidence(self, message: str) -> float:
        """Calculate confidence score for response"""
        # Simple confidence calculation based on message length and keywords
        confidence = 0.5
        
        if len(message.split()) > 3:
            confidence += 0.2
        
        keywords = ["rent", "rental", "book", "reserve", "item", "product"]
        if any(keyword in message.lower() for keyword in keywords):
            confidence += 0.3
        
        return min(confidence, 1.0)
    
    def get_conversation_history(self, session_id: str) -> List[Dict[str, Any]]:
        """Get conversation history for a session"""
        return self.conversation_history.get(session_id, [])
    
    def clear_session(self, session_id: str) -> bool:
        """Clear conversation history for a session"""
        if session_id in self.conversation_history:
            del self.conversation_history[session_id]
            return True
        return False
    
    def get_session_stats(self, session_id: str) -> Dict[str, Any]:
        """Get statistics for a conversation session"""
        history = self.conversation_history.get(session_id, [])
        
        user_messages = [msg for msg in history if msg["type"] == "user"]
        bot_messages = [msg for msg in history if msg["type"] == "bot"]
        
        return {
            "session_id": session_id,
            "total_messages": len(history),
            "user_messages": len(user_messages),
            "bot_messages": len(bot_messages),
            "session_duration": self._calculate_session_duration(history),
            "last_activity": history[-1]["timestamp"].isoformat() if history else None
        }
    
    def _calculate_session_duration(self, history: List[Dict[str, Any]]) -> Optional[float]:
        """Calculate session duration in seconds"""
        if len(history) < 2:
            return None
        
        start_time = history[0]["timestamp"]
        end_time = history[-1]["timestamp"]
        
        return (end_time - start_time).total_seconds()
