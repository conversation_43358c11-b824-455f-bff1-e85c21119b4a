#!/usr/bin/env python3
"""
Script to optimize database indexes for the RentUp application.

This script adds indexes to frequently queried fields to improve query performance.
"""

import logging
from sqlalchemy import create_engine, text
from tenacity import retry, stop_after_attempt, wait_exponential

import sys
import os

# Add the parent directory to the path so we can import app modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from app.core.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
def create_db_engine():
    """Create a database engine with retry logic."""
    # Fix for escaped URL
    db_url = settings.DATABASE_URL
    if '\\x3a' in db_url:
        db_url = db_url.replace('\\x3a', ':')

    return create_engine(
        db_url,
        pool_pre_ping=True,
        pool_size=settings.DB_POOL_SIZE,
        max_overflow=settings.DB_MAX_OVERFLOW,
        pool_timeout=settings.DB_POOL_TIMEOUT
    )


def add_indexes():
    """Add indexes to frequently queried fields."""
    engine = create_db_engine()

    with engine.connect() as conn:
        try:
            # Start a transaction
            trans = conn.begin()

            # Items table indexes
            logger.info("Adding indexes to items table...")
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_items_category ON items (category)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_items_subcategory ON items (subcategory)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_items_location ON items (location)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_items_is_available ON items (is_available)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_items_owner_id ON items (owner_id)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_items_created_at ON items (created_at)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_items_daily_price ON items (daily_price)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_items_is_featured ON items (is_featured)"))

            # Auctions table indexes
            logger.info("Adding indexes to auctions table...")
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_auctions_status ON auctions (status)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_auctions_item_id ON auctions (item_id)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_auctions_end_time ON auctions (end_time)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_auctions_start_time ON auctions (start_time)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_auctions_owner_id ON auctions (owner_id)"))

            # Rentals table indexes
            logger.info("Adding indexes to rentals table...")
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_rentals_status ON rentals (status)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_rentals_item_id ON rentals (item_id)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_rentals_renter_id ON rentals (renter_id)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_rentals_owner_id ON rentals (owner_id)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_rentals_start_date ON rentals (start_date)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_rentals_end_date ON rentals (end_date)"))

            # Bids table indexes
            logger.info("Adding indexes to bids table...")
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_bids_auction_id ON bids (auction_id)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_bids_bidder_id ON bids (bidder_id)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_bids_status ON bids (status)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_bids_amount ON bids (amount)"))

            # Agreements table indexes
            logger.info("Adding indexes to agreements table...")
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_agreements_rental_id ON agreements (rental_id)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_agreements_owner_id ON agreements (owner_id)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_agreements_renter_id ON agreements (renter_id)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_agreements_status ON agreements (status)"))

            # Fraud alerts table indexes
            logger.info("Adding indexes to fraud_alerts table...")
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_fraud_alerts_user_id ON fraud_alerts (user_id)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_fraud_alerts_item_id ON fraud_alerts (item_id)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_fraud_alerts_auction_id ON fraud_alerts (auction_id)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_fraud_alerts_status ON fraud_alerts (status)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_fraud_alerts_severity ON fraud_alerts (severity)"))

            # Composite indexes for common query patterns
            logger.info("Adding composite indexes for common query patterns...")
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_items_category_available ON items (category, is_available)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_items_location_available ON items (location, is_available)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_auctions_status_end_time ON auctions (status, end_time)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_rentals_status_dates ON rentals (status, start_date, end_date)"))

            # Commit the transaction
            trans.commit()
            logger.info("All indexes added successfully!")
        except Exception as e:
            # Rollback the transaction in case of error
            trans.rollback()
            logger.error(f"Error adding indexes: {e}")
            raise


if __name__ == "__main__":
    logger.info("Starting database index optimization...")
    add_indexes()
    logger.info("Database index optimization completed!")
