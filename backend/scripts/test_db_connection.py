#!/usr/bin/env python3
"""
Test script to verify database connection.

This script tests the connection to the PostgreSQL database and
performs basic queries to verify that the database is working correctly.
"""

import sys
from pathlib import Path
import argparse
import logging

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import text
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import from our modules
try:
    from app.core.db_config import db_config
    from app.core.database_connection import get_engine
    engine = get_engine()
    NEW_MODULES = True
    print("✅ Using new optimization modules")
except ImportError as e:
    # Fall back to old modules
    print(f"⚠️ Falling back to old modules: {e}")
    from app.core.config import settings
    from app.core.database import engine
    NEW_MODULES = False
    # Override the DATABASE_URL for this script
    settings.DATABASE_URL = "postgresql://rentup:rentup_secure_password@localhost:5432/rentup"

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Test database connection')
    parser.add_argument('--host', help='Database host')
    parser.add_argument('--port', type=int, help='Database port')
    parser.add_argument('--user', help='Database user')
    parser.add_argument('--password', help='Database password')
    parser.add_argument('--db', help='Database name')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    return parser.parse_args()

def override_config(args):
    """Override database configuration with command line arguments."""
    if not NEW_MODULES:
        logger.warning("Using old configuration modules. Command line arguments will be ignored.")
        return

    # For the new modules, we would need to update environment variables
    # or create a new configuration. For now, just log the attempt.
    if args.host or args.port or args.user or args.password or args.db:
        logger.info("Command line database configuration override requested.")
        logger.info("Note: Configuration override not implemented for new modules yet.")

def test_connection():
    """Test the database connection."""
    try:
        # Try to connect and execute a simple query
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            logger.info(f"Connection successful! Result: {result.scalar()}")

            # Get the number of users
            result = conn.execute(text("SELECT COUNT(*) FROM users"))
            logger.info(f"Number of users: {result.scalar()}")

            # Get the number of items
            result = conn.execute(text("SELECT COUNT(*) FROM items"))
            logger.info(f"Number of items: {result.scalar()}")

            # Get the number of rentals
            result = conn.execute(text("SELECT COUNT(*) FROM rentals"))
            logger.info(f"Number of rentals: {result.scalar()}")

            # Get the number of auctions
            result = conn.execute(text("SELECT COUNT(*) FROM auctions"))
            logger.info(f"Number of auctions: {result.scalar()}")

            # Get the number of bids
            result = conn.execute(text("SELECT COUNT(*) FROM bids"))
            logger.info(f"Number of bids: {result.scalar()}")

            # Get the number of agreements
            result = conn.execute(text("SELECT COUNT(*) FROM agreements"))
            logger.info(f"Number of agreements: {result.scalar()}")

            # Get the number of fraud alerts
            result = conn.execute(text("SELECT COUNT(*) FROM fraud_alerts"))
            logger.info(f"Number of fraud alerts: {result.scalar()}")

            logger.info("All queries executed successfully!")
            return True

    except Exception as e:
        logger.error(f"Error connecting to the database: {e}")
        return False

def test_database_version():
    """Test getting database version."""
    logger.info("Testing database version query...")

    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT version()"))
            version = result.scalar()
            logger.info(f"Database version: {version}")
            return True
    except Exception as e:
        logger.error(f"Error getting database version: {str(e)}")
        return False

def main():
    """Main function."""
    args = parse_args()

    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Override configuration if needed
    override_config(args)

    # Log configuration
    if NEW_MODULES:
        logger.info(f"Using database URL: {db_config.url}")
    else:
        logger.info(f"Using database URL: {settings.DATABASE_URL}")

    # Run tests
    connection_ok = test_connection()

    if connection_ok:
        version_ok = test_database_version()

        if version_ok:
            logger.info("✅ All database tests passed")
            return 0

    logger.error("❌ Some database tests failed")
    return 1

if __name__ == "__main__":
    sys.exit(main())
