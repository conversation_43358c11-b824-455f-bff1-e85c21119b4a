#!/usr/bin/env python3
"""
Test script to verify API endpoints.
"""
import requests
import json
import logging
import argparse
from tabulate import tabulate

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# API base URL
BASE_URL = "http://localhost:8092"

def test_health_endpoint():
    """Test health endpoint."""
    try:
        url = f"{BASE_URL}/health"
        # Add timeout to prevent hanging on unresponsive servers
        response = requests.get(url, timeout=10)

        if response.status_code == 200:
            logger.info(f"Health endpoint: {response.status_code} {response.reason}")
            logger.info(f"Response: {json.dumps(response.json(), indent=2)}")
            return True
        else:
            logger.error(f"Health endpoint failed: {response.status_code} {response.reason}")
            return False
    except Exception as e:
        logger.error(f"Health endpoint test failed: {str(e)}")
        return False

def test_api_endpoints():
    """Test API endpoints."""
    endpoints = [
        "/api/v1/users",
        "/api/v1/items",
        "/api/v1/rentals",
        "/api/v1/auctions",
        "/api/v1/agreements"
    ]

    results = []

    for endpoint in endpoints:
        try:
            url = f"{BASE_URL}{endpoint}"
            # Add timeout to prevent hanging on unresponsive servers
            response = requests.get(url, timeout=10)

            status = "✅" if response.status_code == 200 else "❌"
            results.append([endpoint, response.status_code, response.reason, status])

        except Exception as e:
            results.append([endpoint, "Error", str(e), "❌"])

    # Print results as table
    headers = ["Endpoint", "Status Code", "Reason", "Result"]
    logger.info("\nAPI Endpoints Test Results:")
    logger.info(tabulate(results, headers=headers, tablefmt="grid"))

    # Check if all endpoints are successful
    return all(result[3] == "✅" for result in results)

def test_auth_endpoints():
    """Test authentication endpoints."""
    try:
        # Test login endpoint
        url = f"{BASE_URL}/api/v1/auth/login"
        data = {
            "username": "<EMAIL>",
            "password": "password"
        }
        # Add timeout to prevent hanging on unresponsive servers
        response = requests.post(url, data=data, timeout=10)

        if response.status_code == 200:
            logger.info(f"Login endpoint: {response.status_code} {response.reason}")
            token = response.json().get("access_token")
            logger.info(f"Access token: {token[:20]}...")

            # Test authenticated endpoint
            url = f"{BASE_URL}/api/v1/users/me"
            headers = {"Authorization": f"Bearer {token}"}
            # Add timeout to prevent hanging on unresponsive servers
            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code == 200:
                logger.info(f"Authenticated endpoint: {response.status_code} {response.reason}")
                logger.info(f"User: {response.json().get('email')}")
                return True
            else:
                logger.error(f"Authenticated endpoint failed: {response.status_code} {response.reason}")
                return False
        else:
            logger.error(f"Login endpoint failed: {response.status_code} {response.reason}")
            logger.error(f"Response: {response.text}")
            return False
    except Exception as e:
        logger.error(f"Auth endpoints test failed: {str(e)}")
        return False

def test_items_endpoint():
    """Test items endpoint."""
    try:
        url = f"{BASE_URL}/api/v1/items"
        # Add timeout to prevent hanging on unresponsive servers
        response = requests.get(url, timeout=10)

        if response.status_code == 200:
            items = response.json()
            logger.info(f"Items endpoint: {response.status_code} {response.reason}")
            logger.info(f"Found {len(items)} items")

            # Print first 3 items
            if items:
                logger.info("\nSample Items:")
                for i, item in enumerate(items[:3]):
                    logger.info(f"  {i+1}. {item.get('name')} - ${item.get('daily_price')}/day")

            return True
        else:
            logger.error(f"Items endpoint failed: {response.status_code} {response.reason}")
            return False
    except Exception as e:
        logger.error(f"Items endpoint test failed: {str(e)}")
        return False

def test_ai_endpoints():
    """Test AI endpoints."""
    endpoints = [
        "/api/v1/ai/recommendations",
        "/api/v1/ai/content-moderation",
        "/api/v1/ai/fraud-detection",
        "/api/v1/ai/pricing"
    ]

    results = []

    for endpoint in endpoints:
        try:
            url = f"{BASE_URL}{endpoint}"
            # Add timeout to prevent hanging on unresponsive servers
            response = requests.get(url, timeout=10)

            status = "✅" if response.status_code in [200, 404, 405] else "❌"
            results.append([endpoint, response.status_code, response.reason, status])

        except Exception as e:
            results.append([endpoint, "Error", str(e), "❌"])

    # Print results as table
    headers = ["AI Endpoint", "Status Code", "Reason", "Result"]
    logger.info("\nAI Endpoints Test Results:")
    logger.info(tabulate(results, headers=headers, tablefmt="grid"))

    # For AI endpoints, we consider 404 and 405 as acceptable since they might require POST
    return all(result[3] == "✅" for result in results)

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test API endpoints")
    parser.add_argument("--health", action="store_true", help="Test health endpoint")
    parser.add_argument("--api", action="store_true", help="Test API endpoints")
    parser.add_argument("--auth", action="store_true", help="Test authentication endpoints")
    parser.add_argument("--items", action="store_true", help="Test items endpoint")
    parser.add_argument("--ai", action="store_true", help="Test AI endpoints")
    parser.add_argument("--all", action="store_true", help="Test all endpoints")

    args = parser.parse_args()

    # If no arguments are provided, test all endpoints
    if not any(vars(args).values()):
        args.health = True

    logger.info("Testing API endpoints...")

    # Test health endpoint
    if args.health or args.all:
        if not test_health_endpoint():
            logger.error("Health endpoint test failed.")
            return

    # Test API endpoints
    if args.api or args.all:
        if not test_api_endpoints():
            logger.error("API endpoints test failed.")
            return

    # Test authentication endpoints
    if args.auth or args.all:
        if not test_auth_endpoints():
            logger.error("Authentication endpoints test failed.")
            return

    # Test items endpoint
    if args.items or args.all:
        if not test_items_endpoint():
            logger.error("Items endpoint test failed.")
            return

    # Test AI endpoints
    if args.ai or args.all:
        if not test_ai_endpoints():
            logger.error("AI endpoints test failed.")
            return

    logger.info("All API tests completed!")

if __name__ == "__main__":
    main()
