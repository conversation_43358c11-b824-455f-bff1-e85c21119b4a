#!/usr/bin/env python3
"""
Script to migrate data from SQLite to PostgreSQL.

This script extracts data from a SQLite database and imports it into PostgreSQL.
It handles schema differences and data type conversions as needed.
"""
import sys
from pathlib import Path
import logging
import json
import argparse
import sqlite3
import psycopg2
from psycopg2.extras import execute_values

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import the settings module
from app.core.config import settings

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Table definitions with their primary keys and foreign keys
TABLE_DEFINITIONS = {
    "users": {
        "primary_key": "id",
        "order": 1,
        "dependencies": []
    },
    "items": {
        "primary_key": "id",
        "order": 2,
        "dependencies": ["users"]
    },
    "rentals": {
        "primary_key": "id",
        "order": 3,
        "dependencies": ["users", "items"]
    },
    "auctions": {
        "primary_key": "id",
        "order": 4,
        "dependencies": ["users", "items"]
    },
    "bids": {
        "primary_key": "id",
        "order": 5,
        "dependencies": ["users", "auctions"]
    },
    "agreements": {
        "primary_key": "id",
        "order": 6,
        "dependencies": ["users", "rentals"]
    },
    "fraud_alerts": {
        "primary_key": "id",
        "order": 7,
        "dependencies": ["users", "items", "auctions"]
    },
    "moderation_alerts": {
        "primary_key": "id",
        "order": 8,
        "dependencies": ["users", "items"]
    }
}

def connect_to_sqlite(sqlite_path):
    """Connect to SQLite database."""
    try:
        conn = sqlite3.connect(sqlite_path)
        conn.row_factory = sqlite3.Row
        logger.info(f"Connected to SQLite database at {sqlite_path}")
        return conn
    except sqlite3.Error as e:
        logger.error(f"Error connecting to SQLite database: {e}")
        raise

def connect_to_postgres():
    """Connect to PostgreSQL database."""
    try:
        conn = psycopg2.connect(
            host=settings.POSTGRES_SERVER,
            port=settings.POSTGRES_PORT,
            database=settings.POSTGRES_DB,
            user=settings.POSTGRES_USER,
            password=settings.POSTGRES_PASSWORD
        )
        logger.info(f"Connected to PostgreSQL database at {settings.POSTGRES_SERVER}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}")
        return conn
    except psycopg2.Error as e:
        logger.error(f"Error connecting to PostgreSQL database: {e}")
        raise

def get_sqlite_tables(sqlite_conn):
    """Get list of tables in SQLite database."""
    cursor = sqlite_conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
    tables = [row[0] for row in cursor.fetchall()]
    cursor.close()
    return tables

def get_table_columns(sqlite_conn, table_name):
    """Get column names for a table in SQLite database."""
    cursor = sqlite_conn.cursor()
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = [row[1] for row in cursor.fetchall()]
    cursor.close()
    return columns

def get_table_data(sqlite_conn, table_name, columns):
    """Get all data from a table in SQLite database."""
    cursor = sqlite_conn.cursor()
    # Use parameterized query to prevent SQL injection
    # SQLite doesn't support parameterizing table names, but this is still safe
    # as table_name comes from a predefined list, not user input
    query = f"SELECT {', '.join(columns)} FROM {table_name}"  # nosec
    cursor.execute(query)
    rows = cursor.fetchall()
    cursor.close()
    return rows

def prepare_data_for_postgres(rows, columns):
    """
    Prepare data for PostgreSQL insertion, handling data type conversions.

    Args:
        rows: List of SQLite rows
        columns: List of column names

    Returns:
        List of tuples ready for PostgreSQL insertion
    """
    prepared_data = []

    for row in rows:
        # Convert SQLite row to dictionary
        row_dict = dict(zip(columns, row))

        # Handle JSON fields (stored as text in SQLite)
        for key, value in row_dict.items():
            if key in ['images', 'attributes', 'details'] and value:
                try:
                    # If it's already a string representation of JSON, parse it
                    if isinstance(value, str):
                        row_dict[key] = json.loads(value)
                    # If it's already a dict, keep it as is
                except (json.JSONDecodeError, TypeError):
                    # If it's not valid JSON, keep it as is
                    pass

        # Convert dictionary back to tuple in the same order as columns
        prepared_row = tuple(row_dict[col] for col in columns)
        prepared_data.append(prepared_row)

    return prepared_data

def insert_data_to_postgres(pg_conn, table_name, columns, data):
    """
    Insert data into PostgreSQL table using execute_values for efficiency.

    Args:
        pg_conn: PostgreSQL connection
        table_name: Name of the table
        columns: List of column names
        data: List of data tuples

    Returns:
        Number of rows inserted
    """
    cursor = pg_conn.cursor()

    try:
        # Construct the SQL query
        # PostgreSQL doesn't support parameterizing table names with execute_values,
        # but this is still safe as table_name comes from a predefined list, not user input
        query = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES %s"  # nosec

        # Execute the query with all values at once
        execute_values(cursor, query, data)

        # Commit the transaction
        pg_conn.commit()

        logger.info(f"Inserted {len(data)} rows into {table_name}")
        return len(data)
    except psycopg2.Error as e:
        pg_conn.rollback()
        logger.error(f"Error inserting data into {table_name}: {e}")
        raise
    finally:
        cursor.close()

def migrate_table(sqlite_conn, pg_conn, table_name):
    """
    Migrate a single table from SQLite to PostgreSQL.

    Args:
        sqlite_conn: SQLite connection
        pg_conn: PostgreSQL connection
        table_name: Name of the table to migrate

    Returns:
        Number of rows migrated
    """
    logger.info(f"Migrating table: {table_name}")

    # Get columns for the table
    columns = get_table_columns(sqlite_conn, table_name)

    # Get data from SQLite
    rows = get_table_data(sqlite_conn, table_name, columns)

    if not rows:
        logger.info(f"No data to migrate for table {table_name}")
        return 0

    # Prepare data for PostgreSQL
    prepared_data = prepare_data_for_postgres(rows, columns)

    # Insert data into PostgreSQL
    return insert_data_to_postgres(pg_conn, table_name, columns, prepared_data)

def migrate_database(sqlite_path):
    """
    Migrate all tables from SQLite to PostgreSQL.

    Args:
        sqlite_path: Path to SQLite database file

    Returns:
        Dictionary with migration statistics
    """
    # Connect to databases
    sqlite_conn = connect_to_sqlite(sqlite_path)
    pg_conn = connect_to_postgres()

    try:
        # Get tables from SQLite
        tables = get_sqlite_tables(sqlite_conn)

        # Filter tables to only those in our definitions
        tables_to_migrate = [t for t in tables if t in TABLE_DEFINITIONS]

        # Sort tables by dependency order
        tables_to_migrate.sort(key=lambda t: TABLE_DEFINITIONS[t]["order"])

        # Migration statistics
        stats = {"total_tables": len(tables_to_migrate), "total_rows": 0, "tables": {}}

        # Migrate each table
        for table_name in tables_to_migrate:
            rows_migrated = migrate_table(sqlite_conn, pg_conn, table_name)
            stats["tables"][table_name] = rows_migrated
            stats["total_rows"] += rows_migrated

        logger.info(f"Migration completed successfully. Migrated {stats['total_rows']} rows across {stats['total_tables']} tables.")
        return stats

    finally:
        # Close connections
        sqlite_conn.close()
        pg_conn.close()

def main():
    """Main function to run the migration script."""
    parser = argparse.ArgumentParser(description="Migrate data from SQLite to PostgreSQL")
    parser.add_argument("--sqlite-path", required=True, help="Path to SQLite database file")
    args = parser.parse_args()

    try:
        migrate_database(args.sqlite_path)
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
