#!/usr/bin/env python3
"""
Test script to verify database connectivity and basic operations.
"""
import sys
from pathlib import Path

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database URL
DATABASE_URL = "postgresql://rentup:rentup_secure_password@localhost:5432/rentup"

def test_database_connection():
    """Test database connection."""
    try:
        # Create engine
        engine = create_engine(DATABASE_URL)

        # Test connection
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            logger.info(f"Database connection successful: {result.fetchone()}")

            # Get database version
            result = conn.execute(text("SELECT version()"))
            logger.info(f"Database version: {result.fetchone()[0]}")

        return True
    except Exception as e:
        logger.error(f"Database connection failed: {str(e)}")
        return False

def test_database_tables():
    """Test database tables."""
    try:
        # Create engine
        engine = create_engine(DATABASE_URL)

        # Create session
        Session = sessionmaker(bind=engine)
        session = Session()

        # Get table names
        result = session.execute(text("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'"))
        tables = [row[0] for row in result]
        logger.info(f"Database tables: {tables}")

        # Count rows in each table
        for table in tables:
            # Use parameterized query to prevent SQL injection
            result = session.execute(text("SELECT COUNT(*) FROM :table").bindparams(table=table))
            count = result.fetchone()[0]
            logger.info(f"Table {table}: {count} rows")

        session.close()
        return True
    except Exception as e:
        logger.error(f"Database tables test failed: {str(e)}")
        return False

def test_users_table():
    """Test users table."""
    try:
        # Create engine
        engine = create_engine(DATABASE_URL)

        # Create session
        Session = sessionmaker(bind=engine)
        session = Session()

        # Get users
        result = session.execute(text("SELECT id, email, full_name FROM users LIMIT 5"))
        users = result.fetchall()

        logger.info("Users in database:")
        for user in users:
            logger.info(f"  ID: {user[0]}, Email: {user[1]}, Name: {user[2]}")

        session.close()
        return True
    except Exception as e:
        logger.error(f"Users table test failed: {str(e)}")
        return False

def test_items_table():
    """Test items table."""
    try:
        # Create engine
        engine = create_engine(DATABASE_URL)

        # Create session
        Session = sessionmaker(bind=engine)
        session = Session()

        # Get items
        result = session.execute(text("SELECT id, name, category, daily_price FROM items LIMIT 5"))
        items = result.fetchall()

        logger.info("Items in database:")
        for item in items:
            logger.info(f"  ID: {item[0]}, Name: {item[1]}, Category: {item[2]}, Price: ${item[3]}/day")

        session.close()
        return True
    except Exception as e:
        logger.error(f"Items table test failed: {str(e)}")
        return False

def main():
    """Main function."""
    logger.info("Testing database...")

    # Test database connection
    if not test_database_connection():
        logger.error("Database connection test failed.")
        return

    # Test database tables
    if not test_database_tables():
        logger.error("Database tables test failed.")
        return

    # Test users table
    if not test_users_table():
        logger.error("Users table test failed.")
        return

    # Test items table
    if not test_items_table():
        logger.error("Items table test failed.")
        return

    logger.info("All database tests passed!")

if __name__ == "__main__":
    main()
