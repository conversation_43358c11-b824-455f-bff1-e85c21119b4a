#!/usr/bin/env python3
"""
AI performance optimization script for RentUp.

This script analyzes the performance of AI components and applies optimizations
such as model quantization, caching strategies, and batch processing.
"""
import sys
from pathlib import Path
import argparse
import time
import logging
import json
from typing import Dict, List, Any, Optional
import asyncio
import statistics
import matplotlib.pyplot as plt
import numpy as np
from tabulate import tabulate

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.core.config import settings
from app.core.cache import RedisCache
from app.ai.models import (
    AIRequest,
    ContentModerationRequest,
    FraudDetectionRequest,
    RecommendationRequest
)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Test data for different AI components
TEST_DATA = {
    "content_moderation": [
        {
            "content_type": "text",
            "text_content": "This is a test message for content moderation.",
            "user_id": "test_user_1",
            "context": "item_listing"
        },
        {
            "content_type": "text",
            "text_content": "This product is amazing and I love it!",
            "user_id": "test_user_2",
            "context": "review"
        },
        {
            "content_type": "text",
            "text_content": "Contact <NAME_EMAIL> for more information.",
            "user_id": "test_user_3",
            "context": "user_message"
        }
    ],
    "fraud_detection": [
        {
            "event_type": "user_registration",
            "user_id": "test_user_1",
            "event_data": {
                "email": "<EMAIL>",
                "ip_address": "***********",
                "device_id": "test_device_1",
                "registration_time": "2023-01-01T12:00:00Z"
            },
            "risk_threshold": 0.7
        },
        {
            "event_type": "item_listing",
            "user_id": "test_user_2",
            "item_id": "test_item_1",
            "event_data": {
                "item_price": 1000,
                "item_category": "electronics",
                "listing_time": "2023-01-02T12:00:00Z"
            },
            "risk_threshold": 0.7
        }
    ],
    "recommendation": [
        {
            "user_id": "test_user_1",
            "recommendation_type": "similar_items",
            "item_id": "test_item_1",
            "limit": 5
        },
        {
            "user_id": "test_user_2",
            "recommendation_type": "personalized",
            "limit": 5
        }
    ]
}

async def test_ai_component(component_type: str, test_data: Dict[str, Any], iterations: int = 5) -> Dict[str, Any]:
    """
    Test the performance of an AI component.
    
    Args:
        component_type: Type of AI component to test
        test_data: Test data for the component
        iterations: Number of test iterations
        
    Returns:
        Dictionary with performance metrics
    """
    from app.main import app
    from fastapi.testclient import TestClient
    
    client = TestClient(app)
    
    # Prepare request data
    if component_type == "content_moderation":
        endpoint = "/api/content-moderation"
        request_model = ContentModerationRequest
    elif component_type == "fraud_detection":
        endpoint = "/api/fraud-detection"
        request_model = FraudDetectionRequest
    elif component_type == "recommendation":
        endpoint = "/api/recommendations"
        request_model = RecommendationRequest
    else:
        raise ValueError(f"Unknown component type: {component_type}")
    
    # Run tests
    results = []
    cache_hits = 0
    
    for i in range(iterations):
        for test_case in test_data:
            # Clear cache for first iteration
            if i == 0:
                redis_cache = RedisCache()
                if redis_cache.is_available:
                    redis_cache.clear_pattern(f"{component_type}:*")
            
            # Send request
            start_time = time.time()
            response = client.post(endpoint, json=test_case)
            end_time = time.time()
            
            # Check if response is valid
            if response.status_code != 200:
                logger.error(f"Error testing {component_type}: {response.text}")
                continue
            
            # Extract metrics
            latency = (end_time - start_time) * 1000  # Convert to ms
            response_data = response.json()
            
            # Check if this was a cache hit
            if i > 0 and "metadata" in response_data and "cache_hit" in response_data["metadata"]:
                if response_data["metadata"]["cache_hit"]:
                    cache_hits += 1
            
            results.append({
                "iteration": i,
                "latency_ms": latency,
                "response": response_data
            })
    
    # Calculate metrics
    latencies = [result["latency_ms"] for result in results]
    
    return {
        "component": component_type,
        "iterations": iterations,
        "test_cases": len(test_data),
        "total_requests": len(results),
        "cache_hits": cache_hits,
        "latency": {
            "min_ms": min(latencies),
            "max_ms": max(latencies),
            "avg_ms": statistics.mean(latencies),
            "median_ms": statistics.median(latencies),
            "p95_ms": np.percentile(latencies, 95),
            "p99_ms": np.percentile(latencies, 99)
        },
        "results": results
    }

def plot_performance(metrics: Dict[str, Any], output_file: Optional[str] = None):
    """
    Plot performance metrics.
    
    Args:
        metrics: Performance metrics
        output_file: Optional file to save the plot
    """
    components = list(metrics.keys())
    avg_latencies = [metrics[c]["latency"]["avg_ms"] for c in components]
    p95_latencies = [metrics[c]["latency"]["p95_ms"] for c in components]
    
    fig, ax = plt.subplots(figsize=(10, 6))
    
    x = np.arange(len(components))
    width = 0.35
    
    ax.bar(x - width/2, avg_latencies, width, label='Avg Latency (ms)')
    ax.bar(x + width/2, p95_latencies, width, label='P95 Latency (ms)')
    
    ax.set_ylabel('Latency (ms)')
    ax.set_title('AI Component Performance')
    ax.set_xticks(x)
    ax.set_xticklabels(components)
    ax.legend()
    
    if output_file:
        plt.savefig(output_file)
    else:
        plt.show()

def print_performance_table(metrics: Dict[str, Any]):
    """
    Print performance metrics as a table.
    
    Args:
        metrics: Performance metrics
    """
    table_data = []
    
    for component, data in metrics.items():
        table_data.append([
            component,
            f"{data['latency']['avg_ms']:.2f}",
            f"{data['latency']['p95_ms']:.2f}",
            f"{data['latency']['min_ms']:.2f}",
            f"{data['latency']['max_ms']:.2f}",
            f"{data['cache_hits']} / {data['total_requests']}"
        ])
    
    headers = ["Component", "Avg Latency (ms)", "P95 Latency (ms)", "Min Latency (ms)", "Max Latency (ms)", "Cache Hits"]
    print(tabulate(table_data, headers=headers, tablefmt="grid"))

async def main():
    """Main function to run the optimization script."""
    parser = argparse.ArgumentParser(description="Optimize AI components for RentUp")
    
    # Add arguments
    parser.add_argument("--test", action="store_true", help="Run performance tests")
    parser.add_argument("--optimize", action="store_true", help="Apply optimizations")
    parser.add_argument("--component", choices=["content_moderation", "fraud_detection", "recommendation", "all"], 
                        default="all", help="Component to test/optimize")
    parser.add_argument("--iterations", type=int, default=5, help="Number of test iterations")
    parser.add_argument("--plot", action="store_true", help="Generate performance plots")
    parser.add_argument("--output", type=str, help="Output file for plots")
    
    args = parser.parse_args()
    
    # Default to test if no action specified
    if not (args.test or args.optimize):
        args.test = True
    
    # Determine which components to process
    components = ["content_moderation", "fraud_detection", "recommendation"] if args.component == "all" else [args.component]
    
    # Run tests
    if args.test:
        logger.info(f"Testing AI components: {', '.join(components)}")
        
        metrics = {}
        for component in components:
            logger.info(f"Testing {component}...")
            component_metrics = await test_ai_component(
                component_type=component,
                test_data=TEST_DATA[component],
                iterations=args.iterations
            )
            metrics[component] = component_metrics
            logger.info(f"Completed testing {component}")
        
        # Print results
        print_performance_table(metrics)
        
        # Generate plots if requested
        if args.plot:
            plot_performance(metrics, args.output)
    
    # Apply optimizations
    if args.optimize:
        logger.info(f"Optimizing AI components: {', '.join(components)}")
        
        # Apply optimizations for each component
        for component in components:
            logger.info(f"Optimizing {component}...")
            
            # Implement optimization logic here
            # This would typically involve:
            # 1. Adjusting cache TTLs
            # 2. Configuring batch processing
            # 3. Tuning model parameters
            
            logger.info(f"Completed optimizing {component}")
    
    logger.info("AI optimization completed successfully!")

if __name__ == "__main__":
    asyncio.run(main())
