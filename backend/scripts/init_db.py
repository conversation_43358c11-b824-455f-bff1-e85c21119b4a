#!/usr/bin/env python3
"""
Database initialization script for RentUp.
This script creates the database tables and populates them with initial data.
"""
import sys
from pathlib import Path

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.models.user import User
from app.models.item import Item
from app.models.rental import Rental
from app.models.auction import Auction
from app.models.bid import Bid
from app.models.agreement import Agreement
from app.models.fraud_alert import FraudAlert

# Override the DATABASE_URL for this script
settings.DATABASE_URL = "postgresql://rentup:rentup_secure_password@localhost:5432/rentup"
import uuid
from datetime import datetime, timedelta, UTC
import argparse
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_database():
    """Create the database if it doesn't exist."""
    # Extract database name from URL
    db_url_parts = settings.DATABASE_URL.split('/')
    db_name = db_url_parts[-1]

    # Create a connection to the postgres database
    engine = create_engine('/'.join(db_url_parts[:-1]) + '/postgres')

    # Connect and create the database if it doesn't exist
    with engine.connect() as conn:
        conn.execute(text("COMMIT"))

        # Check if database exists - using parameterized query to prevent SQL injection
        result = conn.execute(
            text("SELECT 1 FROM pg_database WHERE datname = :db_name").bindparams(db_name=db_name)
        )
        if result.fetchone() is None:
            # For CREATE DATABASE, we need to use a different approach since it can't be parameterized directly
            # This is still safe as db_name comes from settings, not user input
            conn.execute(text(f"CREATE DATABASE {db_name}"))
            logger.info(f"Database {db_name} created.")
        else:
            logger.info(f"Database {db_name} already exists.")

def create_tables():
    """Create all tables defined in the models."""
    engine = create_engine(settings.DATABASE_URL)

    # Create tables directly using SQL to avoid ORM relationship issues
    with engine.connect() as conn:
        # Create users table
        conn.execute(text("""
        CREATE TABLE IF NOT EXISTS users (
            id VARCHAR PRIMARY KEY,
            email VARCHAR NOT NULL UNIQUE,
            full_name VARCHAR NOT NULL,
            hashed_password VARCHAR NOT NULL,
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            is_admin BOOLEAN NOT NULL DEFAULT FALSE,
            verification_level VARCHAR NOT NULL DEFAULT 'basic',
            risk_score FLOAT,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        """))

        # Create items table
        conn.execute(text("""
        CREATE TABLE IF NOT EXISTS items (
            id VARCHAR PRIMARY KEY,
            name VARCHAR NOT NULL,
            description TEXT,
            category VARCHAR NOT NULL,
            owner_id VARCHAR NOT NULL REFERENCES users(id),
            value FLOAT NOT NULL,
            daily_price FLOAT NOT NULL,
            weekly_price FLOAT,
            monthly_price FLOAT,
            location VARCHAR,
            is_available BOOLEAN NOT NULL DEFAULT TRUE,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        """))

        # Create rentals table
        conn.execute(text("""
        CREATE TABLE IF NOT EXISTS rentals (
            id VARCHAR PRIMARY KEY,
            item_id VARCHAR NOT NULL REFERENCES items(id),
            renter_id VARCHAR NOT NULL REFERENCES users(id),
            owner_id VARCHAR NOT NULL REFERENCES users(id),
            start_date TIMESTAMP WITH TIME ZONE NOT NULL,
            end_date TIMESTAMP WITH TIME ZONE NOT NULL,
            total_price FLOAT NOT NULL,
            status VARCHAR NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        """))

        # Create auctions table
        conn.execute(text("""
        CREATE TABLE IF NOT EXISTS auctions (
            id VARCHAR PRIMARY KEY,
            item_id VARCHAR NOT NULL REFERENCES items(id),
            owner_id VARCHAR NOT NULL REFERENCES users(id),
            title VARCHAR NOT NULL,
            description TEXT,
            start_time TIMESTAMP WITH TIME ZONE NOT NULL,
            end_time TIMESTAMP WITH TIME ZONE NOT NULL,
            reserve_price FLOAT NOT NULL,
            min_increment FLOAT NOT NULL,
            current_highest_bid FLOAT NOT NULL DEFAULT 0,
            current_highest_bidder_id VARCHAR REFERENCES users(id),
            status VARCHAR NOT NULL,
            rental_start_date TIMESTAMP WITH TIME ZONE NOT NULL,
            rental_end_date TIMESTAMP WITH TIME ZONE NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        """))

        # Create bids table
        conn.execute(text("""
        CREATE TABLE IF NOT EXISTS bids (
            id VARCHAR PRIMARY KEY,
            auction_id VARCHAR NOT NULL REFERENCES auctions(id),
            bidder_id VARCHAR NOT NULL REFERENCES users(id),
            amount FLOAT NOT NULL,
            status VARCHAR NOT NULL,
            placed_at TIMESTAMP WITH TIME ZONE NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        """))

        # Create agreements table
        conn.execute(text("""
        CREATE TABLE IF NOT EXISTS agreements (
            id VARCHAR PRIMARY KEY,
            rental_id VARCHAR NOT NULL REFERENCES rentals(id),
            owner_id VARCHAR NOT NULL REFERENCES users(id),
            renter_id VARCHAR NOT NULL REFERENCES users(id),
            content TEXT NOT NULL,
            status VARCHAR NOT NULL,
            agreement_type VARCHAR NOT NULL,
            terms JSONB,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        """))

        # Create fraud_alerts table
        conn.execute(text("""
        CREATE TABLE IF NOT EXISTS fraud_alerts (
            id VARCHAR PRIMARY KEY,
            user_id VARCHAR REFERENCES users(id),
            item_id VARCHAR REFERENCES items(id),
            auction_id VARCHAR REFERENCES auctions(id),
            alert_type VARCHAR NOT NULL,
            severity VARCHAR NOT NULL,
            risk_score FLOAT NOT NULL,
            details JSONB,
            status VARCHAR NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            resolved_at TIMESTAMP WITH TIME ZONE,
            resolved_by VARCHAR REFERENCES users(id)
        )
        """))

        conn.commit()

    logger.info("Tables created directly with SQL.")

def create_sample_data():
    """Create sample data for testing."""
    engine = create_engine(settings.DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()

    try:
        # Create categories (using Item model with category field)
        categories = ["Electronics", "Furniture", "Vehicles", "Tools", "Sports"]
        logger.info("Categories defined.")

        # Create users
        users = []
        for i in range(5):
            user = User(
                id=str(uuid.uuid4()),
                email=f"user{i}@example.com",
                full_name=f"Test User {i}",
                hashed_password="$argon2id$v=19$m=65536,t=3,p=4$c2FsdHNhbHRzYWx0c2FsdA$xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",  # Dummy hash
                is_active=True,
                verification_level="basic" if i < 3 else "advanced",
                risk_score=0.2 if i < 2 else (0.5 if i < 4 else 0.8)
            )
            users.append(user)
        db.add_all(users)
        db.commit()
        logger.info("Users created.")

        # Create items
        items = []
        for i in range(10):
            item = Item(
                id=str(uuid.uuid4()),
                name=f"Test Item {i}",
                description=f"A test item {i} for rental",
                category=categories[i % len(categories)],
                owner_id=users[i % len(users)].id,
                value=100.0 * (i + 1),
                daily_price=10.0 * (i + 1),
                weekly_price=50.0 * (i + 1),
                monthly_price=180.0 * (i + 1),
                location="Test Location",
                is_available=True
            )
            items.append(item)
        db.add_all(items)
        db.commit()
        logger.info("Items created.")

        # Create rentals
        rentals = []
        for i in range(5):
            rental = Rental(
                id=str(uuid.uuid4()),
                item_id=items[i].id,
                renter_id=users[(i + 1) % len(users)].id,
                owner_id=items[i].owner_id,
                start_date=datetime.now(UTC) + timedelta(days=i),
                end_date=datetime.now(UTC) + timedelta(days=i + 7),
                total_price=items[i].daily_price * 7,
                status="pending" if i < 2 else ("active" if i < 4 else "completed")
            )
            rentals.append(rental)
        db.add_all(rentals)
        db.commit()
        logger.info("Rentals created.")

        # Skip reviews for now due to dependency on bookings table
        logger.info("Skipping reviews creation.")

        # Create auctions
        auctions = []
        for i in range(3):
            auction = Auction(
                id=str(uuid.uuid4()),
                item_id=items[i + 5].id,
                owner_id=items[i + 5].owner_id,
                title=f"Auction for {items[i + 5].name}",
                description=f"Bid on {items[i + 5].name} for a great deal!",
                start_time=datetime.now(UTC) - timedelta(days=1),
                end_time=datetime.now(UTC) + timedelta(days=6 - i),
                reserve_price=items[i + 5].daily_price * 5,
                min_increment=5.0,
                current_highest_bid=0.0,
                status="active",
                rental_start_date=datetime.now(UTC) + timedelta(days=7),
                rental_end_date=datetime.now(UTC) + timedelta(days=14)
            )
            auctions.append(auction)
        db.add_all(auctions)
        db.commit()
        logger.info("Auctions created.")

        # Create bids
        bids = []
        for i in range(5):
            auction_index = i % len(auctions)
            bidder_index = (i + 1) % len(users)
            bid_amount = auctions[auction_index].reserve_price + (i + 1) * 10.0

            bid = Bid(
                id=str(uuid.uuid4()),
                auction_id=auctions[auction_index].id,
                bidder_id=users[bidder_index].id,
                amount=bid_amount,
                status="active" if i == 4 else "outbid",
                placed_at=datetime.now(UTC) - timedelta(hours=24-i)
            )
            bids.append(bid)

            # Update auction with highest bid
            if i == 4:
                auctions[auction_index].current_highest_bid = bid_amount
                auctions[auction_index].current_highest_bidder_id = users[bidder_index].id

        db.add_all(bids)
        db.commit()
        logger.info("Bids created.")

        # Create agreements
        agreements = []
        for i in range(2):
            agreement = Agreement(
                id=str(uuid.uuid4()),
                rental_id=rentals[i].id,
                owner_id=rentals[i].owner_id,
                renter_id=rentals[i].renter_id,
                content=f"This is a rental agreement for {items[i].name}. The rental period is from {rentals[i].start_date} to {rentals[i].end_date}.",
                status="draft" if i == 0 else "signed",
                agreement_type="standard"
            )
            agreements.append(agreement)
        db.add_all(agreements)
        db.commit()
        logger.info("Agreements created.")

        # Create fraud alerts
        fraud_alerts = []
        alert_types = ["suspicious_user", "suspicious_item", "suspicious_auction"]
        severities = ["low", "medium", "high"]
        statuses = ["new", "investigating", "resolved"]

        for i in range(3):
            alert = FraudAlert(
                id=str(uuid.uuid4()),
                user_id=users[i].id if i == 0 else None,
                item_id=items[i].id if i == 1 else None,
                auction_id=auctions[0].id if i == 2 else None,
                alert_type=alert_types[i],
                severity=severities[i],
                risk_score=0.3 + (i * 0.2),
                details={"test": "data"},
                status=statuses[i],
                created_at=datetime.now(UTC) - timedelta(days=i),
                resolved_at=datetime.now(UTC) if statuses[i] == "resolved" else None,
                resolved_by=users[0].id if statuses[i] == "resolved" else None
            )
            fraud_alerts.append(alert)
        db.add_all(fraud_alerts)
        db.commit()
        logger.info("Fraud alerts created.")

        # Notifications are not implemented yet
        logger.info("Skipping notifications creation.")

        logger.info("Sample data creation completed successfully.")
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating sample data: {e}")
        raise
    finally:
        db.close()

def main():
    parser = argparse.ArgumentParser(description="Initialize the RentUp database")
    parser.add_argument("--create-db", action="store_true", help="Create the database if it doesn't exist")
    parser.add_argument("--create-tables", action="store_true", help="Create all tables")
    parser.add_argument("--sample-data", action="store_true", help="Create sample data")
    parser.add_argument("--all", action="store_true", help="Perform all initialization steps")

    args = parser.parse_args()

    if args.all or args.create_db:
        create_database()

    if args.all or args.create_tables:
        create_tables()

    if args.all or args.sample_data:
        create_sample_data()

if __name__ == "__main__":
    main()
