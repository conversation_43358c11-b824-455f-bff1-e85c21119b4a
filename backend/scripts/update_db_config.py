#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to update the database configuration in the application.
"""
import sys
from pathlib import Path

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import the settings module
from app.core.config import settings

def update_config():
    """Update the database configuration."""
    try:
        # Print the current DATABASE_URL
        logger.info(f"Current DATABASE_URL: {settings.DATABASE_URL}")
        
        # Override the DATABASE_URL
        settings.DATABASE_URL = "postgresql://rentup:rentup_secure_password@localhost:5432/rentup"
        
        # Print the updated DATABASE_URL
        logger.info(f"Updated DATABASE_URL: {settings.DATABASE_URL}")
        
        logger.info("Database configuration updated successfully!")
    except Exception as e:
        logger.error(f"Error updating database configuration: {e}")
        raise

if __name__ == "__main__":
    update_config()
