#!/usr/bin/env python3
"""
Database initialization script for RentUp.
This script creates the database tables and populates them with initial data.
"""
import sys
from pathlib import Path

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import create_engine, text
from app.core.config import settings
import uuid
from datetime import datetime, timedelta, UTC
import argparse
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Override the DATABASE_URL for this script
settings.DATABASE_URL = "postgresql://rentup:rentup_secure_password@localhost:5432/rentup"

def create_database():
    """Create the database if it doesn't exist."""
    # Extract database name from URL
    db_url_parts = settings.DATABASE_URL.split('/')
    db_name = db_url_parts[-1]

    # Create a connection to the postgres database
    engine = create_engine('/'.join(db_url_parts[:-1]) + '/postgres')

    # Connect and create the database if it doesn't exist
    with engine.connect() as conn:
        conn.execute(text("COMMIT"))

        # Check if database exists - using parameterized query to prevent SQL injection
        result = conn.execute(
            text("SELECT 1 FROM pg_database WHERE datname = :db_name").bindparams(db_name=db_name)
        )
        if result.fetchone() is None:
            # For CREATE DATABASE, we need to use a different approach since it can't be parameterized directly
            # This is still safe as db_name comes from settings, not user input
            conn.execute(text(f"CREATE DATABASE {db_name}"))
            logger.info(f"Database {db_name} created.")
        else:
            logger.info(f"Database {db_name} already exists.")

def create_tables():
    """Create all tables defined in the models."""
    engine = create_engine(settings.DATABASE_URL)

    # Create tables directly using SQL to avoid ORM relationship issues
    with engine.connect() as conn:
        # Drop existing tables in reverse order of dependencies
        conn.execute(text("DROP TABLE IF EXISTS fraud_alerts CASCADE"))
        conn.execute(text("DROP TABLE IF EXISTS agreements CASCADE"))
        conn.execute(text("DROP TABLE IF EXISTS bids CASCADE"))
        conn.execute(text("DROP TABLE IF EXISTS auctions CASCADE"))
        conn.execute(text("DROP TABLE IF EXISTS rentals CASCADE"))
        conn.execute(text("DROP TABLE IF EXISTS items CASCADE"))
        conn.execute(text("DROP TABLE IF EXISTS users CASCADE"))
        conn.commit()
        logger.info("Dropped existing tables.")
        # Create users table
        conn.execute(text("""
        CREATE TABLE IF NOT EXISTS users (
            id VARCHAR PRIMARY KEY,
            email VARCHAR NOT NULL UNIQUE,
            full_name VARCHAR NOT NULL,
            hashed_password VARCHAR NOT NULL,
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            is_admin BOOLEAN NOT NULL DEFAULT FALSE,
            verification_level VARCHAR NOT NULL DEFAULT 'basic',
            risk_score FLOAT,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        """))

        # Create items table
        conn.execute(text("""
        CREATE TABLE IF NOT EXISTS items (
            id VARCHAR PRIMARY KEY,
            name VARCHAR NOT NULL,
            description TEXT,
            category VARCHAR NOT NULL,
            subcategory VARCHAR,
            condition VARCHAR NOT NULL DEFAULT 'good',
            owner_id VARCHAR NOT NULL REFERENCES users(id),
            value FLOAT NOT NULL,
            daily_price FLOAT NOT NULL,
            weekly_price FLOAT,
            monthly_price FLOAT,
            deposit_amount FLOAT,
            location VARCHAR NOT NULL,
            is_available BOOLEAN NOT NULL DEFAULT TRUE,
            is_featured BOOLEAN NOT NULL DEFAULT FALSE,
            is_verified BOOLEAN NOT NULL DEFAULT FALSE,
            images JSONB,
            attributes JSONB,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        """))

        # Create rentals table
        conn.execute(text("""
        CREATE TABLE IF NOT EXISTS rentals (
            id VARCHAR PRIMARY KEY,
            item_id VARCHAR NOT NULL REFERENCES items(id),
            renter_id VARCHAR NOT NULL REFERENCES users(id),
            owner_id VARCHAR NOT NULL REFERENCES users(id),
            start_date TIMESTAMP WITH TIME ZONE NOT NULL,
            end_date TIMESTAMP WITH TIME ZONE NOT NULL,
            total_price FLOAT NOT NULL,
            status VARCHAR NOT NULL,
            payment_status VARCHAR NOT NULL DEFAULT 'pending',
            payment_intent_id VARCHAR,
            notes TEXT,
            deposit_paid BOOLEAN NOT NULL DEFAULT FALSE,
            deposit_returned BOOLEAN NOT NULL DEFAULT FALSE,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        """))

        # Create auctions table
        conn.execute(text("""
        CREATE TABLE IF NOT EXISTS auctions (
            id VARCHAR PRIMARY KEY,
            item_id VARCHAR NOT NULL REFERENCES items(id),
            owner_id VARCHAR NOT NULL REFERENCES users(id),
            title VARCHAR NOT NULL,
            description TEXT,
            start_time TIMESTAMP WITH TIME ZONE NOT NULL,
            end_time TIMESTAMP WITH TIME ZONE NOT NULL,
            reserve_price FLOAT NOT NULL,
            min_increment FLOAT NOT NULL,
            current_highest_bid FLOAT NOT NULL DEFAULT 0,
            current_highest_bidder_id VARCHAR REFERENCES users(id),
            status VARCHAR NOT NULL,
            rental_start_date TIMESTAMP WITH TIME ZONE NOT NULL,
            rental_end_date TIMESTAMP WITH TIME ZONE NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        """))

        # Create bids table
        conn.execute(text("""
        CREATE TABLE IF NOT EXISTS bids (
            id VARCHAR PRIMARY KEY,
            auction_id VARCHAR NOT NULL REFERENCES auctions(id),
            bidder_id VARCHAR NOT NULL REFERENCES users(id),
            amount FLOAT NOT NULL,
            status VARCHAR NOT NULL,
            placed_at TIMESTAMP WITH TIME ZONE NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        """))

        # Create agreements table
        conn.execute(text("""
        CREATE TABLE IF NOT EXISTS agreements (
            id VARCHAR PRIMARY KEY,
            rental_id VARCHAR NOT NULL REFERENCES rentals(id),
            owner_id VARCHAR NOT NULL REFERENCES users(id),
            renter_id VARCHAR NOT NULL REFERENCES users(id),
            content TEXT NOT NULL,
            status VARCHAR NOT NULL,
            agreement_type VARCHAR NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        """))

        # Create fraud_alerts table
        conn.execute(text("""
        CREATE TABLE IF NOT EXISTS fraud_alerts (
            id VARCHAR PRIMARY KEY,
            user_id VARCHAR REFERENCES users(id),
            item_id VARCHAR REFERENCES items(id),
            auction_id VARCHAR REFERENCES auctions(id),
            alert_type VARCHAR NOT NULL,
            severity VARCHAR NOT NULL,
            risk_score FLOAT NOT NULL,
            details JSONB,
            status VARCHAR NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            resolved_at TIMESTAMP WITH TIME ZONE,
            resolved_by VARCHAR REFERENCES users(id)
        )
        """))

        conn.commit()

    logger.info("Tables created directly with SQL.")

def create_sample_data():
    """Create sample data for testing."""
    engine = create_engine(settings.DATABASE_URL)

    # Define categories
    categories = ["Electronics", "Furniture", "Vehicles", "Tools", "Sports"]
    logger.info("Categories defined.")

    # Create sample data using direct SQL
    with engine.connect() as conn:
        try:
            # Create users
            user_ids = []
            for i in range(5):
                user_id = str(uuid.uuid4())
                user_ids.append(user_id)

                conn.execute(text("""
                INSERT INTO users (
                    id, email, full_name, hashed_password, is_active, is_admin,
                    verification_level, risk_score, created_at, updated_at
                ) VALUES (
                    :id, :email, :full_name, :hashed_password, :is_active, :is_admin,
                    :verification_level, :risk_score, :created_at, :updated_at
                )
                """), {
                    "id": user_id,
                    "email": f"user{i}@example.com",
                    "full_name": f"Test User {i}",
                    "hashed_password": "$argon2id$v=19$m=65536,t=3,p=4$c2FsdHNhbHRzYWx0c2FsdA$xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
                    "is_active": True,
                    "is_admin": i == 0,  # First user is admin
                    "verification_level": "basic" if i < 3 else "advanced",
                    "risk_score": 0.2 if i < 2 else (0.5 if i < 4 else 0.8),
                    "created_at": datetime.now(UTC),
                    "updated_at": datetime.now(UTC)
                })

            logger.info("Users created.")

            # Create items
            item_ids = []
            for i in range(10):
                item_id = str(uuid.uuid4())
                item_ids.append(item_id)

                conditions = ["new", "like_new", "good", "fair", "poor"]

                conn.execute(text("""
                INSERT INTO items (
                    id, name, description, category, subcategory, condition, owner_id,
                    value, daily_price, weekly_price, monthly_price, deposit_amount,
                    location, is_available, is_featured, is_verified, images, attributes,
                    created_at, updated_at
                ) VALUES (
                    :id, :name, :description, :category, :subcategory, :condition, :owner_id,
                    :value, :daily_price, :weekly_price, :monthly_price, :deposit_amount,
                    :location, :is_available, :is_featured, :is_verified, :images, :attributes,
                    :created_at, :updated_at
                )
                """), {
                    "id": item_id,
                    "name": f"Test Item {i}",
                    "description": f"A test item {i} for rental",
                    "category": categories[i % len(categories)],
                    "subcategory": f"Sub-{categories[i % len(categories)]}" if i % 2 == 0 else None,
                    "condition": conditions[i % len(conditions)],
                    "owner_id": user_ids[i % len(user_ids)],
                    "value": 100.0 * (i + 1),
                    "daily_price": 10.0 * (i + 1),
                    "weekly_price": 50.0 * (i + 1),
                    "monthly_price": 180.0 * (i + 1),
                    "deposit_amount": 50.0 * (i + 1) if i % 2 == 0 else None,
                    "location": "Test Location",
                    "is_available": True,
                    "is_featured": i < 3,
                    "is_verified": i < 5,
                    "images": '["https://example.com/image1.jpg", "https://example.com/image2.jpg"]' if i % 2 == 0 else None,
                    "attributes": '{"color": "red", "size": "medium"}' if i % 2 == 0 else None,
                    "created_at": datetime.now(UTC),
                    "updated_at": datetime.now(UTC)
                })

            logger.info("Items created.")

            # Create rentals
            rental_ids = []
            for i in range(5):
                rental_id = str(uuid.uuid4())
                rental_ids.append(rental_id)

                owner_id = conn.execute(text("SELECT owner_id FROM items WHERE id = :item_id"),
                                       {"item_id": item_ids[i]}).scalar()

                conn.execute(text("""
                INSERT INTO rentals (
                    id, item_id, renter_id, owner_id, start_date, end_date,
                    total_price, status, payment_status, payment_intent_id, notes,
                    deposit_paid, deposit_returned, created_at, updated_at
                ) VALUES (
                    :id, :item_id, :renter_id, :owner_id, :start_date, :end_date,
                    :total_price, :status, :payment_status, :payment_intent_id, :notes,
                    :deposit_paid, :deposit_returned, :created_at, :updated_at
                )
                """), {
                    "id": rental_id,
                    "item_id": item_ids[i],
                    "renter_id": user_ids[(i + 1) % len(user_ids)],
                    "owner_id": owner_id,
                    "start_date": datetime.now(UTC) + timedelta(days=i),
                    "end_date": datetime.now(UTC) + timedelta(days=i + 7),
                    "total_price": 10.0 * (i + 1) * 7,  # daily_price * 7
                    "status": "pending" if i < 2 else ("active" if i < 4 else "completed"),
                    "payment_status": "paid" if i > 2 else "pending",
                    "payment_intent_id": f"pi_{uuid.uuid4()}" if i > 2 else None,
                    "notes": f"Notes for rental {i}" if i % 2 == 0 else None,
                    "deposit_paid": i > 1,
                    "deposit_returned": i > 3,
                    "created_at": datetime.now(UTC),
                    "updated_at": datetime.now(UTC)
                })

            logger.info("Rentals created.")

            # Create auctions
            auction_ids = []
            for i in range(3):
                auction_id = str(uuid.uuid4())
                auction_ids.append(auction_id)

                item_index = i + 5
                owner_id = conn.execute(text("SELECT owner_id FROM items WHERE id = :item_id"),
                                       {"item_id": item_ids[item_index]}).scalar()

                conn.execute(text("""
                INSERT INTO auctions (
                    id, item_id, owner_id, title, description, start_time, end_time,
                    reserve_price, min_increment, current_highest_bid, status,
                    rental_start_date, rental_end_date, created_at, updated_at
                ) VALUES (
                    :id, :item_id, :owner_id, :title, :description, :start_time, :end_time,
                    :reserve_price, :min_increment, :current_highest_bid, :status,
                    :rental_start_date, :rental_end_date, :created_at, :updated_at
                )
                """), {
                    "id": auction_id,
                    "item_id": item_ids[item_index],
                    "owner_id": owner_id,
                    "title": f"Auction for Test Item {item_index}",
                    "description": f"Bid on Test Item {item_index} for a great deal!",
                    "start_time": datetime.now(UTC) - timedelta(days=1),
                    "end_time": datetime.now(UTC) + timedelta(days=6 - i),
                    "reserve_price": 10.0 * (item_index + 1) * 5,  # daily_price * 5
                    "min_increment": 5.0,
                    "current_highest_bid": 0.0,
                    "status": "active",
                    "rental_start_date": datetime.now(UTC) + timedelta(days=7),
                    "rental_end_date": datetime.now(UTC) + timedelta(days=14),
                    "created_at": datetime.now(UTC),
                    "updated_at": datetime.now(UTC)
                })

            logger.info("Auctions created.")

            # Create bids
            highest_bids = {}
            highest_bidders = {}

            for i in range(5):
                bid_id = str(uuid.uuid4())
                auction_index = i % len(auction_ids)
                bidder_index = (i + 1) % len(user_ids)

                # Get reserve price for the auction
                reserve_price = conn.execute(text("SELECT reserve_price FROM auctions WHERE id = :auction_id"),
                                           {"auction_id": auction_ids[auction_index]}).scalar()

                bid_amount = reserve_price + (i + 1) * 10.0

                conn.execute(text("""
                INSERT INTO bids (
                    id, auction_id, bidder_id, amount, status, placed_at, created_at, updated_at
                ) VALUES (
                    :id, :auction_id, :bidder_id, :amount, :status, :placed_at, :created_at, :updated_at
                )
                """), {
                    "id": bid_id,
                    "auction_id": auction_ids[auction_index],
                    "bidder_id": user_ids[bidder_index],
                    "amount": bid_amount,
                    "status": "active" if i == 4 else "outbid",
                    "placed_at": datetime.now(UTC) - timedelta(hours=24-i),
                    "created_at": datetime.now(UTC),
                    "updated_at": datetime.now(UTC)
                })

                # Keep track of highest bid for each auction
                if auction_index not in highest_bids or bid_amount > highest_bids[auction_index]:
                    highest_bids[auction_index] = bid_amount
                    highest_bidders[auction_index] = user_ids[bidder_index]

            # Update auctions with highest bids
            for auction_index, bid_amount in highest_bids.items():
                conn.execute(text("""
                UPDATE auctions
                SET current_highest_bid = :bid_amount,
                    current_highest_bidder_id = :bidder_id,
                    updated_at = :updated_at
                WHERE id = :auction_id
                """), {
                    "bid_amount": bid_amount,
                    "bidder_id": highest_bidders[auction_index],
                    "updated_at": datetime.now(UTC),
                    "auction_id": auction_ids[auction_index]
                })

            logger.info("Bids created.")

            # Create agreements
            for i in range(2):
                agreement_id = str(uuid.uuid4())

                # Get rental details
                rental = conn.execute(text("""
                SELECT r.id, r.owner_id, r.renter_id, r.start_date, r.end_date, i.name
                FROM rentals r
                JOIN items i ON r.item_id = i.id
                WHERE r.id = :rental_id
                """), {"rental_id": rental_ids[i]}).fetchone()

                conn.execute(text("""
                INSERT INTO agreements (
                    id, rental_id, owner_id, renter_id, content, status, agreement_type,
                    created_at, updated_at
                ) VALUES (
                    :id, :rental_id, :owner_id, :renter_id, :content, :status, :agreement_type,
                    :created_at, :updated_at
                )
                """), {
                    "id": agreement_id,
                    "rental_id": rental_ids[i],
                    "owner_id": rental.owner_id,
                    "renter_id": rental.renter_id,
                    "content": f"This is a rental agreement for {rental.name}. The rental period is from {rental.start_date} to {rental.end_date}.",
                    "status": "draft" if i == 0 else "signed",
                    "agreement_type": "standard",
                    "created_at": datetime.now(UTC),
                    "updated_at": datetime.now(UTC)
                })

            logger.info("Agreements created.")

            # Create fraud alerts
            alert_types = ["suspicious_user", "suspicious_item", "suspicious_auction"]
            severities = ["low", "medium", "high"]
            statuses = ["new", "investigating", "resolved"]

            for i in range(3):
                alert_id = str(uuid.uuid4())

                conn.execute(text("""
                INSERT INTO fraud_alerts (
                    id, user_id, item_id, auction_id, alert_type, severity, risk_score,
                    details, status, created_at, resolved_at, resolved_by
                ) VALUES (
                    :id, :user_id, :item_id, :auction_id, :alert_type, :severity, :risk_score,
                    :details, :status, :created_at, :resolved_at, :resolved_by
                )
                """), {
                    "id": alert_id,
                    "user_id": user_ids[i] if i == 0 else None,
                    "item_id": item_ids[i] if i == 1 else None,
                    "auction_id": auction_ids[0] if i == 2 else None,
                    "alert_type": alert_types[i],
                    "severity": severities[i],
                    "risk_score": 0.3 + (i * 0.2),
                    "details": '{"test": "data"}',
                    "status": statuses[i],
                    "created_at": datetime.now(UTC) - timedelta(days=i),
                    "resolved_at": datetime.now(UTC) if statuses[i] == "resolved" else None,
                    "resolved_by": user_ids[0] if statuses[i] == "resolved" else None
                })

            logger.info("Fraud alerts created.")

            conn.commit()
            logger.info("Sample data creation completed successfully.")
        except Exception as e:
            conn.rollback()
            logger.error(f"Error creating sample data: {e}")
            raise

def main():
    parser = argparse.ArgumentParser(description="Initialize the RentUp database")
    parser.add_argument("--create-db", action="store_true", help="Create the database if it doesn't exist")
    parser.add_argument("--create-tables", action="store_true", help="Create all tables")
    parser.add_argument("--sample-data", action="store_true", help="Create sample data")
    parser.add_argument("--all", action="store_true", help="Perform all initialization steps")

    args = parser.parse_args()

    if args.all or args.create_db:
        create_database()

    if args.all or args.create_tables:
        create_tables()

    if args.all or args.sample_data:
        create_sample_data()

if __name__ == "__main__":
    main()
