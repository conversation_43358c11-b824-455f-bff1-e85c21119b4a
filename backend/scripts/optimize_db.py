#!/usr/bin/env python3
"""
Database optimization script for RentUp.

This script analyzes the PostgreSQL database and performs optimizations
such as creating indexes, updating statistics, and suggesting configuration
improvements.

Usage:
    python optimize_db.py --analyze  # Analyze database and suggest optimizations
    python optimize_db.py --apply    # Apply recommended optimizations
    python optimize_db.py --all      # Analyze and apply optimizations
    python optimize_db.py --help     # Show help message
"""
import sys
from pathlib import Path
import argparse
import logging
from typing import Dict, Any

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import create_engine, text, inspect

# Import the settings module
from app.core.config import settings

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Tables that should have indexes
TABLE_INDEXES = {
    "users": [
        {"name": "idx_users_email", "columns": ["email"]},
        {"name": "idx_users_verification_level", "columns": ["verification_level"]}
    ],
    "items": [
        {"name": "idx_items_owner_id", "columns": ["owner_id"]},
        {"name": "idx_items_category", "columns": ["category"]},
        {"name": "idx_items_location", "columns": ["location"]},
        {"name": "idx_items_is_available", "columns": ["is_available"]},
        {"name": "idx_items_is_featured", "columns": ["is_featured"]},
        {"name": "idx_items_created_at", "columns": ["created_at"]}
    ],
    "rentals": [
        {"name": "idx_rentals_item_id", "columns": ["item_id"]},
        {"name": "idx_rentals_renter_id", "columns": ["renter_id"]},
        {"name": "idx_rentals_owner_id", "columns": ["owner_id"]},
        {"name": "idx_rentals_status", "columns": ["status"]},
        {"name": "idx_rentals_start_date", "columns": ["start_date"]},
        {"name": "idx_rentals_end_date", "columns": ["end_date"]}
    ],
    "auctions": [
        {"name": "idx_auctions_item_id", "columns": ["item_id"]},
        {"name": "idx_auctions_owner_id", "columns": ["owner_id"]},
        {"name": "idx_auctions_status", "columns": ["status"]},
        {"name": "idx_auctions_end_time", "columns": ["end_time"]}
    ],
    "bids": [
        {"name": "idx_bids_auction_id", "columns": ["auction_id"]},
        {"name": "idx_bids_bidder_id", "columns": ["bidder_id"]},
        {"name": "idx_bids_status", "columns": ["status"]}
    ],
    "agreements": [
        {"name": "idx_agreements_rental_id", "columns": ["rental_id"]},
        {"name": "idx_agreements_owner_id", "columns": ["owner_id"]},
        {"name": "idx_agreements_renter_id", "columns": ["renter_id"]},
        {"name": "idx_agreements_status", "columns": ["status"]}
    ],
    "fraud_alerts": [
        {"name": "idx_fraud_alerts_user_id", "columns": ["user_id"]},
        {"name": "idx_fraud_alerts_item_id", "columns": ["item_id"]},
        {"name": "idx_fraud_alerts_status", "columns": ["status"]},
        {"name": "idx_fraud_alerts_severity", "columns": ["severity"]}
    ],
    "moderation_alerts": [
        {"name": "idx_moderation_alerts_user_id", "columns": ["user_id"]},
        {"name": "idx_moderation_alerts_item_id", "columns": ["item_id"]},
        {"name": "idx_moderation_alerts_status", "columns": ["status"]},
        {"name": "idx_moderation_alerts_severity", "columns": ["severity"]}
    ]
}

def get_db_connection():
    """Create a database connection."""
    try:
        engine = create_engine(settings.get_database_url)
        return engine
    except Exception as e:
        logger.error(f"Error connecting to database: {e}")
        raise

def analyze_database(engine) -> Dict[str, Any]:
    """
    Analyze the database and return optimization recommendations.

    Args:
        engine: SQLAlchemy engine

    Returns:
        Dictionary with analysis results
    """
    results = {
        "missing_indexes": [],
        "table_statistics": {},
        "query_performance": [],
        "recommendations": []
    }

    inspector = inspect(engine)

    # Check for missing indexes
    for table_name, indexes in TABLE_INDEXES.items():
        try:
            existing_indexes = inspector.get_indexes(table_name)
            existing_index_names = [idx['name'] for idx in existing_indexes]

            for index in indexes:
                if index["name"] not in existing_index_names:
                    results["missing_indexes"].append({
                        "table": table_name,
                        "index_name": index["name"],
                        "columns": index["columns"]
                    })
        except Exception as e:
            logger.warning(f"Error checking indexes for table {table_name}: {e}")

    # Get table statistics
    with engine.connect() as conn:
        try:
            # Get table row counts
            for table_name in TABLE_INDEXES.keys():
                try:
                    # Use parameterized query to prevent SQL injection
                    count_result = conn.execute(
                        text("SELECT COUNT(*) FROM :table_name").bindparams(table_name=table_name)
                    ).scalar()
                    results["table_statistics"][table_name] = {
                        "row_count": count_result
                    }
                except Exception as e:
                    logger.warning(f"Error getting statistics for table {table_name}: {e}")

            # Check for tables that need VACUUM
            vacuum_results = conn.execute(text("""
                SELECT relname, n_dead_tup, n_live_tup
                FROM pg_stat_user_tables
                WHERE n_dead_tup > 0
                ORDER BY n_dead_tup DESC
            """)).fetchall()

            if vacuum_results:
                results["recommendations"].append({
                    "type": "vacuum",
                    "description": "Run VACUUM on tables with dead tuples",
                    "tables": [row[0] for row in vacuum_results]
                })

            # Check for tables that need ANALYZE
            analyze_results = conn.execute(text("""
                SELECT relname
                FROM pg_stat_user_tables
                WHERE last_analyze IS NULL OR last_analyze < NOW() - INTERVAL '1 day'
                ORDER BY last_analyze NULLS FIRST
            """)).fetchall()

            if analyze_results:
                results["recommendations"].append({
                    "type": "analyze",
                    "description": "Run ANALYZE on tables with outdated statistics",
                    "tables": [row[0] for row in analyze_results]
                })

        except Exception as e:
            logger.error(f"Error analyzing database: {e}")

    return results

def apply_optimizations(engine, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
    """
    Apply recommended optimizations to the database.

    Args:
        engine: SQLAlchemy engine
        analysis_results: Results from analyze_database

    Returns:
        Dictionary with optimization results
    """
    results = {
        "created_indexes": [],
        "vacuum_results": [],
        "analyze_results": [],
        "errors": []
    }

    with engine.connect() as conn:
        # Create missing indexes
        for index_info in analysis_results["missing_indexes"]:
            try:
                table_name = index_info["table"]
                index_name = index_info["index_name"]
                columns = index_info["columns"]

                # Create the index
                # PostgreSQL doesn't support parameterizing index names or table names,
                # but this is still safe as these values come from a predefined list, not user input
                column_list = ", ".join(columns)
                conn.execute(text(f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name} ({column_list})"))
                conn.commit()

                results["created_indexes"].append({
                    "table": table_name,
                    "index_name": index_name,
                    "columns": columns
                })

                logger.info(f"Created index {index_name} on table {table_name}")
            except Exception as e:
                error_msg = f"Error creating index {index_info['index_name']}: {e}"
                logger.error(error_msg)
                results["errors"].append(error_msg)

        # Run VACUUM on tables that need it
        for recommendation in analysis_results["recommendations"]:
            if recommendation["type"] == "vacuum":
                for table_name in recommendation["tables"]:
                    try:
                        # Use parameterized query to prevent SQL injection
                        conn.execute(text("VACUUM :table_name").bindparams(table_name=table_name))
                        results["vacuum_results"].append({
                            "table": table_name,
                            "status": "success"
                        })
                        logger.info(f"Vacuumed table {table_name}")
                    except Exception as e:
                        error_msg = f"Error vacuuming table {table_name}: {e}"
                        logger.error(error_msg)
                        results["errors"].append(error_msg)

            # Run ANALYZE on tables that need it
            elif recommendation["type"] == "analyze":
                for table_name in recommendation["tables"]:
                    try:
                        # Use parameterized query to prevent SQL injection
                        conn.execute(text("ANALYZE :table_name").bindparams(table_name=table_name))
                        results["analyze_results"].append({
                            "table": table_name,
                            "status": "success"
                        })
                        logger.info(f"Analyzed table {table_name}")
                    except Exception as e:
                        error_msg = f"Error analyzing table {table_name}: {e}"
                        logger.error(error_msg)
                        results["errors"].append(error_msg)

    return results

def main():
    """Main function to optimize the database."""
    parser = argparse.ArgumentParser(description="Optimize the RentUp PostgreSQL database")

    # Add arguments
    parser.add_argument("--analyze", action="store_true", help="Analyze database and suggest optimizations")
    parser.add_argument("--apply", action="store_true", help="Apply recommended optimizations")
    parser.add_argument("--all", action="store_true", help="Analyze and apply optimizations")

    args = parser.parse_args()

    # Default to analyze if no arguments provided
    if not (args.analyze or args.apply or args.all):
        args.analyze = True

    try:
        engine = get_db_connection()

        if args.analyze or args.all:
            logger.info("Analyzing database...")
            analysis_results = analyze_database(engine)

            # Print analysis results
            logger.info("Database Analysis Results:")
            logger.info(f"Missing indexes: {len(analysis_results['missing_indexes'])}")
            for idx in analysis_results["missing_indexes"]:
                logger.info(f"  - Table: {idx['table']}, Index: {idx['index_name']} on columns: {', '.join(idx['columns'])}")

            logger.info("Table statistics:")
            for table, stats in analysis_results["table_statistics"].items():
                logger.info(f"  - {table}: {stats['row_count']} rows")

            logger.info("Recommendations:")
            for rec in analysis_results["recommendations"]:
                logger.info(f"  - {rec['description']}: {', '.join(rec['tables'])}")

        if args.apply or args.all:
            if args.all:
                # If --all, use the analysis results from above
                pass
            elif not args.analyze:
                # If only --apply, run analysis first
                logger.info("Analyzing database before applying optimizations...")
                analysis_results = analyze_database(engine)

            logger.info("Applying optimizations...")
            optimization_results = apply_optimizations(engine, analysis_results)

            # Print optimization results
            logger.info("Optimization Results:")
            logger.info(f"Created indexes: {len(optimization_results['created_indexes'])}")
            for idx in optimization_results["created_indexes"]:
                logger.info(f"  - Created index {idx['index_name']} on table {idx['table']}")

            logger.info(f"Vacuumed tables: {len(optimization_results['vacuum_results'])}")
            for vac in optimization_results['vacuum_results']:
                logger.info(f"  - Vacuumed table {vac['table']}")

            logger.info(f"Analyzed tables: {len(optimization_results['analyze_results'])}")
            for ana in optimization_results['analyze_results']:
                logger.info(f"  - Analyzed table {ana['table']}")

            if optimization_results["errors"]:
                logger.warning(f"Errors during optimization: {len(optimization_results['errors'])}")
                for error in optimization_results["errors"]:
                    logger.warning(f"  - {error}")

        logger.info("Database optimization completed successfully!")

    except Exception as e:
        logger.error(f"Error optimizing database: {e}")
        raise

if __name__ == "__main__":
    main()
