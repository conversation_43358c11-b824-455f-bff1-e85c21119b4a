#!/usr/bin/env python3
"""
DEPRECATED: Script to add more sample data to the database.

⚠️  WARNING: This script has been refactored into a modular package.
    Please use 'add_more_data_refactored.py' instead.

This script has been kept for backward compatibility but will be removed
in a future version. The new modular approach provides better maintainability,
performance, and extensibility.

Migration Guide:
- Old: python add_more_data.py --all 100
- New: python add_more_data_refactored.py --all 100

The new script supports all the same command line arguments and provides
additional features like better error handling, progress tracking, and
modular architecture.
"""
import sys
from pathlib import Path
import argparse
import time

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import uuid
from datetime import datetime, timedelta, UTC
import logging
import json
import random

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('data_generation.log')
    ]
)
logger = logging.getLogger(__name__)

# Import the settings module
from app.core.config import settings

# Import tqdm for progress bars
try:
    from tqdm import tqdm
except ImportError:
    # If tqdm is not installed, create a simple replacement
    def tqdm(iterable, *args, **kwargs):
        return iterable

# Create a connection pool for better performance
from sqlalchemy.pool import QueuePool

# Use the database URL from settings with connection pooling
engine = create_engine(
    settings.DATABASE_URL,
    poolclass=QueuePool,
    pool_size=5,
    max_overflow=10,
    pool_timeout=30,
    pool_recycle=1800  # Recycle connections after 30 minutes
)

def add_more_items(count=25):
    """Add more items to the database.

    This function adds items (5 per category by default) to the database with
    random attributes, prices, and images. Each item is assigned to a random user.

    Args:
        count: Number of items to add (default: 25)
    """
    # Use the global engine with connection pooling
    start_time = time.time()

    # Define categories and subcategories
    categories = {
        "Electronics": ["Smartphones", "Laptops", "Cameras", "Audio Equipment", "Gaming Consoles"],
        "Furniture": ["Sofas", "Tables", "Chairs", "Beds", "Storage"],
        "Vehicles": ["Cars", "Motorcycles", "Bicycles", "Scooters", "Boats"],
        "Tools": ["Power Tools", "Hand Tools", "Garden Tools", "Construction Equipment", "Measuring Tools"],
        "Sports": ["Fitness Equipment", "Outdoor Gear", "Water Sports", "Winter Sports", "Team Sports"]
    }

    # Define conditions
    conditions = ["new", "like_new", "good", "fair", "poor"]

    # Define locations
    locations = ["New York", "Los Angeles", "Chicago", "Houston", "Phoenix", "Philadelphia", "San Antonio", "San Diego", "Dallas", "San Jose"]

    # Sample image URLs (placeholder)
    image_urls = [
        "https://images.unsplash.com/photo-1505740420928-5e560c06d30e",
        "https://images.unsplash.com/photo-1523275335684-37898b6baf30",
        "https://images.unsplash.com/photo-1526170375885-4d8ecf77b99f",
        "https://images.unsplash.com/photo-1485955900006-10f4d324d411",
        "https://images.unsplash.com/photo-1572635196237-14b3f281503f",
        "https://images.unsplash.com/photo-1581235720704-06d3acfcb36f",
        "https://images.unsplash.com/photo-1542291026-7eec264c27ff",
        "https://images.unsplash.com/photo-1560343090-f0409e92791a",
        "https://images.unsplash.com/photo-1496181133206-80ce9b88a853",
        "https://images.unsplash.com/photo-1507764923504-cd90bf7da772"
    ]

    # Item names and descriptions by category
    item_data = {
        "Electronics": [
            {"name": "iPhone 13 Pro", "description": "Latest iPhone model with 256GB storage and excellent camera."},
            {"name": "MacBook Pro 16", "description": "Powerful laptop with M1 Pro chip, perfect for professionals."},
            {"name": "Sony A7 III", "description": "Full-frame mirrorless camera with excellent low-light performance."},
            {"name": "Bose QuietComfort 45", "description": "Premium noise-cancelling headphones for immersive audio."},
            {"name": "PlayStation 5", "description": "Next-gen gaming console with fast SSD and ray tracing."}
        ],
        "Furniture": [
            {"name": "Leather Sectional Sofa", "description": "Comfortable L-shaped sofa with genuine leather upholstery."},
            {"name": "Solid Oak Dining Table", "description": "Beautiful handcrafted dining table that seats 6 people."},
            {"name": "Ergonomic Office Chair", "description": "Adjustable chair with lumbar support for all-day comfort."},
            {"name": "Queen Memory Foam Mattress", "description": "Premium mattress that adapts to your body for better sleep."},
            {"name": "Bookshelf with Storage", "description": "Modern bookshelf with additional storage compartments."}
        ],
        "Vehicles": [
            {"name": "Tesla Model 3", "description": "Electric sedan with autopilot features and long range."},
            {"name": "Harley-Davidson Street Glide", "description": "Powerful touring motorcycle with classic styling."},
            {"name": "Trek Domane SL 6", "description": "Carbon fiber road bike with electronic shifting."},
            {"name": "Xiaomi Mi Electric Scooter", "description": "Foldable electric scooter with 30km range."},
            {"name": "Sea-Doo GTI SE", "description": "3-person watercraft with 170hp engine for fun on the water."}
        ],
        "Tools": [
            {"name": "DeWalt Cordless Drill Set", "description": "20V MAX drill with multiple attachments and batteries."},
            {"name": "Craftsman Tool Set", "description": "Comprehensive 450-piece mechanics tool set with case."},
            {"name": "Honda Gas Lawn Mower", "description": "Self-propelled mower with variable speed control."},
            {"name": "Hilti Concrete Breaker", "description": "Professional-grade demolition hammer for construction."},
            {"name": "Fluke Digital Multimeter", "description": "Precision measuring tool for electrical work."}
        ],
        "Sports": [
            {"name": "Peloton Bike+", "description": "Interactive exercise bike with live and on-demand classes."},
            {"name": "Coleman 8-Person Tent", "description": "Spacious tent for family camping trips."},
            {"name": "Inflatable Stand-Up Paddleboard", "description": "Stable SUP board with paddle and pump included."},
            {"name": "Burton Custom Flying V Snowboard", "description": "All-mountain snowboard for intermediate to advanced riders."},
            {"name": "Wilson NCAA Official Basketball", "description": "Regulation size and weight basketball for indoor/outdoor use."}
        ]
    }

    with engine.connect() as conn:
        try:
            # Get all user IDs
            user_ids = [row[0] for row in conn.execute(text("SELECT id FROM users")).fetchall()]

            # Calculate total items to insert based on count parameter
            total_items = min(count, len(item_data) * 5)  # Limit to available item data
            items_per_category = total_items // len(item_data)

            logger.info(f"Preparing to insert {total_items} items ({items_per_category} per category)")

            # Prepare batch of items to insert
            items_to_insert = []

            # Use tqdm for progress tracking
            for category, items in tqdm(item_data.items(), desc="Processing categories"):
                for item in items[:items_per_category]:  # Limit items per category
                    item_id = str(uuid.uuid4())
                    subcategory = random.choice(categories[category])
                    condition = random.choice(conditions)
                    owner_id = random.choice(user_ids)
                    location = random.choice(locations)

                    # Generate random prices
                    value = random.randint(100, 2000)
                    daily_price = round(value * 0.01, 2)  # 1% of value per day
                    weekly_price = round(daily_price * 6, 2)  # 6 days price for 7 days
                    monthly_price = round(weekly_price * 3.5, 2)  # 3.5 weeks price for a month
                    deposit_amount = round(value * 0.2, 2)  # 20% of value as deposit

                    # Generate random image URLs (2-4 images per item)
                    num_images = random.randint(2, 4)
                    images = random.sample(image_urls, num_images)

                    # Generate random attributes based on category
                    attributes = {}
                    if category == "Electronics":
                        attributes = {
                            "brand": random.choice(["Apple", "Samsung", "Sony", "LG", "Dell"]),
                            "color": random.choice(["Black", "White", "Silver", "Blue", "Red"]),
                            "warranty": random.choice(["1 year", "2 years", "None"])
                        }
                    elif category == "Furniture":
                        attributes = {
                            "material": random.choice(["Wood", "Leather", "Fabric", "Metal", "Glass"]),
                            "color": random.choice(["Brown", "Black", "White", "Gray", "Beige"]),
                            "assembly_required": random.choice(["Yes", "No"])
                        }
                    elif category == "Vehicles":
                        attributes = {
                            "make": random.choice(["Toyota", "Honda", "Ford", "BMW", "Tesla"]),
                            "year": random.randint(2015, 2023),
                            "mileage": random.randint(0, 50000)
                        }
                    elif category == "Tools":
                        attributes = {
                            "brand": random.choice(["DeWalt", "Milwaukee", "Bosch", "Makita", "Craftsman"]),
                            "power_source": random.choice(["Cordless", "Corded", "Gas", "Manual"]),
                            "weight": f"{random.randint(1, 20)} lbs"
                        }
                    elif category == "Sports":
                        attributes = {
                            "brand": random.choice(["Nike", "Adidas", "Under Armour", "Wilson", "Coleman"]),
                            "size": random.choice(["Small", "Medium", "Large", "One Size"]),
                            "skill_level": random.choice(["Beginner", "Intermediate", "Advanced"])
                        }

                    items_to_insert.append({
                        "id": item_id,
                        "name": item["name"],
                        "description": item["description"],
                        "category": category,
                        "subcategory": subcategory,
                        "condition": condition,
                        "owner_id": owner_id,
                        "value": value,
                        "daily_price": daily_price,
                        "weekly_price": weekly_price,
                        "monthly_price": monthly_price,
                        "deposit_amount": deposit_amount,
                        "location": location,
                        "is_available": True,
                        "is_featured": random.random() < 0.2,  # 20% chance of being featured
                        "is_verified": random.random() < 0.5,  # 50% chance of being verified
                        "images": json.dumps(images),
                        "attributes": json.dumps(attributes),
                        "created_at": datetime.now(UTC) - timedelta(days=random.randint(1, 30)),
                        "updated_at": datetime.now(UTC)
                    })

            # Execute batch insert using executemany for better performance
            conn.execute(text("""
            INSERT INTO items (
                id, name, description, category, subcategory, condition, owner_id,
                value, daily_price, weekly_price, monthly_price, deposit_amount,
                location, is_available, is_featured, is_verified, images, attributes,
                created_at, updated_at
            ) VALUES (
                :id, :name, :description, :category, :subcategory, :condition, :owner_id,
                :value, :daily_price, :weekly_price, :monthly_price, :deposit_amount,
                :location, :is_available, :is_featured, :is_verified, :images, :attributes,
                :created_at, :updated_at
            )
            """), items_to_insert)

            # Commit the transaction
            conn.commit()

            # Calculate and log performance metrics
            end_time = time.time()
            elapsed_time = end_time - start_time
            items_per_second = len(items_to_insert) / elapsed_time

            logger.info(f"Added {len(items_to_insert)} items to the database in {elapsed_time:.2f} seconds ({items_per_second:.2f} items/sec)")
        except SQLAlchemyError as e:
            conn.rollback()
            logger.error(f"Database error adding items: {e}")
            raise
        except Exception as e:
            conn.rollback()
            logger.error(f"Error adding items: {e}")
            raise

def add_more_auctions():
    """Add more auctions to the database.

    This function adds 5 more auctions to the database, selecting items
    that are not already in auctions or rentals. Each auction is configured
    with random start and end times, reserve prices, and rental periods.
    """
    engine = create_engine(settings.DATABASE_URL)

    with engine.connect() as conn:
        try:
            # No need for user IDs in this function

            # Get all item IDs that are not already in auctions or rentals
            item_ids = [row[0] for row in conn.execute(text("""
                SELECT i.id FROM items i
                LEFT JOIN auctions a ON i.id = a.item_id
                LEFT JOIN rentals r ON i.id = r.item_id
                WHERE a.id IS NULL AND r.id IS NULL
                LIMIT 5
            """)).fetchall()]

            if not item_ids:
                logger.warning("No available items found for new auctions.")
                return

            # Get all item details in a single query for better performance
            items_data = {}
            if item_ids:
                items_result = conn.execute(text("""
                    SELECT id, owner_id, name, daily_price
                    FROM items
                    WHERE id IN :item_ids
                """), {"item_ids": tuple(item_ids)}).fetchall()

                for item in items_result:
                    items_data[item.id] = {
                        "owner_id": item.owner_id,
                        "name": item.name,
                        "daily_price": item.daily_price
                    }

            # Prepare batch of auctions to insert
            auctions_to_insert = []
            current_time = datetime.now(UTC)

            # Add 5 more auctions
            for item_id in item_ids:
                auction_id = str(uuid.uuid4())

                # Get item details from our cached data
                item_data = items_data[item_id]
                owner_id = item_data["owner_id"]
                item_name = item_data["name"]
                daily_price = item_data["daily_price"]

                # Generate auction details
                start_time = current_time + timedelta(hours=random.randint(1, 48))
                duration = random.randint(3, 7)  # 3 to 7 days
                end_time = start_time + timedelta(days=duration)

                # Rental period starts after auction ends
                rental_start_date = end_time + timedelta(days=1)
                rental_duration = random.randint(3, 14)  # 3 to 14 days
                rental_end_date = rental_start_date + timedelta(days=rental_duration)

                # Set reserve price and minimum increment
                reserve_price = daily_price * rental_duration * 0.8  # 80% of normal rental price
                min_increment = max(5.0, reserve_price * 0.05)  # 5% of reserve price or $5, whichever is higher

                # Add auction to batch
                auctions_to_insert.append({
                    "id": auction_id,
                    "item_id": item_id,
                    "owner_id": owner_id,
                    "title": f"Auction for {item_name}",
                    "description": f"Bid on this {item_name} for a {rental_duration}-day rental starting on {rental_start_date.strftime('%Y-%m-%d')}.",
                    "start_time": start_time,
                    "end_time": end_time,
                    "reserve_price": reserve_price,
                    "min_increment": min_increment,
                    "current_highest_bid": 0.0,
                    "current_highest_bidder_id": None,
                    "status": "upcoming" if start_time > current_time else "active",
                    "rental_start_date": rental_start_date,
                    "rental_end_date": rental_end_date,
                    "created_at": current_time,
                    "updated_at": current_time
                })

            # Execute batch insert
            if auctions_to_insert:
                conn.execute(text("""
                INSERT INTO auctions (
                    id, item_id, owner_id, title, description, start_time, end_time,
                    reserve_price, min_increment, current_highest_bid, current_highest_bidder_id,
                    status, rental_start_date, rental_end_date, created_at, updated_at
                ) VALUES (
                    :id, :item_id, :owner_id, :title, :description, :start_time, :end_time,
                    :reserve_price, :min_increment, :current_highest_bid, :current_highest_bidder_id,
                    :status, :rental_start_date, :rental_end_date, :created_at, :updated_at
                )
                """), auctions_to_insert)

            conn.commit()
            logger.info("Added 5 more auctions to the database.")
        except Exception as e:
            conn.rollback()
            logger.error(f"Error adding more auctions: {e}")
            raise

def add_more_bids():
    """Add more bids to the database.

    This function adds 15 more bids to active auctions in the database.
    Each bid is assigned to a random user (not the auction owner) and
    has an amount higher than the current highest bid or reserve price.
    """
    engine = create_engine(settings.DATABASE_URL)

    with engine.connect() as conn:
        try:
            # Get all user IDs
            user_ids = [row[0] for row in conn.execute(text("SELECT id FROM users")).fetchall()]

            # Get active auctions
            auctions = conn.execute(text("""
                SELECT id, reserve_price, min_increment, current_highest_bid
                FROM auctions
                WHERE status = 'active'
            """)).fetchall()

            if not auctions:
                logger.warning("No active auctions found for new bids.")
                return

            # Dictionary to track highest bids for each auction
            auction_highest_bids = {}

            # Prepare batch of bids to insert
            bids_to_insert = []
            auction_updates = []
            outbid_updates = []

            # Add 15 bids across the active auctions
            for i in range(15):
                bid_id = str(uuid.uuid4())

                # Select a random auction
                auction = random.choice(auctions)
                auction_id = auction.id
                reserve_price = auction.reserve_price
                min_increment = auction.min_increment
                current_highest_bid = auction.current_highest_bid

                # Get auction owner to exclude from bidders
                owner_id = conn.execute(text("SELECT owner_id FROM auctions WHERE id = :auction_id"),
                                      {"auction_id": auction_id}).scalar()

                # Get a bidder (not the owner)
                available_bidders = [uid for uid in user_ids if uid != owner_id]
                bidder_id = random.choice(available_bidders)

                # Get the current highest bid (either from our tracking dictionary or from the database)
                tracked_highest_bid = auction_highest_bids.get(auction_id, current_highest_bid)
                highest_bid = max(tracked_highest_bid, current_highest_bid)

                # Calculate bid amount
                if highest_bid < reserve_price:
                    # First bid should be at least the reserve price
                    bid_amount = reserve_price + (random.random() * min_increment)
                else:
                    # Subsequent bids should be higher than current highest bid
                    bid_amount = highest_bid + (random.random() * min_increment * 2) + min_increment

                bid_amount = round(bid_amount, 2)
                placed_at = datetime.now(UTC) - timedelta(minutes=random.randint(1, 60))
                current_time = datetime.now(UTC)

                # Add bid to batch
                bids_to_insert.append({
                    "id": bid_id,
                    "auction_id": auction_id,
                    "bidder_id": bidder_id,
                    "amount": bid_amount,
                    "status": "active",
                    "placed_at": placed_at,
                    "created_at": current_time,
                    "updated_at": current_time
                })

                # If this is the highest bid, prepare auction update
                if bid_amount > current_highest_bid:
                    auction_updates.append({
                        "bid_amount": bid_amount,
                        "bidder_id": bidder_id,
                        "updated_at": current_time,
                        "auction_id": auction_id
                    })

                    # If there was a previous highest bid, prepare outbid update
                    if current_highest_bid > 0:
                        outbid_updates.append({
                            "updated_at": current_time,
                            "auction_id": auction_id,
                            "bid_id": bid_id
                        })

                    # Update the auction in our local list for future iterations
                    # Create a simple dictionary to track the highest bid for each auction
                    for idx, a in enumerate(auctions):
                        if a.id == auction_id:
                            # Create a new Row object with updated values
                            # Since we can't easily create a new Row object, we'll just track the highest bid separately
                            auction_highest_bids[auction_id] = bid_amount
                            break

            # Execute batch inserts and updates
            if bids_to_insert:
                conn.execute(text("""
                INSERT INTO bids (
                    id, auction_id, bidder_id, amount, status, placed_at, created_at, updated_at
                ) VALUES (
                    :id, :auction_id, :bidder_id, :amount, :status, :placed_at, :created_at, :updated_at
                )
                """), bids_to_insert)

            # Update auctions with highest bids
            for update in auction_updates:
                conn.execute(text("""
                UPDATE auctions
                SET current_highest_bid = :bid_amount,
                    current_highest_bidder_id = :bidder_id,
                    updated_at = :updated_at
                WHERE id = :auction_id
                """), update)

            # Update previous highest bids to outbid status
            for update in outbid_updates:
                conn.execute(text("""
                UPDATE bids
                SET status = 'outbid', updated_at = :updated_at
                WHERE auction_id = :auction_id AND status = 'active' AND id != :bid_id
                """), update)

            conn.commit()
            logger.info("Added 15 more bids to the database.")
        except Exception as e:
            conn.rollback()
            logger.error(f"Error adding more bids: {e}")
            raise

def add_more_agreements():
    """Add more agreements to the database."""
    engine = create_engine(settings.DATABASE_URL)

    with engine.connect() as conn:
        try:
            # Get all rentals that don't have agreements yet
            rental_data = conn.execute(text("""
                SELECT r.id, r.owner_id, r.renter_id, r.item_id, r.start_date, r.end_date, r.status, i.name
                FROM rentals r
                JOIN items i ON r.item_id = i.id
                LEFT JOIN agreements a ON r.id = a.rental_id
                WHERE a.id IS NULL
                LIMIT 10
            """)).fetchall()

            rental_ids = [rental.id for rental in rental_data]

            if not rental_ids:
                logger.warning("No available rentals found for new agreements.")
                return

            # Prepare batch of agreements to insert
            agreements_to_insert = []
            current_time = datetime.now(UTC)

            # Add agreements for rentals
            for rental in rental_data:
                rental_id = rental.id
                owner_id = rental.owner_id
                renter_id = rental.renter_id
                item_name = rental.name
                start_date = rental.start_date
                end_date = rental.end_date
                rental_status = rental.status

                # Calculate rental duration in days
                rental_duration = (end_date - start_date).days

                # Generate agreement content
                agreement_content = f"Rental Agreement for {item_name}\n\n"
                agreement_content += f"This agreement is made between the owner and the renter for the rental of {item_name}.\n"
                agreement_content += f"Rental period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')} ({rental_duration} days)\n\n"
                agreement_content += "Terms and Conditions:\n"
                agreement_content += "1. The renter agrees to pay the rental fee in full before the rental period begins.\n"
                agreement_content += "2. The renter agrees to return the item in the same condition as received.\n"
                agreement_content += "3. The owner reserves the right to charge additional fees for damages.\n"
                agreement_content += "4. The renter agrees to use the item for its intended purpose only.\n"

                # Determine agreement status based on rental status
                if rental_status == "pending":
                    agreement_status = "draft"
                elif rental_status == "confirmed":
                    agreement_status = "pending"
                else:
                    agreement_status = "active"

                # Add agreement to batch
                agreements_to_insert.append({
                    "id": str(uuid.uuid4()),
                    "rental_id": rental_id,
                    "owner_id": owner_id,
                    "renter_id": renter_id,
                    "agreement_type": "standard",
                    "content": agreement_content,  # Use the actual content instead of a hash
                    "status": agreement_status,
                    "created_at": current_time - timedelta(days=random.randint(1, 5)),
                    "updated_at": current_time
                })

            # Execute batch insert
            if agreements_to_insert:
                conn.execute(text("""
                INSERT INTO agreements (
                    id, rental_id, owner_id, renter_id, agreement_type,
                    content, status, created_at, updated_at
                ) VALUES (
                    :id, :rental_id, :owner_id, :renter_id, :agreement_type,
                    :content, :status, :created_at, :updated_at
                )
                """), agreements_to_insert)

            conn.commit()
            logger.info(f"Added {len(rental_ids)} more agreements to the database.")
        except Exception as e:
            conn.rollback()
            logger.error(f"Error adding more agreements: {e}")
            raise

def add_more_rentals():
    """Add more rentals to the database.

    This function adds 10 more rental records to the database, selecting items
    that are not already rented and creating rental agreements for them.
    """
    engine = create_engine(settings.DATABASE_URL)

    with engine.connect() as conn:
        try:
            # Get all user IDs
            user_ids = [row[0] for row in conn.execute(text("SELECT id FROM users")).fetchall()]

            # Get all item IDs that are not already in rentals
            item_ids = [row[0] for row in conn.execute(text("""
                SELECT i.id FROM items i
                LEFT JOIN rentals r ON i.id = r.item_id
                WHERE r.id IS NULL
                LIMIT 10
            """)).fetchall()]

            if not item_ids:
                logger.warning("No available items found for new rentals.")
                return

            # Get all item details in a single query for better performance
            items_data = {}
            if item_ids:
                items_result = conn.execute(text("""
                    SELECT id, owner_id, daily_price
                    FROM items
                    WHERE id IN :item_ids
                """), {"item_ids": tuple(item_ids)}).fetchall()

                for item in items_result:
                    items_data[item.id] = {
                        "owner_id": item.owner_id,
                        "daily_price": item.daily_price
                    }

            # Prepare batch of rentals to insert
            rentals_to_insert = []
            current_time = datetime.now(UTC)

            # Add 10 more rentals
            for item_id in item_ids:
                rental_id = str(uuid.uuid4())

                # Get item details from our cached data
                item_data = items_data[item_id]
                owner_id = item_data["owner_id"]
                daily_price = item_data["daily_price"]

                # Get a renter (not the owner)
                available_renters = [uid for uid in user_ids if uid != owner_id]
                renter_id = random.choice(available_renters)

                # Generate random dates
                start_date = current_time + timedelta(days=random.randint(1, 14))
                duration = random.randint(3, 14)  # 3 to 14 days
                end_date = start_date + timedelta(days=duration)
                total_price = daily_price * duration

                # Determine status based on dates
                if start_date > current_time + timedelta(days=7):
                    status = "pending"
                    payment_status = "pending"
                    deposit_paid = False
                elif start_date > current_time:
                    status = "confirmed"
                    payment_status = "paid"
                    deposit_paid = True
                elif end_date > current_time:
                    status = "active"
                    payment_status = "paid"
                    deposit_paid = True
                else:
                    status = "completed"
                    payment_status = "paid"
                    deposit_paid = True

                # Add rental to batch
                rentals_to_insert.append({
                    "id": rental_id,
                    "item_id": item_id,
                    "renter_id": renter_id,
                    "owner_id": owner_id,
                    "start_date": start_date,
                    "end_date": end_date,
                    "total_price": total_price,
                    "status": status,
                    "payment_status": payment_status,
                    "payment_intent_id": f"pi_{uuid.uuid4()}" if payment_status == "paid" else None,
                    "notes": f"Notes for rental of item {item_id}" if random.random() < 0.5 else None,
                    "deposit_paid": deposit_paid,
                    "deposit_returned": status == "completed" and random.random() < 0.8,
                    "created_at": current_time - timedelta(days=random.randint(1, 10)),
                    "updated_at": current_time
                })

            # Execute batch insert
            if rentals_to_insert:
                conn.execute(text("""
                INSERT INTO rentals (
                    id, item_id, renter_id, owner_id, start_date, end_date,
                    total_price, status, payment_status, payment_intent_id, notes,
                    deposit_paid, deposit_returned, created_at, updated_at
                ) VALUES (
                    :id, :item_id, :renter_id, :owner_id, :start_date, :end_date,
                    :total_price, :status, :payment_status, :payment_intent_id, :notes,
                    :deposit_paid, :deposit_returned, :created_at, :updated_at
                )
                """), rentals_to_insert)

            conn.commit()
            logger.info(f"Added {len(item_ids)} more rentals to the database.")
        except Exception as e:
            conn.rollback()
            logger.error(f"Error adding more rentals: {e}")
            raise

def add_fraud_alerts():
    """Add more fraud alerts to the database.

    This function adds 5 more fraud alerts to the database, selecting random users,
    items, auctions, and rentals to associate with the alerts.
    """
    engine = create_engine(settings.DATABASE_URL)

    with engine.connect() as conn:
        try:
            # Get all user IDs
            user_ids = [row[0] for row in conn.execute(text("SELECT id FROM users")).fetchall()]

            # Get all item IDs
            item_ids = [row[0] for row in conn.execute(text("SELECT id FROM items")).fetchall()]

            # Get all auction IDs
            auction_ids = [row[0] for row in conn.execute(text("SELECT id FROM auctions")).fetchall()]

            # We don't need rental IDs since the fraud_alerts table doesn't have a rental_id column

            # Alert types
            alert_types = ["suspicious_user", "suspicious_item", "suspicious_auction",
                          "suspicious_rental", "suspicious_login", "suspicious_payment"]
            # Severity is determined by risk score, not needed as a separate list
            statuses = ["new", "investigating", "resolved", "false_positive"]

            # Prepare batch of fraud alerts to insert
            alerts_to_insert = []
            current_time = datetime.now(UTC)

            # Add 5 more fraud alerts
            for i in range(5):
                alert_id = str(uuid.uuid4())

                # Randomly select alert type and associated entity
                alert_type = random.choice(alert_types)
                user_id = random.choice(user_ids) if alert_type in ["suspicious_user", "suspicious_login"] else None
                item_id = random.choice(item_ids) if alert_type == "suspicious_item" else None
                auction_id = random.choice(auction_ids) if alert_type == "suspicious_auction" else None
                # Note: The fraud_alerts table doesn't have a rental_id column, so we don't need to set it

                # Generate risk score and severity
                risk_score = round(random.uniform(0.1, 0.9), 2)
                severity = "low" if risk_score < 0.3 else ("medium" if risk_score < 0.6 else
                          ("high" if risk_score < 0.8 else "critical"))

                # Generate status and resolution details
                status = random.choices(statuses, weights=[0.4, 0.3, 0.2, 0.1])[0]
                resolved_at = current_time if status in ["resolved", "false_positive"] else None
                resolved_by = random.choice(user_ids) if resolved_at else None

                # Generate alert details
                details = {
                    "ip_address": f"192.168.{random.randint(1, 255)}.{random.randint(1, 255)}",
                    "device_id": f"device_{uuid.uuid4().hex[:8]}",
                    "timestamp": (current_time - timedelta(hours=random.randint(1, 24))).isoformat(),
                    "confidence": risk_score,
                    "triggers": random.sample(["unusual_location", "rapid_transactions",
                                             "multiple_accounts", "suspicious_pattern",
                                             "unusual_behavior"], k=random.randint(1, 3))
                }

                # Add fraud alert to batch
                alerts_to_insert.append({
                    "id": alert_id,
                    "user_id": user_id,
                    "item_id": item_id,
                    "auction_id": auction_id,
                    "alert_type": alert_type,
                    "severity": severity,
                    "risk_score": risk_score,
                    "details": json.dumps(details),
                    "status": status,
                    "created_at": current_time - timedelta(days=random.randint(1, 7)),
                    "resolved_at": resolved_at,
                    "resolved_by": resolved_by
                })

            # Execute batch insert
            if alerts_to_insert:
                conn.execute(text("""
                INSERT INTO fraud_alerts (
                    id, user_id, item_id, auction_id, alert_type, severity,
                    risk_score, details, status, created_at, resolved_at, resolved_by
                ) VALUES (
                    :id, :user_id, :item_id, :auction_id, :alert_type, :severity,
                    :risk_score, :details, :status, :created_at, :resolved_at, :resolved_by
                )
                """), alerts_to_insert)

            conn.commit()
            logger.info("Added 5 more fraud alerts to the database.")
        except Exception as e:
            conn.rollback()
            logger.error(f"Error adding more fraud alerts: {e}")
            raise

def add_data_with_progress(func, count, description):
    """
    Execute a data addition function with progress reporting.

    Args:
        func: Function to execute
        count: Number of times to execute
        description: Description for logging
    """
    start_time = time.time()
    logger.info(f"Adding {count} {description}...")

    try:
        for i in range(count):
            func()
            # Log progress for large batches
            if (i + 1) % 10 == 0 or i + 1 == count:
                elapsed = time.time() - start_time
                logger.info(f"Progress: {i + 1}/{count} {description} added ({elapsed:.2f}s elapsed)")
    except SQLAlchemyError as e:
        logger.error(f"Database error adding {description}: {e}")
        raise
    except Exception as e:
        logger.error(f"Error adding {description}: {e}")
        raise

def main():
    """Main function to add more data to the database."""
    # Print deprecation warning
    print("⚠️  WARNING: This script is DEPRECATED!")
    print("Please use 'add_more_data_refactored.py' instead.")
    print("The new script provides better performance, error handling, and modularity.")
    print("This script will be removed in a future version.")
    print("")

    # Ask user if they want to continue
    response = input("Do you want to continue with the deprecated script? (y/N): ")
    if response.lower() not in ['y', 'yes']:
        print("Exiting. Please use 'add_more_data_refactored.py' instead.")
        sys.exit(0)

    parser = argparse.ArgumentParser(description="Add sample data to the RentUp database")

    # Add arguments for each data type
    parser.add_argument("--items", type=int, help="Number of items to add")
    parser.add_argument("--auctions", type=int, help="Number of auctions to add")
    parser.add_argument("--bids", type=int, help="Number of bids to add")
    parser.add_argument("--rentals", type=int, help="Number of rentals to add")
    parser.add_argument("--agreements", type=int, help="Number of agreements to add")
    parser.add_argument("--fraud-alerts", type=int, help="Number of fraud alerts to add")
    parser.add_argument("--all", type=int, help="Add proportional data for all types")

    args = parser.parse_args()

    # If --all is specified, calculate proportional amounts
    if args.all:
        item_count = args.all
        auction_count = max(1, int(item_count * 0.2))  # 20% of items become auctions
        bid_count = max(1, int(auction_count * 3))     # 3 bids per auction on average
        rental_count = max(1, int(item_count * 0.4))   # 40% of items get rented
        agreement_count = rental_count                 # One agreement per rental
        fraud_alert_count = max(1, int(item_count * 0.1))  # 10% of items get fraud alerts
    else:
        # Use specified values or defaults
        item_count = args.items or 25
        auction_count = args.auctions or 5
        bid_count = args.bids or 15
        rental_count = args.rentals or 10
        agreement_count = args.agreements or 10
        fraud_alert_count = args.fraud_alerts or 5

    try:
        logger.info("Starting to add more data to the database...")
        total_start_time = time.time()

        # Add items - use the optimized function with count parameter
        if item_count > 0:
            add_more_items(count=item_count)
            logger.info(f"Added {item_count} items to the database")

        # Add auctions
        if auction_count > 0:
            # Use the add_data_with_progress helper for better reporting
            add_data_with_progress(
                add_more_auctions,
                auction_count // 5 + (1 if auction_count % 5 > 0 else 0),
                "auction batches"
            )
            logger.info(f"Added approximately {auction_count} auctions to the database")

        # Add bids
        if bid_count > 0:
            add_data_with_progress(
                add_more_bids,
                bid_count // 15 + (1 if bid_count % 15 > 0 else 0),
                "bid batches"
            )
            logger.info(f"Added approximately {bid_count} bids to the database")

        # Add rentals
        if rental_count > 0:
            add_data_with_progress(
                add_more_rentals,
                rental_count // 10 + (1 if rental_count % 10 > 0 else 0),
                "rental batches"
            )
            logger.info(f"Added approximately {rental_count} rentals to the database")

        # Add agreements
        if agreement_count > 0:
            add_data_with_progress(
                add_more_agreements,
                agreement_count // 10 + (1 if agreement_count % 10 > 0 else 0),
                "agreement batches"
            )
            logger.info(f"Added approximately {agreement_count} agreements to the database")

        # Add fraud alerts
        if fraud_alert_count > 0:
            add_data_with_progress(
                add_fraud_alerts,
                fraud_alert_count // 5 + (1 if fraud_alert_count % 5 > 0 else 0),
                "fraud alert batches"
            )
            logger.info(f"Added approximately {fraud_alert_count} fraud alerts to the database")

        total_elapsed = time.time() - total_start_time
        logger.info(f"Successfully added all data to the database in {total_elapsed:.2f} seconds!")

    except Exception as e:
        logger.error(f"Error adding data: {e}")
        raise

if __name__ == "__main__":
    main()
