#!/usr/bin/env python3
"""
Run Phase 3 tests and generate test results.
"""
import os
import sys
import subprocess
import datetime
from pathlib import Path

# Define paths
BACKEND_DIR = Path(__file__).parent
TEST_RESULTS_DIR = Path("testResults/phase3")
TEST_RESULTS_DIR.mkdir(parents=True, exist_ok=True)

# Test result file paths
FULL_TEST_RESULT_PATH = TEST_RESULTS_DIR / "phase3_full_testResult.md"


def run_tests():
    """Run the Phase 3 tests"""
    print("Running Phase 3 tests...")
    
    # Run pytest with the Phase 3 test file
    result = subprocess.run(
        ["pytest", "-xvs", "app/tests/test_phase3.py"],
        cwd=BACKEND_DIR,
        capture_output=True,
        text=True
    )
    
    # Save the test output
    with open(TEST_RESULTS_DIR / "phase3_test_output.txt", "w") as f:
        f.write(result.stdout)
        f.write("\n\n")
        f.write(result.stderr)
    
    # Check if the tests passed
    if result.returncode == 0:
        print("✅ All Phase 3 tests passed!")
    else:
        print("❌ Some Phase 3 tests failed. See test results for details.")
    
    # Return the test result
    return result.returncode == 0


def update_phase_status(passed):
    """Update the Phase 3 status in the development guide"""
    dev_phase_path = Path("docs/dev_guide/devPhase3.md")
    
    if not dev_phase_path.exists():
        print(f"Warning: {dev_phase_path} not found. Could not update phase status.")
        return
    
    # Read the current content
    with open(dev_phase_path, "r") as f:
        content = f.read()
    
    # Update the status
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    status_line = f"**Status:** {'Completed' if passed else 'In Progress'} (Last tested: {timestamp})"
    
    if "**Status:**" in content:
        # Replace the existing status line
        lines = content.split("\n")
        for i, line in enumerate(lines):
            if "**Status:**" in line:
                lines[i] = status_line
                break
        updated_content = "\n".join(lines)
    else:
        # Add the status line after the title
        if "# Phase 3" in content:
            updated_content = content.replace("# Phase 3", f"# Phase 3\n\n{status_line}")
        else:
            # Just append it at the beginning
            updated_content = f"{status_line}\n\n{content}"
    
    # Write the updated content
    with open(dev_phase_path, "w") as f:
        f.write(updated_content)
    
    print(f"Updated phase status in {dev_phase_path}")


def update_tasks_status(passed):
    """Update the Phase 3 tasks status"""
    tasks_path = Path("docs/dev_guide/devPhase3_tasks.md")
    
    if not tasks_path.exists():
        print(f"Warning: {tasks_path} not found. Could not update tasks status.")
        return
    
    # Read the current content
    with open(tasks_path, "r") as f:
        content = f.read()
    
    # Update the status of the tasks for parts 4, 5, and 6
    lines = content.split("\n")
    updated_lines = []
    
    for line in lines:
        # Check if the line is a task for parts 4, 5, or 6
        if any(f"Part {i}:" in line for i in [4, 5, 6]) and "[ ]" in line:
            # Mark the task as completed
            updated_lines.append(line.replace("[ ]", "[x]"))
        else:
            updated_lines.append(line)
    
    updated_content = "\n".join(updated_lines)
    
    # Write the updated content
    with open(tasks_path, "w") as f:
        f.write(updated_content)
    
    print(f"Updated tasks status in {tasks_path}")


def main():
    """Main function"""
    # Run the tests
    passed = run_tests()
    
    # Update the phase and tasks status
    if passed:
        update_phase_status(passed)
        update_tasks_status(passed)
    
    # Return the exit code
    return 0 if passed else 1


if __name__ == "__main__":
    sys.exit(main())
