#!/usr/bin/env python3
"""
Test Optimization Script
Optimizes test configurations and fixes common test issues
"""

import os
import json
import re
import subprocess
from pathlib import Path
from typing import Dict, List, Set

class TestOptimizer:
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.frontend_path = self.project_root / "frontend"
        self.backend_path = self.project_root / "backend"
        
    def optimize_jest_config(self):
        """Optimize Jest configuration for better performance."""
        jest_config_path = self.frontend_path / "jest.config.js"
        
        if not jest_config_path.exists():
            return
            
        print("🔧 Optimizing Jest configuration...")
        
        # Read current config
        with open(jest_config_path, 'r') as f:
            content = f.read()
        
        # Enhanced Jest configuration
        optimized_config = '''module.exports = {
  testEnvironment: 'jsdom',
  transform: {
    '^.+\\\\.(ts|tsx|js|jsx)$': ['babel-jest', { 
      presets: [
        ['@babel/preset-env', { targets: { node: 'current' } }],
        ['@babel/preset-react', { runtime: 'automatic' }],
        '@babel/preset-typescript'
      ] 
    }],
  },
  moduleNameMapper: {
    '\\\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '\\\\.(jpg|jpeg|png|gif|webp|svg)$': '<rootDir>/__mocks__/fileMock.js',
    '^msw/node$': '<rootDir>/node_modules/msw/lib/node/index.js',
    '^msw/browser$': '<rootDir>/node_modules/msw/lib/browser/index.js',
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@components/(.*)$': '<rootDir>/src/components/$1',
    '^@pages/(.*)$': '<rootDir>/src/pages/$1',
    '^@hooks/(.*)$': '<rootDir>/src/hooks/$1',
    '^@services/(.*)$': '<rootDir>/src/services/$1',
    '^@utils/(.*)$': '<rootDir>/src/utils/$1',
    '^@contexts/(.*)$': '<rootDir>/src/contexts/$1',
  },
  setupFilesAfterEnv: ['<rootDir>/src/testing/setupTests.ts'],
  testPathIgnorePatterns: [
    '/node_modules/', 
    '/dist/', 
    '/tests/testscripts/',
    '/cypress/',
    '/playwright-report/',
    '/test-results/'
  ],
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/main.tsx',
    '!src/vite-env.d.ts',
    '!src/**/*.stories.{js,jsx,ts,tsx}',
    '!src/testing/**/*',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  // Performance optimizations
  testTimeout: 15000,
  maxWorkers: '50%',
  cache: true,
  clearMocks: true,
  restoreMocks: true,
  
  // Better error reporting
  verbose: false,
  bail: false,
  errorOnDeprecated: true,
  
  // Watch mode optimizations
  watchPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/coverage/',
    '/.git/',
    '/test-results/',
    '/playwright-report/'
  ],
};'''
        
        # Write optimized config
        with open(jest_config_path, 'w') as f:
            f.write(optimized_config)
        
        print("✅ Jest configuration optimized")
    
    def fix_common_test_issues(self):
        """Fix common test issues found in the codebase."""
        print("🔧 Fixing common test issues...")
        
        # Fix React import issues
        self._fix_react_imports()
        
        # Fix hook dependency issues
        self._fix_hook_dependencies()
        
        # Fix context provider issues
        self._fix_context_providers()
        
        print("✅ Common test issues fixed")
    
    def _fix_react_imports(self):
        """Add missing React imports to test files."""
        test_files = list(self.frontend_path.rglob("*.test.{ts,tsx,js,jsx}"))
        test_files.extend(list(self.frontend_path.rglob("*.spec.{ts,tsx,js,jsx}")))
        
        for test_file in test_files:
            try:
                with open(test_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check if React is imported and JSX is used
                has_react_import = re.search(r'import.*React.*from.*[\'"]react[\'"]', content)
                has_jsx = re.search(r'<\w+', content)
                
                if has_jsx and not has_react_import:
                    # Add React import at the top
                    lines = content.split('\n')
                    import_index = 0
                    
                    # Find the right place to insert the import
                    for i, line in enumerate(lines):
                        if line.strip().startswith('import'):
                            import_index = i
                            break
                    
                    lines.insert(import_index, "import React from 'react';")
                    
                    with open(test_file, 'w', encoding='utf-8') as f:
                        f.write('\n'.join(lines))
                        
            except (IOError, UnicodeDecodeError):
                continue
    
    def _fix_hook_dependencies(self):
        """Fix useEffect and other hook dependency issues."""
        component_files = list(self.frontend_path.rglob("src/**/*.{ts,tsx}"))
        
        for comp_file in component_files:
            try:
                with open(comp_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Look for useEffect with missing dependencies
                useeffect_pattern = r'useEffect\s*\(\s*\(\s*\)\s*=>\s*{[^}]*},\s*\[\s*\]\s*\)'
                
                if re.search(useeffect_pattern, content):
                    # This is a complex fix that would require AST parsing
                    # For now, just flag it for manual review
                    print(f"⚠️  Manual review needed for useEffect dependencies in: {comp_file}")
                        
            except (IOError, UnicodeDecodeError):
                continue
    
    def _fix_context_providers(self):
        """Ensure test files have proper context providers."""
        test_provider_content = '''import React from 'react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from '@/contexts/AuthContext';
import { ToastProvider } from '@/contexts/ToastContext';

// Create a test query client
const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      cacheTime: 0,
    },
  },
});

export const TestProviders: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = createTestQueryClient();
  
  return (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <ToastProvider>
            {children}
          </ToastProvider>
        </AuthProvider>
      </QueryClientProvider>
    </BrowserRouter>
  );
};

export default TestProviders;'''
        
        test_providers_path = self.frontend_path / "src/testing/TestProviders.tsx"
        
        # Ensure the testing directory exists
        test_providers_path.parent.mkdir(exist_ok=True)
        
        # Write the enhanced test providers
        with open(test_providers_path, 'w') as f:
            f.write(test_provider_content)
    
    def optimize_playwright_config(self):
        """Optimize Playwright configuration."""
        print("🔧 Optimizing Playwright configuration...")
        
        # The configuration was already optimized in the main script
        # Just ensure it's using the latest best practices
        
        playwright_config = self.frontend_path / "playwright.config.ts"
        if playwright_config.exists():
            print("✅ Playwright configuration already optimized")
        else:
            print("⚠️  Playwright configuration not found")
    
    def create_test_utilities(self):
        """Create useful test utilities."""
        print("🔧 Creating test utilities...")
        
        # Custom render function
        render_utils_content = '''import React from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { TestProviders } from './TestProviders';

// Custom render function that includes providers
const customRender = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: TestProviders, ...options });

// Re-export everything
export * from '@testing-library/react';

// Override render method
export { customRender as render };'''
        
        render_utils_path = self.frontend_path / "src/testing/utils/renderUtils.tsx"
        render_utils_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(render_utils_path, 'w') as f:
            f.write(render_utils_content)
        
        # Mock utilities
        mock_utils_content = '''// Common mocks for testing
export const mockUseIsMobile = (isMobile: boolean = false) => {
  jest.mock('@/hooks/useMediaQuery', () => ({
    useIsMobile: () => isMobile,
  }));
};

export const mockAuthContext = (authState: any = {}) => {
  jest.mock('@/contexts/AuthContext', () => ({
    useAuth: () => ({
      user: null,
      isAuthenticated: false,
      login: jest.fn(),
      logout: jest.fn(),
      ...authState,
    }),
  }));
};

export const mockToastContext = () => {
  jest.mock('@/contexts/ToastContext', () => ({
    useToast: () => ({
      showToast: jest.fn(),
      hideToast: jest.fn(),
    }),
  }));
};

export const mockApiService = () => {
  jest.mock('@/services/apiClient', () => ({
    apiClient: {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
    },
  }));
};'''
        
        mock_utils_path = self.frontend_path / "src/testing/utils/mockUtils.ts"
        
        with open(mock_utils_path, 'w') as f:
            f.write(mock_utils_content)
        
        print("✅ Test utilities created")
    
    def run_optimization(self):
        """Run all optimizations."""
        print("🚀 Starting test optimization...")
        print("=" * 50)
        
        self.optimize_jest_config()
        self.fix_common_test_issues()
        self.optimize_playwright_config()
        self.create_test_utilities()
        
        print("=" * 50)
        print("✅ Test optimization complete!")
        print("\n📋 Next steps:")
        print("1. Run tests to verify improvements")
        print("2. Update test files to use new utilities")
        print("3. Review and fix any remaining issues")

def main():
    optimizer = TestOptimizer()
    optimizer.run_optimization()

if __name__ == "__main__":
    main()
