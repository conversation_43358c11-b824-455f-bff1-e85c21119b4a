# RentUp

A modern cross-platform rental marketplace connecting people who want to rent, rent-to-buy, or auction items. RentUp supports a wide range of categories including physical items, spaces, digital products, and services.

## Source of Truth Documentation

The following documents serve as the authoritative source of truth for the RentUp project. When working on any part of the project, always refer to these documents first to ensure consistency and alignment with project standards.

### Core Documentation
- **[README.md](./README.md)**: Project overview, tech stack, features, and roadmap
- **[dirStructure.md](./dirStructure.md)**: Directory structure and organization
- **[fileRelations.md](./fileRelations.md)**: Relationships between files and components
- **[testMethod.md](./testMethod.md)**: Testing methodology and standards
- **[build.md](./build.md)**: Build process and deployment procedures
- **[Makefile](./Makefile)**: Automation scripts and commands
- **[sitemap.md](./sitemap.md)**: Site structure and navigation

### Code Standards
- **File Size Guidelines (Updated May 2025)**: Keep JavaScript files between 300-500 lines of code
  - Maximum threshold: 600 lines before refactoring is required
  - Absolute limit: 800 lines (files exceeding this must be refactored immediately)
  - See [Code Organization Guidelines 2025](./knowledge_base/frontend/code_organization_guidelines_2025.md) for details
  - Benefits include improved performance, better code splitting, enhanced AI-assisted development, and more effective code reviews

### Development Guides
- **[docs/dev_guide/](./docs/dev_guide/)**: Development phase documentation
  - Each phase has a dedicated folder with:
    - **devPhaseX.md**: Phase overview, goals, and implementation approach
    - **devPhaseX_tasks.md**: Specific tasks and requirements for the phase

### Knowledge Base
- **[knowledge_base/](./knowledge_base/)**: Comprehensive guides and best practices
  - **[knowledge_base/frontend/](./knowledge_base/frontend/)**: Frontend development guides
  - **[knowledge_base/backend/](./knowledge_base/backend/)**: Backend development guides

When implementing features or fixing issues, always check these documents first to understand the context, requirements, and best practices for the specific area you're working on.

## Vision

RentUp aims to revolutionize the rental economy by creating a trusted platform where individuals and businesses can safely rent, purchase through rent-to-buy, or participate in auctions. Our AI-powered platform continuously learns and improves to provide better matching, pricing recommendations, fraud prevention, and dispute resolution.

## Tech Stack

### Frontend
- **Web**: Next.js 14 (React 19+) as a Progressive Web App
- **React**: v19
- **React-DOM**: v19.1.0
- **Tailwind CSS**: v4
- **React Hook Form**: v7.55.0
- **Zod**: v3.24.2
- **Zustand**: v5.0.3
- **Mobile**: React Native with Expo
- **Styling**: Tailwind CSS with custom design system
- **State Management**: React Query + Zustand
- **Forms**: React Hook Form with Zod validation
- **Server-Side Rendering**:
  - React Server Components for server-only logic
  - HTML streaming for progressive rendering
  - Hybrid rendering strategies for optimal performance
- **Responsive Design**:
  - Fluid layouts with percentage-based sizing
  - Mobile-first approach with progressive enhancement
  - Standard breakpoints (xs:375px through 2xl:1536px)
  - Touch-friendly interfaces with proper sizing
  - Responsive typography and spacing system
- **Accessibility**:
  - WCAG 2.1 AA compliance
  - Semantic HTML structure
  - Keyboard navigation support
  - Screen reader compatibility
  - Sufficient color contrast (4.5:1 for normal text, 3:1 for large text)
  - Form accessibility with proper labels and error messages
- **Performance Optimization**:
  - Core Web Vitals optimization (LCP, FID, CLS)
  - Advanced code splitting with dynamic imports
  - List virtualization for efficient rendering
  - Service workers for offline support and caching
  - Modern image formats (WebP, AVIF) with responsive loading
  - Bundle optimization with tree shaking and dead code elimination
  - Critical CSS extraction and inline loading
  - Performance monitoring and metrics collection
- **Security Enhancements**:
  - Content Security Policy implementation
  - Strict input validation with Zod
  - CSRF protection
  - Secure authentication with OAuth 2.0 and PKCE
  - XSS prevention with proper output encoding
  - Permissions Policy implementation
- **AI Integration**:
  - AI-powered recommendations and content curation
  - AI-assisted development with GitHub Copilot
  - AI-powered fraud detection and prevention

### Backend
- **Database**: PostgreSQL 17 (Docker containerized) with advanced optimization
- **Vector Database**: Qdrant (for AI features)
- **Alembic**: v1.15.2
- **SQLAlchemy**: v2.0.40 with async support
- **API**: FastAPI with Uvicorn (Production-ready with Gunicorn)
- **Authentication**: Enhanced JWT with MFA support and security hardening
- **Real-time**: WebSockets for messaging, notifications, and live auctions
- **Template Engine**: Jinja2 for agreement generation
- **Fraud Detection**: Multi-layered ML-based system
- **AI Framework**: Google ADK integration ready (8-week migration roadmap)
- **Caching**: Multi-level Redis caching with intelligent strategies
- **Security**: OWASP 2025 compliant with comprehensive security headers
- **Monitoring**: Prometheus, Grafana, ELK stack for observability
- **Performance**: Query optimization, JOIN optimization, connection pooling

### Infrastructure
- **Containerization**: Docker with multi-stage production builds
- **Hosting**: AWS (EC2, S3, RDS) with auto-scaling capabilities
- **CI/CD**: GitHub Actions with automated testing and deployment
- **Monitoring**: Prometheus/Grafana with comprehensive dashboards
- **Logging**: ELK Stack (Elasticsearch, Logstash, Kibana) for centralized logging
- **Security**: Production-hardened with security headers and rate limiting
- **Deployment**: Production-ready with health checks and graceful shutdown

### Third-party Services
- **Analytics**: Plausible (privacy-focused)
- **Payments**: Stripe (with Apple Pay/Google Pay integration)
- **Maps/Location**: Mapbox
- **Email**: Resend.com
- **SMS**: Twilio
- **Identity Verification**: ID verification and biometric checks
- **Digital Signatures**: For legally binding rental agreements

## Key Features

### For Renters
- Search and filter items by category, location, price
- Choose between rental, rent-to-buy, or auction options
- Track rent-to-buy progress and conversion status
- Participate in rental auctions for high-demand items
- AI-powered recommendations and price tracking
- View detailed item information and availability
- Check item reliability scores and rental history
- Book items with secure payment processing
- Rate and review rental experiences
- In-app messaging with item owners
- Rent both physical and digital items
- Create "Looking to Rent" listings to find specific items not currently available
#### Financial Management
- View detailed transaction history with status tracking
- Access digital receipts and invoices
- Track security deposits and refunds
- Monitor late return penalties and adjustments
- Set up automatic payments and payment reminders
- Export financial records for tax purposes
- View rental credit and rewards balance
- Track rent-to-buy payment progress

### For Item Owners
- List items for rental, rent-to-buy, or auction
- Set flexible terms for rent-to-buy conversions
- Manage multiple transaction types
- Track payments and conversion progress
- Document item maintenance and testing history
- Build reliability scores through successful rentals
- Receive pricing optimization suggestions
- Set items for auction-style rental
- List digital items for rental (software, media, etc.)
- Browse and respond to "Looking to Rent" listings from potential renters
#### Financial Dashboard
- Real-time earnings overview
- Payout schedule and history
- Late return fee collection status
- Damage claim tracking and resolution
- Tax documentation and reporting
- Revenue analytics and projections
- Item performance metrics
- Automated pricing optimization insights

### For Businesses
- Business dashboard for managing multiple listings
- Team management with role-based permissions
- Member invitation and management system
- Business account settings and profile management
- Analytics and reporting with business insights
- Bulk operations for listings and rentals
- Verified business status with enhanced trust features
- Cost calculator for ROI analysis
- Affiliate program participation
- Business account switching between personal and business accounts
- Multiple business account management for users
#### Business Account Tiers
- **Starter Tier**: Up to 5 team members, 50 listings, basic analytics, standard support
- **Professional Tier**: Up to 20 team members, 200 listings, advanced analytics, priority support, bulk upload capabilities, 5% insurance discount, 10% transaction fee discount
- **Enterprise Tier**: Up to 100 team members, 1000 listings, comprehensive analytics, priority support with dedicated account manager, bulk upload capabilities, custom branding, API access, 15% insurance discount, 20% transaction fee discount
#### Financial Controls & Analytics
- Revenue tracking by category/location
- Commission management and adjustments
- Refund processing and dispute resolution
- Platform fee analysis and optimization
- Tax compliance monitoring
- Payment gateway performance metrics
- Chargeback and fraud loss tracking
- Promotion impact analysis
- Business-specific financial reporting

### AI-Powered Features
- Smart matching between users and items
- Dynamic pricing for rentals and auctions
- Rent-to-buy value optimization
- Fraud detection and prevention
- Dispute resolution assistance
- Personalized recommendations
- Continuous learning from platform usage
- Auction price optimization
- Self-improving recommendation engine that evolves with user interactions
- Autonomous content moderation using vision and text analysis
- Predictive maintenance suggestions for rental items
- Adaptive search algorithms that improve based on user behavior
- AI-powered content curation and quality assessment
- Automatic categorization and tagging system
- Content moderation with AI assistance
- Model performance monitoring and management
- **Google ADK Integration Ready**: Modern agentic framework for production AI
  - 5 specialized agents identified for migration
  - Hierarchical agent architecture with main router
  - 8-week structured migration roadmap
  - Production-ready agent definitions created
  - Comprehensive evaluation framework
- Interactive AI visualization tools
  - Comprehensive visualization dashboard with multiple views
  - Preference visualization showing user preference weights and influences
  - Item relationship visualization with interactive exploration
  - Recommendation explanation with factor analysis
  - Recommendation comparison for side-by-side analysis
  - Enhanced analytics dashboard with performance metrics
  - Time-series visualization of recommendation performance
  - Category distribution and factor influence analysis
#### Financial Intelligence
- Dynamic pricing optimization
- Revenue prediction models
- User payment behavior analysis
- Fraud pattern detection
- Promotion effectiveness prediction
- Platform health indicators
- Market demand forecasting
- Automated financial reporting

### Trust & Safety
- User verification (ID, phone, email)
- Secure payment handling
- Insurance options
- Standardized rental agreements
- Dispute resolution process
- Rating and review system
- Item reliability scoring and history tracking
- Pre-rental testing protocols and verification
- Multi-stage verification process (government ID, selfie verification, address verification)
- Secure escrow payment system that only releases funds after successful rental completion
- AI-powered fraud detection to identify suspicious listing patterns and user behaviors
- Real-time item verification through image recognition and serial number tracking
- Comprehensive insurance coverage for both renters and owners
- Secure in-app messaging with prohibited external contact until transaction completion
- Geolocation verification to confirm item pickup/dropoff
- Trust badges and verification levels visible on all user profiles
- Community reporting system with rapid response team
- Background checks for high-value rentals

## API Architecture

Our platform is built on a comprehensive RESTful API that enables seamless integration between frontend applications and backend services. The API follows standard HTTP methods and status codes, with JSON as the primary data format.

Key API features include:
- Authentication via JWT tokens
- Comprehensive endpoints for all platform functionality
- Versioned API for backward compatibility
- Rate limiting to prevent abuse
- Detailed error responses
- Webhook support for real-time integrations

For detailed API documentation, see our [API Specification](./docs/api-specification.md).

## Monetization Strategy
- **Balanced Commission Model**: Split fee approach with 8-10% from renters and 3-5% from owners
- **Dynamic Fee Structure**: Reduced fees for first-time users, high-volume renters, and during promotional periods
- **Premium Features**: Subscription tiers for enhanced visibility and advanced tools
- **Advertising**: Sponsored listings and targeted ads from relevant businesses
- **Engagement Rewards**: Fee discounts through gamification and platform participation
- **Seasonal Promotions**: Reduced fees during cultural events and holidays to boost specific rental categories
- **Referral Incentives**: Commission discounts for bringing new users to the platform
- **Affiliate Partnerships**: Revenue sharing with complementary service providers
- **Featured Listings**: Priority placement in search results for additional fees
- **Insurance Services**: Optional coverage for rentals with revenue sharing
- **Auction Premium**: Additional fees for auction-style rentals of high-demand items
- **Verification Services**: Premium verification badges for enhanced trust
- **Loyalty Program**: Cumulative points system that reduces transaction fees over time

### Autonomous Implementation
- **Data Collection Framework**: Track user behaviors, transaction patterns, and engagement metrics
- **Rules Engine**: Define triggers and conditions for automatic promotions
- **AI Decision Layer**: Machine learning models to optimize fee structures and promotions
- **Feedback Loop**: Continuously measure effectiveness and refine strategies
- **A/B Testing System**: Automatically test different approaches and scale successful ones

## Engagement and Growth Strategies

### Gamification and Rewards
- **Rental Challenges**: Complete themed rental goals for rewards
- **Seasonal Competitions**: Participate in holiday-themed contests with prizes
- **Community Ratings**: Earn bonus points for highly-rated transactions
- **Knowledge Quizzes**: Answer questions about rental categories for points
- **Daily Check-ins**: Receive points for regular platform engagement
- **Achievement Badges**: Unlock visual recognition with fee discount benefits

### Promotional Campaigns
- **New Market Launches**: Zero-fee periods when entering new geographic regions
- **Category Boosting**: Reduced fees for under-represented item categories
- **Cultural Event Tie-ins**: Special promotions aligned with local festivals
- **Flash Promotions**: Surprise 24-hour fee reductions to drive activity
- **Counter-Cyclical Incentives**: Lower fees during typically slow periods

### Autonomous Optimization
- **Dynamic Fee Adjustment**: AI-driven commission rates based on market conditions
- **Personalized Incentives**: Custom offers generated from user behavior patterns
- **Predictive Engagement**: Proactive rewards to prevent user churn
- **A/B Testing System**: Automatic optimization of promotional strategies

### Loyalty and Retention
- **Tiered Status Levels**: Progressive benefits based on platform usage
- **Points Ecosystem**: Comprehensive reward system for all platform activities
- **Flexible Redemption**: Convert points to fee discounts or premium features
- **Anniversary Rewards**: Special benefits on user's platform anniversary
- **Win-Back Campaigns**: Targeted offers for dormant users

### Community Building
- **Ambassador Program**: Empower power users to promote the platform
- **Community Events**: Virtual and physical meetups for rental communities
- **Knowledge Sharing**: Reward users who create guides for specific categories
- **Sustainability Impact**: Track and showcase environmental benefits of renting

## Design Philosophy

Our design system focuses on creating a consistent, accessible, and intuitive user experience across all platforms. We prioritize clarity, simplicity, and user-centered design principles.

For complete design guidelines, see our [Design System Documentation](./docs/design-system/design-system.md).

### Accessibility

RentUP is committed to creating an inclusive platform that follows WCAG 2.1 AA standards. Our accessibility features include:

- **Screen Reader Support**: All interactive elements have proper ARIA attributes and screen reader text
- **Keyboard Navigation**: Full keyboard support with visible focus indicators
- **Color Contrast**: All text and UI elements meet WCAG 2.1 AA contrast requirements
- **Discernible Text**: All buttons have text that is either visible or available to screen readers
- **Alternative Text**: All images have descriptive alt text
- **Semantic HTML**: Proper HTML5 semantic elements for better structure
- **Responsive Design**: Accessible on all device sizes with proper touch targets
- **Form Accessibility**: Clear labels, error messages, and validation

### Interactive Elements System

RentUP implements a comprehensive interactive elements system that ensures consistent behavior and accessibility across all buttons, links, and other interactive components:

- **Centralized Styling**: All interactive elements use a shared CSS framework
- **Consistent Behavior**: Uniform hover, focus, and active states across the platform
- **Accessibility-First**: All elements include proper ARIA attributes and keyboard navigation
- **Title Attributes**: Every interactive element has a descriptive title for screen readers
- **Cursor Indicators**: All clickable elements show the pointer cursor on hover
- **Ripple Effects**: Material Design-inspired feedback for user interactions
- **Extensible Components**: Easy to extend for specialized functionality
- **Icon Buttons**: Buttons with only icons include title, aria-label, and sr-only text

#### Why Consistency Matters

Consistent interactive elements are crucial for a good user experience:

1. **Reduced Cognitive Load**: Users don't have to learn different interaction patterns
2. **Improved Accessibility**: Consistent behavior helps users with disabilities
3. **Easier Maintenance**: Centralized styling makes updates simpler and more reliable
4. **Better Visual Harmony**: Consistent styling creates a more polished look
5. **Faster Development**: Reusable components speed up implementation of new features

#### Implementation

We use two main components for all interactive elements:

- `InteractiveButton`: For all button-like interactions
- `InteractiveLink`: For all link-like interactions (internal and external)

These components ensure that all interactive elements have:
- Proper cursor pointer on hover
- Consistent hover effects
- Accessibility attributes
- Loading states when needed

See our [Interactive Elements Documentation](./docs/design-system/interactive-elements.md) for implementation details and usage guidelines.

### Color Scheme

#### Primary Colors
- **Primary Blue** (#3B82F6) - Used for headers, navigation, primary buttons, and trust indicators
  - Light: #60A5FA - For hover states and backgrounds
  - Dark: #2563EB - For active states and emphasis

- **Secondary Teal** (#0D9488) - Used for community features, progress indicators, and secondary CTAs
  - Light: #14B8A6 - For hover states
  - Dark: #0F766E - For active states

- **Accent Orange** (#F97316) - Used for primary CTAs, important notifications, and premium features
  - Light: #FB923C - For hover states
  - Dark: #EA580C - For active states

#### Feedback Colors
- **Success Green** (#10B981) - For successful transactions and positive status indicators
- **Warning Amber** (#F59E0B) - For attention required and pending actions
- **Error Red** (#EF4444) - For critical errors and security warnings

#### Neutral Colors
- **White** (#FFFFFF) - Primary background and content areas
- **Light Gray** (#F3F4F6) - Secondary backgrounds and disabled states
- **Medium Gray** (#9CA3AF) - Disabled text and subtle borders
- **Dark Gray** (#4B5563) - Body text and secondary headings
- **Near Black** (#1F2937) - Primary headings and important text

For detailed color usage guidelines, see our [Color Palette Documentation](./docs/design-system/color-palette.md).

### Typography
- **Primary Font**: Inter - Clean, modern sans-serif for all UI elements
- **Secondary Font**: Montserrat - Used for headings and emphasis
- **Font Sizes**:
  - Headings: 48px (h1), 36px (h2), 30px (h3), 24px (h4), 20px (h5), 18px (h6)
  - Body: 16px (default), 14px (small), 12px (extra small)
  - Display: 64px, 56px (for hero sections and major features)
- **Font Weights**:
  - Regular (400) - Default body text
  - Medium (500) - Emphasis and subheadings
  - Semibold (600) - Buttons and important UI elements
  - Bold (700) - Main headings and strong emphasis

### Spacing System
- **Base Unit**: 4px
- **Spacing Scale**:
  - xs: 4px (0.25rem)
  - sm: 8px (0.5rem)
  - md: 16px (1rem)
  - lg: 24px (1.5rem)
  - xl: 32px (2rem)
  - 2xl: 48px (3rem)
  - 3xl: 64px (4rem)
  - 4xl: 96px (6rem)
- **Component Spacing**:
  - Card padding: 16px
  - Section margins: 64px
  - Form field gaps: 16px
  - Button padding: 8px 16px

### Iconography
- **Style**: Outlined with 2px stroke, rounded corners
- **Sizes**:
  - Small: 16px (for dense UI areas)
  - Medium: 24px (standard UI elements)
  - Large: 32px (featured areas)
- **Icon Set**: Custom icon set based on Phosphor Icons
- **Usage Guidelines**:
  - Use icons consistently for similar actions
  - Pair icons with text for clarity
  - Maintain adequate touch targets (minimum 44px)
  - Use color sparingly for emphasis

### User Experience Principles
- Intuitive navigation
- Minimal steps to complete actions
- Progressive disclosure of information
- Mobile-first responsive design
- Accessibility compliance (WCAG 2.1)
- Consistent feedback for user actions
- Forgiving interfaces that prevent errors

## Development Roadmap

### Phase 1: Frontend Scaffolding ✅
- Core UI components and layouts
- Static pages and authentication UI
- Item and listing UI components
- User and booking UI components
- Error handling and boundary components
- Basic responsive design implementation

### Phase 2: Backend Integration ✅
- User authentication and profiles
- Database models and migrations
- API endpoints for core functionality
- Multi-modal search capabilities (text + image)
- Vector search with Qdrant integration
- Frontend-backend connection

### Phase 3: Advanced Platform Features ✅ **100% COMPLETE**
- AI-powered recommendations ✅
- Dynamic pricing ✅
- Enhanced analytics ✅
- Auction system ✅
- Agreement generation ✅
- Fraud prevention ✅
- Business accounts system ✅
  - Business dashboard ✅
  - Team management with role-based permissions ✅
  - Member invitation and management ✅
  - Business account settings ✅
  - Account switching between personal and business ✅
  - Multiple business account support ✅
  - Business tiers (Starter, Professional, Enterprise) ✅
- Preference visualization ✅
- Embedding visualization ✅
- Comprehensive visualization dashboard ✅
  - Interactive visualization features with filtering and zooming ✅
  - Multi-tab visualization interface (Overview, Preferences, Item Relationships, Recommendations, Dashboard) ✅
  - Performance-optimized visualization components ✅
  - Responsive and accessible visualization design ✅
- User feedback mechanisms ✅
- Recommendation display components ✅

### 🎉 **FRONTEND DEVELOPMENT: 100% COMPLETE!**

#### **Code Refactoring Achievement**
- **High-Priority Components**: 6/6 completed (100%)
- **Medium-Priority Components**: 9/9 completed (100%)
- **Total Lines Reorganized**: ~11,204 lines into 194 modular files
- **Average File Size**: Reduced from 800+ lines to 50-200 lines per file

#### **Production-Ready Features**
- ✅ **Complete Authentication System**: Multi-factor, social login, security features
- ✅ **Responsive Design**: Mobile-first, all breakpoints (xs:375px - 2xl:1536px)
- ✅ **Accessibility Compliance**: Full WCAG 2.1 AA standards
- ✅ **Performance Optimization**: Core Web Vitals targets achieved
- ✅ **Modern Architecture**: React 19, TypeScript, modular components
- ✅ **Comprehensive Testing**: 95%+ coverage across all modules
- ✅ **Security Hardening**: OWASP best practices implemented
- ✅ **AI Integration**: Advanced recommendation and curation systems
- Advanced responsive design implementation ✅
  - Fluid layouts with percentage-based sizing ✅
  - Mobile-first approach with progressive enhancement ✅
  - Standard breakpoints implementation ✅
  - Touch-friendly interfaces ✅
  - Responsive typography and spacing system ✅
- Accessibility implementation (WCAG 2.1 AA) ✅
  - Semantic HTML structure ✅
  - Keyboard navigation support ✅
  - Screen reader compatibility ✅
  - Color contrast optimization ✅
  - Form accessibility ✅
- Performance optimization ✅
  - Core Web Vitals optimization ✅
  - Server-side rendering implementation ✅
  - HTML streaming for progressive rendering ✅
  - Modern state management with Zustand ✅
- Jest unit testing setup ✅
- Shared UI component library ✅
- Comprehensive test suite (Playwright, Cypress, Jest) ✅

### Phase 4: Scaling & Optimization ✅
- Performance optimization ✅
  - Advanced code splitting and bundle optimization ✅
  - Service worker implementation for offline support ✅
  - Modern image formats and responsive loading ✅
  - List virtualization for efficient rendering ✅
  - Critical CSS extraction and inline loading ✅
  - Performance monitoring and metrics collection ✅
- Security enhancements ✅
  - Content Security Policy implementation ✅
  - Strict input validation and output encoding ✅
  - CSRF protection and secure authentication ✅
  - Permissions Policy implementation ✅
- Scalability enhancements ✅
- Production deployment ✅
- International expansion ✅
- API for third-party integrations ✅
- Digital item rental marketplace ✅
- Affiliate partnership program ✅
- Progressive Web App (PWA) implementation ✅

### Phase 5: Database & Code Optimization ✅
- Database indexing and query optimization
- Code consolidation and refactoring
- Dependency updates
- Performance benchmarking

### Phase 6: AI Architecture Implementation 🔄
- Central router for AI requests
- Specialized AI agents
  - Recommendation agent
  - Fraud detection agent
  - Dynamic pricing agent
  - User analysis agent
  - Content moderation agent
  - Chatbot agent
  - Rent planner agent
- Shared AI components
  - Embedding service
  - Cache service
  - Performance tracking
- Fallback mechanisms for all AI components
- Comprehensive testing and monitoring

### Phase 7: Backend Optimization and Production Readiness ✅
- Backend audit and cleanup ✅
  - Code redundancy elimination ✅
  - Test suite consolidation ✅
  - Documentation alignment ✅
- Database optimization ✅
  - Connection pooling ✅
  - Query optimization ✅
  - Transaction management ✅
  - Advanced optimization modules ✅
    - Query performance tracking ✅
    - JOIN optimization ✅
    - Multi-level caching ✅
    - Database connection management ✅
- Performance enhancements ✅
  - Caching improvements ✅
  - Resource optimization ✅
- Security enhancements ✅
  - Authentication improvements ✅
  - Data protection ✅
  - API security ✅
  - OWASP 2025 compliance ✅
  - Security headers and rate limiting ✅
- Production deployment readiness ✅
  - Docker containerization ✅
  - Monitoring stack (Prometheus, Grafana, ELK) ✅
  - Health checks and graceful shutdown ✅

### Phase 8: Advanced Backend Optimization and Scaling ✅
- Advanced query optimization
  - Query batching
  - Database indexing
  - Query analysis and tuning
- Asynchronous operations
  - Async API endpoints
  - Background task processing
  - Event-driven architecture
- Comprehensive caching
  - Multi-level caching
  - Advanced cache invalidation
  - Cache warming and prefetching
- Horizontal scaling preparation
  - Stateless services
  - Data partitioning
  - Distributed caching
- Codebase cleanup and standardization
- Documentation improvements

### Phase 9: Monitoring, Alerting, and Observability ✅
- Metrics collection and storage
  - System metrics collection
  - Application metrics collection
  - Database metrics collection
- Alerting system
  - Alert rules configuration
  - Notification channels
  - Alert routing and grouping
- Visualization and dashboards
  - Overview dashboard
  - Backend performance dashboard
  - Database performance dashboard
- Logging and tracing
  - Centralized logging
  - Distributed tracing
  - Log analysis

### Phase 10: Advanced Backend Optimizations (2025) 📅
- Advanced FastAPI optimizations
  - Profiling and performance monitoring
  - Advanced caching strategies
  - Asynchronous optimization
  - Modular routing architecture
- PostgreSQL 17 optimizations
  - Query optimization
  - Index optimization
  - Connection management
  - WAL and storage optimization
- Distributed caching system
  - Redis cluster configuration
  - Advanced cache strategies
  - Cache invalidation mechanisms
  - Cache monitoring and optimization
- Performance testing and benchmarking
  - Comprehensive benchmarking suite
  - Load testing scenarios
  - Profiling and analysis
  - Reporting and visualization
- Code quality and maintenance
  - Redundancy checking and elimination
  - Dependency updates to latest versions
  - Comprehensive documentation
  - Automated code quality tools

### Phase 11: Google ADK AI Framework Migration 📅
- **Phase 1: Core Agent Migration** (Week 1-2)
  - Install Google ADK dependencies
  - Migrate recommendation and fraud detection agents
  - Implement tool integrations
  - Basic testing and validation
- **Phase 2: Extended Agent Migration** (Week 3-4)
  - Migrate pricing and content moderation agents
  - Implement hierarchical agent structure
  - Advanced tool integrations
  - Performance optimization
- **Phase 3: Integration and Testing** (Week 5-6)
  - Complete user analysis agent migration
  - Implement main router agent
  - Comprehensive evaluation framework
  - Integration testing and benchmarking
- **Phase 4: Production Deployment** (Week 7-8)
  - Production configuration and deployment
  - A/B testing setup with gradual rollout
  - Monitoring and alerting implementation
  - Performance optimization and scaling

For detailed information on our development phases, see the documentation in the [docs/dev_guide](./docs/dev_guide/) directory.

## System Architecture

Our platform follows a microservices architecture with the following core components:

- **Authentication Service**: Handles user registration, login, and token management
- **User Management Service**: Manages profiles and verification systems
- **Business Account Service**: Manages business accounts, members, and permissions
- **Listing Service**: Handles listing creation, management, and search
- **Booking Service**: Processes booking requests and manages booking status
- **Auction Service**: Manages auction listings, bidding, and real-time updates
- **Agreement Service**: Generates and manages digital rental agreements
- **Messaging Service**: Provides real-time chat functionality
- **Review Service**: Manages review creation and rating calculations
- **Payment Service**: Handles payment processing and payout management
- **Notification Service**: Manages push, email, and in-app notifications
- **Analytics Service**: Tracks user behavior and platform performance
- **Reporting Service**: Handles user reports and content moderation
- **Fraud Detection Service**: Multi-layered system to prevent and detect fraud
- **Verification Service**: Handles identity and document verification

For more details, see our [Core Components](./docs/core-components.md) documentation.

## CI/CD and Deployment

We follow modern DevOps practices with a robust CI/CD pipeline:

- **Source Control**: GitHub with branch protection and PR reviews
- **Continuous Integration**: Automated testing and code quality checks
- **Continuous Deployment**: Automated deployment to staging and production
- **Infrastructure as Code**: AWS resources managed through Terraform
- **Monitoring**: Comprehensive system and application monitoring

For more information, see our [CI/CD Deployment](./docs/CI-CD-deployment.md) documentation.

## Autonomous Platform Intelligence

### Self-Learning Systems
- **Behavioral Pattern Recognition**: System automatically identifies and adapts to emerging user patterns
- **Continuous Model Training**: AI models that retrain themselves based on new transaction data
- **Anomaly Detection**: Self-adjusting thresholds for identifying unusual platform activity
- **Natural Language Understanding**: Evolving comprehension of user queries and feedback
- **Cross-Category Learning**: Insights from one rental category automatically applied to others

### AI Architecture: Hybrid MoE Approach
- **Specialized Expert Models**: Domain-specific models for pricing, recommendations, fraud detection, etc.
- **Router/Gating Network**: Intelligent system to direct queries to appropriate expert models
- **Ensemble Integration**: Combining outputs from multiple experts for more robust decisions
- **Sparse Activation**: Efficient resource usage by only activating relevant expert models
- **Federated Learning**: Distributed model improvement while preserving data privacy
- **Knowledge Distillation**: Compressing insights from complex models into lightweight deployable versions
- **Conditional Computation**: Dynamic pathways through the system based on input characteristics
- **Multi-objective Optimization**: Balancing multiple goals (revenue, user satisfaction, platform growth)
- **Transfer Learning**: Leveraging knowledge from one rental category to improve performance in others
- **Hierarchical Decision Making**: Structured approach to complex decisions with multiple expert layers
- **Uncertainty Quantification**: Measuring confidence levels to determine when human review is needed
- **Explainable AI Components**: Transparent reasoning for critical decisions affecting users

#### AI Component Architecture
- **Central Router**: Directs requests to specialized AI agents based on request type and context
- **Recommendation Engine**: Provides item matching and personalized recommendations
- **Fraud Detection System**: Identifies suspicious patterns and assesses risk
- **Dynamic Pricing Optimizer**: Analyzes market conditions and recommends optimal pricing
- **User Behavior Analyzer**: Tracks engagement patterns and predicts user actions
- **Content Moderation Service**: Filters inappropriate content and ensures listing quality
- **Caching Layer**: Stores frequent predictions and embeddings for improved performance
- **Monitoring System**: Tracks model performance, drift, and resource usage

For detailed implementation specifications, see our [AI Architecture Technical Specifications](./docs/dev_guide/devPhase6_ai/technical_specs/implementation-plan.md) and [AI Model Comparative Analysis](./docs/dev_guide/devPhase6_ai/technical_specs/comparative-analysis.md).

#### Selected Base Models & Strategy

To implement the MoE approach efficiently, RentUp utilizes a tiered selection of fine-tuned Large Language Models (LLMs) managed via the Hugging Face `transformers` library, balancing capability, resource usage (optimized for 8GB VRAM with quantization), and multilingual needs (English, Chinese, Malay, Indonesian).

*   **Embedding Model:** `nomic-embed-text-v1.5` (via `sentence-transformers`) provides core semantic understanding for Retrieval-Augmented Generation (RAG) and similarity tasks, integrated with the Qdrant vector database.
*   **Tiered LLM Experts:**
    *   **Small:** `deepseek-r1:1.5b-qwen-distill-fp16` - Used for fast routing (gating network), simple classification, and basic tasks. Qwen base aids multilingual performance. FP16 used due to small size.
    *   **Medium:** `gemma3:4b-it-q8_0` - Workhorse model for common tasks like RAG-based Q&A, listing assistance, standard recommendations, and moderate analysis. Q8 quantization balances quality and performance.
    *   **Large:** `deepseek-r1:7b-qwen-distill-q8_0` - Reserved for complex reasoning, advanced dynamic pricing, sophisticated fraud detection, and in-depth analytics. Qwen base supports multilingual needs; Q8 quantization used for VRAM efficiency.

*   **Usage Approach:** The MoE router (likely the Small model) directs requests to the appropriate expert tier. All tiers leverage RAG for context grounding. Models will be fine-tuned on RentUp-specific data. This tiered approach incorporates Mixture-of-Quantization (MoQ) principles (FP16, Q8).

*For a detailed breakdown of the AI model implementation strategy, task allocation, fine-tuning plans, and RAG integration, please refer to the [AI Model Strategy document](./docs/ai-model-strategy.md) (to be created).*

### Admin Control Panel
- **Comprehensive Dashboard**: Real-time metrics on platform health, user activity, and business performance
- **User Management**: Tools for account oversight, verification approval, and issue resolution
- **Content Moderation**: Review queue for AI-flagged listings requiring human verification
- **AI Curation Management**: Content quality assessment, categorization, and moderation tools
- **Financial Controls**: Revenue tracking, refund processing, and commission management
- **AI System Oversight**: Model performance monitoring with manual override capabilities
- **Marketing Command Center**: Campaign management, promotion scheduling, and performance tracking
- **Business Intelligence**: Advanced analytics on market trends, user behavior, and revenue patterns
- **System Configuration**: Feature flag management and service health monitoring
- **Staff Management**: Role-based access control for admin team members
- **Audit Logging**: Comprehensive tracking of all administrative actions
- **Crisis Management Tools**: Emergency controls for platform-wide issues
- **Feedback Analysis**: Aggregated user sentiment and suggestion tracking
#### Financial Oversight
- Platform-wide revenue dashboard
- Commission structure management
- Payment gateway monitoring
- Refund approval workflow
- Financial compliance tools
- Automated billing system management
- Revenue sharing calculations
- Platform cost analysis

### Autonomous Operations
- **Dynamic Resource Allocation**: Server resources automatically scale based on demand patterns
- **Self-Healing Infrastructure**: Automatic detection and resolution of system issues
- **Automated Content Curation**: Intelligent highlighting of trending or seasonal rental items
- **Smart Notification System**: Self-optimizing communication timing based on user responsiveness
- **Autonomous Quality Control**: Automatic detection of low-quality listings with improvement suggestions

### Feedback Integration
- **Sentiment Analysis Pipeline**: Automatic processing of reviews and feedback to identify platform improvements
- **Closed-Loop Learning**: System actions automatically evaluated and refined based on outcomes
- **Multi-modal Feedback Processing**: Learning from text, images, usage patterns, and transaction data
- **Emergent Feature Development**: Identifying needed features based on user behavior and requests
- **Autonomous A/B Testing**: Self-initiated experiments to optimize user experience

## Getting Started

### Prerequisites

- Docker and Docker Compose
- Node.js 20+ (for local development)
- Python 3.12+ (for local development)
- PostgreSQL 17+ (for database)
- Qdrant (for vector search)

### Running with Docker

1. Clone the repository:
   ```bash
   git clone https://github.com/agentLabsNetwork/rentup.git
   cd rentup
   ```

2. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

3. Start the application with Docker Compose:
   ```bash
   docker-compose up
   ```

4. Apply database migrations:
   ```bash
   docker-compose exec backend alembic upgrade head
   ```

5. Access the application:
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs
   - PostgreSQL: localhost:5432
   - Qdrant: http://localhost:6333

### Running Tests

To run comprehensive tests for all phases:
```bash
./test_all_phases.py
```

To run tests for a specific phase:
```bash
./test_all_phases.py --phase 3
```

To run tests for a specific component:
```bash
./test_all_phases.py --component recommendations
```

### Local Development

For local development without Docker, see [DEVELOPMENT.md](./DEVELOPMENT.md) for setup instructions.

## Knowledge Base

We maintain a comprehensive knowledge base with the latest best practices, guides, and reference materials for both frontend and backend development:

- `knowledge_base/`: Comprehensive knowledge base
  - `knowledge_base/frontend/`: Frontend development guides
    - Frontend trends and technologies
    - React and Next.js best practices
    - Frontend testing guides
  - `knowledge_base/backend/`: Backend development guides
    - Backend trends and technologies
    - FastAPI optimization strategies
    - Database optimization techniques
    - Backend testing best practices

For more information, see the [Knowledge Base README](./knowledge_base/README.md).

## Testing

We use a comprehensive testing approach that includes both frontend and backend tests. Our testing methodology is outlined in [testmethod.md](./testmethod.md).

### Frontend Testing

We use Playwright and Cypress for frontend testing:

```bash
# Navigate to the frontend directory
cd frontend

# Run all Playwright tests
npm test

# Run smoke tests
npm run test:smoke

# Run home page tests
npm run test:home

# Run tests with UI mode
npm run test:ui

# Run tests in headed mode
npm run test:headed

# View test reports
npm run test:report

# Run Cypress tests
npm run cypress:run
```

### Backend Testing

We use pytest for backend testing:

```bash
# Navigate to the backend directory
cd backend

# Run all tests
pytest

# Run specific test file
pytest app/tests/test_vector_search.py

# Run with coverage
pytest --cov=app
```

### Test Structure

Our tests follow a structured approach for better organization and maintainability:

#### Frontend Tests
- `frontend/tests/`: Playwright tests
  - `page-objects/`: Page object classes
  - `smoke.spec.js`: Basic smoke tests
  - `home-simple.spec.js`: Home page tests
  - `visualization.test.js`: Visualization component tests
  - `recommendations.test.js`: Recommendation component tests
- `frontend/cypress/`: Cypress tests
  - `e2e/`: End-to-end tests
  - `integration/`: Integration tests

#### Backend Tests
- `backend/app/tests/`: Backend tests
  - `test_vector_search.py`: Vector search tests
  - `test_auth_endpoints.py`: Authentication API tests
  - `test_recommendations.py`: Recommendation system tests
  - `test_visualization.py`: Visualization API tests
  - `test_auctions.py`: Auction system tests
  - `test_agreements.py`: Agreement system tests
  - `test_fraud_detection.py`: Fraud prevention tests
- `backend/tests/performance/`: Performance testing scripts
  - `benchmark_api.py`: API performance benchmarking
  - `benchmark_database.py`: Database query benchmarking
  - `locustfile.py`: Load testing with Locust

#### Code Quality Tools
- `backend/scripts/code_quality/`: Code quality tools
  - `check_redundancies.py`: Script to check for code redundancies
  ```bash
  # Check for redundancies in the codebase
  python backend/scripts/code_quality/check_redundancies.py

  # View the redundancy report
  cat redundancy_report.json
  ```
- Linting with flake8 and pylint
- Type checking with mypy
- Code formatting with black
- Import sorting with isort
- Security scanning with bandit

#### Test Results
- `testResults/`: Test results organized by phase and type
  - `organized/phase0/`: Phase 0 test results
  - `organized/phase1/`: Phase 1 test results
  - `organized/phase2/`: Phase 2 test results
  - `organized/phase3/`: Phase 3 test results
  - `organized/playwright/`: Playwright test results
  - `organized/cypress/`: Cypress test results
  - `organized/comprehensive/`: Comprehensive test results

## Contributing

Please read [CONTRIBUTING.md](./CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## License

This project is licensed under the MIT License - see the [LICENSE.md](./LICENSE.md) file for details.

## Anti-Fraud Measures

RentUp employs a multi-layered approach to prevent fraud and protect our community:

### Prevention
- **Advanced Identity Verification**: Partnership with identity verification services to validate government IDs and perform biometric checks
- **Progressive Trust System**: New users have transaction limits that increase with positive platform history
- **Secure Escrow Payments**: All payments are held in escrow until both parties confirm successful completion
- **Prohibited Off-Platform Communication**: All arrangements must stay within our platform until completion
- **Item Verification Technology**: For high-value items, we offer optional serial number registration and verification
- **Smart Contract Documentation**: Blockchain-based rental agreements for transparent, tamper-proof records
- **Identity Theft Protection**: Multi-factor authentication and advanced identity verification to prevent impersonation
- **Property Ownership Verification**: For high-value items, verification of ownership documentation
- **Behavioral Captchas**: Adaptive challenges based on risk level and suspicious activity

### Detection
- **AI-Powered Risk Scoring**: Every transaction receives a real-time risk score based on dozens of signals
- **Behavioral Analysis**: Machine learning models identify unusual patterns in browsing, messaging, and transaction behaviors
- **Network Analysis**: Identifying connections between accounts to detect rings of fraudulent users
- **Listing Analysis**: Automated scanning of images, descriptions, and pricing to flag suspicious listings
- **Community Reporting**: Easy-to-use reporting tools with priority handling for fraud reports
- **Image Forensics**: Detection of manipulated or stolen images in listings
- **Device Fingerprinting**: Tracking suspicious devices across multiple accounts
- **Velocity Monitoring**: Detection of unusual transaction speeds or patterns
- **Cross-listing Detection**: Identifying when the same item is listed by multiple accounts

### Response
- **24/7 Trust & Safety Team**: Dedicated specialists to investigate and respond to issues
- **Rapid Freezing of Suspicious Accounts**: Ability to quickly pause transactions when fraud is suspected
- **Law Enforcement Collaboration**: Established protocols for working with authorities when necessary
- **Comprehensive Insurance**: Protection for both parties in case prevention measures fail
- **Transparent Resolution Process**: Clear steps for handling disputes with defined timelines
- **Graduated Intervention**: Escalating responses based on risk level and violation severity
- **Recovery Assistance**: Support for victims of fraud with clear recovery procedures

### Learning & Improvement
- **Continuous Model Training**: Our fraud detection systems improve with each transaction
- **Regular Security Audits**: Third-party experts regularly test our security measures
- **Fraud Pattern Library**: Constantly updated database of known scam techniques
- **Cross-Platform Intelligence**: Participation in industry-wide fraud prevention networks
- **Autonomous Pattern Recognition**: Self-improving models that identify new fraud techniques
- **Feedback Loops**: Incorporating user reports and resolution outcomes into detection models

## Auction System

RentUp's auction-based rental system enables owners to list unique, high-demand, or seasonal items for competitive bidding:

### Key Features
- **Real-time Bidding**: Live WebSocket-based bidding with instant updates
- **Anti-Sniping Protection**: Automatic time extensions when bids are placed near the end
- **Reserve Pricing**: Set minimum acceptable rental prices
- **Scheduled Auctions**: Plan auctions for optimal timing and participation
- **Bid History**: Transparent record of all bidding activity
- **Automatic Agreement Generation**: Seamless transition from auction to rental agreement
- **Bidder Verification**: Enhanced verification requirements for high-value auctions
- **Auction Analytics**: Insights into bidding patterns and optimal pricing

### Use Cases
- **Seasonal Items**: Holiday decorations, seasonal sports equipment
- **Limited Availability Items**: Rare tools, specialty equipment
- **High-Demand Properties**: Vacation homes during peak seasons
- **Event Spaces**: Venues during popular dates
- **Luxury Items**: Designer fashion, high-end electronics
- **Collectibles**: Vintage items, limited editions

## Item Reliability & History System

RentUp's comprehensive Item Reliability & History System provides transparency and builds trust between renters and owners:

### Key Features
- **Reliability Scoring**: Algorithm-generated scores (0-100) based on item history and maintenance
- **Age Tracking**: Records how long owners have possessed items
- **Rental History**: Tracks number of successful rentals and disputes
- **Maintenance Records**: Documents repairs, servicing, and upkeep
- **Pre-Rental Testing**: Mandatory testing protocols before each rental
- **Last Verified Working**: Timestamps when items were last confirmed functional
- **Backup Availability**: Indicates when owners have backup items available
- **Success Rate**: Percentage of rentals completed without issues
- **Visual Indicators**: Color-coded badges showing reliability levels

### Benefits
- **For Renters**: Increased confidence in item functionality and condition
- **For Owners**: Higher rental rates for well-maintained items with good history
- **For Platform**: Reduced disputes and improved user satisfaction
- **For Community**: Encourages proper maintenance and honest transactions

## Digital Agreement System

RentUp's AI-powered agreement system generates legally-informed digital rental agreements customized to each transaction:

### Key Features
- **Category-Specific Templates**: Specialized agreements for different item types
- **Jurisdiction Awareness**: Adapts to local legal requirements
- **Plain Language Explanations**: Complex terms explained in simple language
- **Visual Summaries**: Graphical representation of key agreement points
- **Digital Signatures**: Legally binding electronic signature support
- **Version Control**: Track changes and amendments
- **Dispute Resolution Clauses**: Clear procedures for handling disagreements
- **Compliance Checking**: Verification of agreement completeness and validity

### Agreement Types
- **Standard Rental Agreements**: For typical rental transactions
- **Auction-Based Agreements**: For items won through the auction system
- **Commercial Agreements**: For business-to-business rentals
- **Special Category Agreements**: For vehicles, real estate, electronics, etc.
- **Service Agreements**: For service-based rentals
- **Digital Product Agreements**: For software and digital content

## Trust Model Inspirations

RentUp's trust and safety framework draws inspiration from successful models across various platforms:

- **Airbnb's** multi-stage verification and secure payment handling
- **eBay's** seller rating system, buyer protection programs, and auction mechanics
- **Turo's** vehicle verification and comprehensive insurance coverage
- **Poshmark's** authentication service for high-value items
- **TaskRabbit's** identity verification and background checks
- **Facebook Marketplace's** integration of social identity and reporting tools
- **Amazon's** review system and A-to-Z guarantee
- **Upwork's** progressive trust building through successful transactions
- **StockX's** authentication process for high-value items
- **DocuSign's** legally binding electronic signature system

We've adapted these proven approaches to the unique challenges of the rental marketplace, creating a comprehensive system that protects all users while maintaining a smooth experience.

---

Last Updated: 2025-05-22
