"""
Simple tests for the AI curation service.
"""

import pytest
from unittest.mock import patch, MagicMock
import numpy as np

from app.services.ai_curation_service import AICurationService


class TestAICurationService:
    """Tests for the AICurationService class."""

    def test_calculate_similarity(self):
        """Test calculating similarity between embeddings."""
        # Arrange
        service = AICurationService(MagicMock())
        embedding1 = [0.1, 0.2, 0.3]
        embedding2 = [0.1, 0.2, 0.3]
        
        # Act
        similarity = service._calculate_similarity(embedding1, embedding2)
        
        # Assert
        assert similarity == 1.0  # Same embeddings should have perfect similarity
        
    def test_calculate_similarity_different(self):
        """Test calculating similarity between different embeddings."""
        # Arrange
        service = AICurationService(MagicMock())
        embedding1 = [0.1, 0.2, 0.3]
        embedding2 = [-0.1, -0.2, -0.3]
        
        # Act
        similarity = service._calculate_similarity(embedding1, embedding2)
        
        # Assert
        assert similarity < 0.5  # Different embeddings should have low similarity
        
    def test_calculate_clarity(self):
        """Test calculating clarity score."""
        # Arrange
        service = AICurationService(MagicMock())
        content = "This is a test content with reasonable length and complexity."
        
        # Act
        clarity = service._calculate_clarity(content)
        
        # Assert
        assert 0 <= clarity <= 1  # Clarity should be between 0 and 1
        
    def test_check_moderation_flags(self):
        """Test checking moderation flags."""
        # Arrange
        service = AICurationService(MagicMock())
        content = "This is a test content with no problematic words."
        
        # Act
        flags = service._check_moderation_flags(content)
        
        # Assert
        assert isinstance(flags, dict)
        assert "spam" in flags
        assert "offensive" in flags
        assert "misleading" in flags
        assert "prohibited" in flags
        assert "irrelevant" in flags
