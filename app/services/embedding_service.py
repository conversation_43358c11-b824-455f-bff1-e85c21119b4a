"""
Text embedding service.
"""

from typing import List, Dict, Any
import numpy as np
import hashlib

from app.core.qdrant import get_qdrant_client
from app.core.config import settings


def get_embedding(text: str) -> List[float]:
    """
    Get embedding for text.
    
    In a real implementation, this would use a model like OpenAI's text-embedding-ada-002
    or a local model like sentence-transformers.
    
    For testing, we'll use a simple hash-based approach.
    """
    # Hash the text to get a deterministic seed
    text_hash = hashlib.md5(text.encode()).hexdigest()
    seed = int(text_hash, 16) % (2**32)
    
    # Use the seed to generate a random vector
    np.random.seed(seed)
    embedding = np.random.normal(0, 1, 384)  # 384 dimensions
    
    # Normalize the vector
    embedding = embedding / np.linalg.norm(embedding)
    
    return embedding.tolist()


def search_by_text(text: str, limit: int = 10) -> List[Dict[str, Any]]:
    """
    Search for items by text.
    """
    # Get embedding for the text
    embedding = get_embedding(text)
    
    # Search in Qdrant
    qdrant_client = get_qdrant_client()
    results = qdrant_client.search(
        collection_name=settings.QDRANT_COLLECTION,
        query_vector=embedding,
        limit=limit
    )
    
    return results


def index_text(text: str, payload: Dict[str, Any], id: str = None) -> None:
    """
    Index text in Qdrant.
    """
    # Get embedding for the text
    embedding = get_embedding(text)
    
    # Create point
    point = {
        "id": id or hashlib.md5(text.encode()).hexdigest(),
        "vector": embedding,
        "payload": payload
    }
    
    # Upsert in Qdrant
    qdrant_client = get_qdrant_client()
    qdrant_client.upsert(
        collection_name=settings.QDRANT_COLLECTION,
        points=[point]
    )
