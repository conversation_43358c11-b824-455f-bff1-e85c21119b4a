"""
Qdrant vector database connection and utilities.
"""

from typing import List, Dict, Any, Optional
import numpy as np

from app.core.config import settings

# Mock Qdrant client for testing
class MockQdrantClient:
    """Mock Qdrant client for testing."""
    
    def __init__(self):
        self.collections = {}
        
    def create_collection(self, collection_name: str, vector_size: int):
        """Create a collection."""
        self.collections[collection_name] = {
            "vectors": [],
            "payloads": [],
            "vector_size": vector_size
        }
        
    def upsert(self, collection_name: str, points: List[Dict[str, Any]]):
        """Upsert points into a collection."""
        if collection_name not in self.collections:
            self.create_collection(collection_name, len(points[0]["vector"]))
            
        for point in points:
            self.collections[collection_name]["vectors"].append(point["vector"])
            self.collections[collection_name]["payloads"].append(point["payload"])
            
    def search(self, collection_name: str, query_vector: List[float], limit: int = 10) -> List[Dict[str, Any]]:
        """Search for similar vectors."""
        if collection_name not in self.collections:
            return []
            
        results = []
        query_vector_np = np.array(query_vector)
        
        for i, vector in enumerate(self.collections[collection_name]["vectors"]):
            vector_np = np.array(vector)
            similarity = np.dot(query_vector_np, vector_np) / (np.linalg.norm(query_vector_np) * np.linalg.norm(vector_np))
            results.append({
                "id": i,
                "score": similarity,
                "payload": self.collections[collection_name]["payloads"][i]
            })
            
        results.sort(key=lambda x: x["score"], reverse=True)
        return results[:limit]


def get_qdrant_client():
    """Get a Qdrant client."""
    # In a real implementation, this would connect to a real Qdrant server
    # from qdrant_client import QdrantClient
    # return QdrantClient(host=settings.QDRANT_HOST, port=settings.QDRANT_PORT)
    
    # For testing, return a mock client
    return MockQdrantClient()
