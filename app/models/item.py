"""
Item model.
"""

from typing import List
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Integer, String, Float, DateTime, ForeignKey, JSON, Table
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class Item(Base):
    """
    Item model.
    """
    __tablename__ = "items"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, index=True, nullable=False)
    description = Column(String, nullable=False)
    price = Column(Float, nullable=False)
    features = Column(JSON)  # List of features
    category_id = Column(Integer, ForeignKey("categories.id"))
    owner_id = Column(Integer, ForeignKey("users.id"))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    owner = relationship("User", back_populates="items")
    category = relationship("Category", back_populates="items")
    rentals = relationship("Rental", back_populates="item")
    bookings = relationship("Booking", back_populates="item")
    reviews = relationship("Review", back_populates="item")
    images = relationship("ItemImage", back_populates="item")
    
    def __repr__(self):
        return f"<Item {self.title}>"
