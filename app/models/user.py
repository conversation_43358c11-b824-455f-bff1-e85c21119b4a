"""
DEPRECATED: This file is deprecated and will be removed in a future version.
Please use backend/app/models/user.py instead, which has the complete and up-to-date user model.
"""

import warnings

# Emit deprecation warning
warnings.warn(
    "app/models/user.py is deprecated and will be removed in a future version. "
    "Please use backend/app/models/user.py instead, which has the complete and up-to-date user model.",
    DeprecationWarning,
    stacklevel=2
)

# Import the User model from the backend to maintain backward compatibility
from backend.app.models.user import User

# Re-export User to maintain backward compatibility
__all__ = ['User']
