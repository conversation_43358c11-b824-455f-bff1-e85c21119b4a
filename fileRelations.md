# RentUp File Relationships

This document provides a detailed overview of the relationships between different files and components in the RentUp project. Understanding these relationships is crucial for maintaining and extending the codebase.

**Last Updated: 2025-05-26**

## Table of Contents

- [Documentation Relationships](#documentation-relationships)
  - [Source of Truth Documents](#source-of-truth-documents)
  - [Knowledge Base](#knowledge-base)
  - [Development Guides](#development-guides)
- [Backend Relationships](#backend-relationships)
  - [API Endpoints and Services](#api-endpoints-and-services)
  - [Database Models and Services](#database-models-and-services)
  - [Vector Search Components](#vector-search-components)
  - [AI Architecture Components](#ai-architecture-components)
- [Frontend Relationships](#frontend-relationships)
  - [Page Components and Their Dependencies](#page-components-and-their-dependencies)
  - [Component Hierarchies](#component-hierarchies)
  - [Services and Components](#services-and-components)
  - [Context Providers and Components](#context-providers-and-components)
- [Cross-Stack Relationships](#cross-stack-relationships)
  - [Frontend-Backend API Integration](#frontend-backend-api-integration)
  - [Authentication Flow](#authentication-flow)
  - [Search Flow](#search-flow)
- [Database Relationships](#database-relationships)
- [Development Dependencies](#development-dependencies)

## Documentation Relationships

### Source of Truth Documents

The following documents serve as the authoritative source of truth for the RentUp project:

- **README.md**: Project overview, tech stack, features, and roadmap
  - Referenced by all development phases and tasks
  - Provides high-level context for the entire project
  - Updated with each major phase completion
  - Contains file size guidelines (300-500 lines per JavaScript file)

- **dirStructure.md**: Directory structure and organization
  - Referenced when creating new files or directories
  - Ensures consistent file organization
  - Updated when new directories or major files are added

- **fileRelations.md**: Relationships between files and components
  - Referenced when modifying component interactions
  - Ensures understanding of dependencies
  - Updated when new relationships are established

- **testMethod.md**: Testing methodology and standards
  - Referenced when writing tests
  - Ensures consistent testing approach
  - Updated when testing strategies evolve

- **build.md**: Build process and deployment procedures
  - Referenced during deployment
  - Ensures consistent build process
  - Updated when build or deployment changes

- **Makefile**: Automation scripts and commands
  - Referenced for common development tasks
  - Provides standardized commands
  - Updated when new automation is added

- **sitemap.md**: Site structure and navigation
  - Referenced when designing new pages
  - Ensures consistent navigation
  - Updated when site structure changes

### Knowledge Base

The knowledge base provides comprehensive guides and best practices:

- **knowledge_base/README.md**: Knowledge base overview
  - Provides an index of all knowledge base documents
  - Updated when new guides are added

- **knowledge_base/frontend/**: Frontend development guides
  - **frontend_trends_2025.md**: Latest frontend trends
    - Referenced when implementing new frontend features
    - Provides context for modern frontend development
  - **react_nextjs_best_practices.md**: React and Next.js best practices
    - Referenced when writing React components
    - Ensures consistent component design
  - **frontend_testing_guide.md**: Frontend testing guide
    - Referenced when writing frontend tests
    - Ensures comprehensive test coverage
  - **code_organization_guidelines.md**: File size and code organization guidelines
    - Referenced when creating or refactoring components
    - Provides strategies for breaking down large files
    - Enforces the 300-500 lines per file standard

- **knowledge_base/backend/**: Backend development guides
  - **backend_trends_2025.md**: Latest backend trends
    - Referenced when implementing new backend features
    - Provides context for modern backend development
  - **fastapi_optimization_guide.md**: FastAPI optimization guide
    - Referenced when optimizing API endpoints
    - Ensures efficient API design
  - **database_optimization_guide.md**: Database optimization guide
    - Referenced when working with database queries
    - Ensures efficient database usage
  - **testing_best_practices.md**: Backend testing best practices
    - Referenced when writing backend tests
    - Ensures comprehensive test coverage
  - **code_organization_guidelines.md**: File size and code organization guidelines
    - Referenced when creating or refactoring Python modules
    - Provides strategies for breaking down large files
    - Enforces the 300-500 lines per file standard

### Development Guides

The development guides provide phase-specific documentation:

- **docs/dev_guide/**: Development phase documentation
  - Each phase has a dedicated folder with:
    - **devPhaseX.md**: Phase overview, goals, and implementation approach
      - Referenced during phase implementation
      - Provides context and goals for the phase
    - **devPhaseX_tasks.md**: Specific tasks and requirements for the phase
      - Referenced for task implementation
      - Provides detailed requirements for each task
  - **devPhase7/**: Backend Optimization and Production Readiness (✅ Completed)
    - **production_readiness_audit_2025.md**: Comprehensive production audit report
    - **phase7_optimization_completion_report.md**: Detailed completion summary
    - Documents enterprise-level security, performance, and deployment readiness
  - **devPhase11/**: Google ADK AI Framework Migration (📅 Planned)
    - **devPhase11.md**: 8-week ADK migration plan and technical approach
    - **devPhase11_tasks.md**: Detailed migration tasks and success criteria
    - Documents modern agentic framework migration strategy

## Backend Relationships

### Code Quality Tools

The code quality tools help maintain code quality and identify redundancies:

- **Redundancy Checker (`backend/scripts/code_quality/check_redundancies.py`)**
  - Identifies similar files based on content similarity
  - Finds unused imports and functions
  - Detects similar functions across files
  - Identifies redundant code patterns
  - Generates comprehensive redundancy reports
  - Used during code reviews and refactoring
  - Documented in `backend/scripts/code_quality/README.md`

### Production Optimization Components (Phase 7 - May 2025)

The production optimization components from Phase 7 provide enterprise-level performance and security:

#### Security Enhancements
- **Enhanced Authentication (`app/core/auth_config.py`)**
  - Implements JWT with 15-minute access tokens and 7-day refresh tokens
  - Provides MFA support and password complexity requirements
  - Used by authentication endpoints for secure user access
  - Integrates with rate limiting and audit logging

- **Rate Limiting System (`app/core/rate_limiting.py`)**
  - Implements Redis-based rate limiting for API endpoints
  - Provides configurable limits per endpoint and user
  - Used by FastAPI middleware for request throttling
  - Prevents DoS attacks and abuse

- **Security Headers Middleware (`app/middleware/security.py`)**
  - Implements OWASP 2025 compliant security headers
  - Provides HSTS, CSP, XSS protection, and frame options
  - Used by FastAPI application for all responses
  - Ensures production-grade security

- **Enhanced Input Validation (`app/core/validation.py`)**
  - Implements Pydantic v2 with strict validation and sanitization
  - Provides XSS protection and content filtering
  - Used by API endpoints for request validation
  - Prevents injection attacks and malicious input

- **Comprehensive Audit Logging (`app/core/audit.py`)**
  - Implements structured audit logging with sensitive data protection
  - Provides event tracking and security monitoring
  - Used by API endpoints for compliance and security
  - Integrates with monitoring and alerting systems

#### Database Optimization
- **Query Optimization (`app/core/query_optimization.py`)**
  - Implements query performance tracking and analysis
  - Provides slow query detection and optimization recommendations
  - Used by database operations for performance monitoring
  - Integrates with PostgreSQL EXPLAIN for query analysis

- **JOIN Optimization (`app/core/join_optimization.py`)**
  - Implements intelligent JOIN strategy selection (LAZY, EAGER, SELECTIN)
  - Provides JOIN pattern analysis and optimization
  - Used by SQLAlchemy queries for performance optimization
  - Reduces query execution time and resource usage

- **Multi-Level Caching (`app/core/query_cache.py`)**
  - Implements SIMPLE, SMART, ADAPTIVE, MULTI_LEVEL caching strategies
  - Provides TTL management and cache invalidation
  - Used by database queries for performance optimization
  - Integrates with Redis for distributed caching

- **Database Configuration (`app/core/db_config.py`)**
  - Implements centralized database configuration management
  - Provides environment-specific settings and validation
  - Used by database connections for consistent configuration
  - Supports PostgreSQL, SQLite, and MySQL

- **Enhanced Connection Management (`app/core/database_connection.py`)**
  - Implements advanced connection pooling with monitoring
  - Provides health checks and connection statistics
  - Used by database operations for efficient connection handling
  - Integrates with performance monitoring

#### Unified Optimization Service
- **Database Optimization Service (`app/services/db_optimization_service.py`)**
  - Provides unified API for all optimization features
  - Implements batch operations and performance statistics
  - Used by API endpoints for optimized database operations
  - Integrates all optimization modules into a single interface

### Advanced Performance Components (Phase 10)

The advanced performance optimization components from Phase 10 enhance the backend's performance:

- **Profiling Middleware (`backend/app/core/profiling.py`)**
  - Provides request/response timing metrics
  - Integrates with cProfile and Py-Spy
  - Used by FastAPI middleware for performance monitoring
  - Connects to performance reporting endpoints

- **Caching System (`backend/app/core/caching/`)**
  - Implements multi-level caching (memory, Redis)
  - Provides TTL-based cache invalidation
  - Supports cache warming for critical data
  - Used by API endpoints for response caching

- **Async Utilities (`backend/app/core/async_utils.py`)**
  - Optimizes async and non-blocking I/O patterns
  - Provides connection pooling for external services
  - Implements efficient background task processing
  - Used by API endpoints for asynchronous operations

- **Database Optimization (`backend/app/core/db/pg_optimizations.py`)**
  - Implements query optimization for PostgreSQL 17
  - Provides index management utilities
  - Configures connection pooling with PgBouncer
  - Optimizes WAL and storage settings

- **Performance Testing Scripts (`backend/tests/performance/`)**
  - Implements API benchmarking tools
  - Provides database query benchmarking
  - Implements cache performance testing
  - Generates performance reports and visualizations

### API Endpoints and Services

The backend API endpoints rely on various services to handle business logic and data access:

- **Items API (`backend/app/api/v1/endpoints/items.py`)**
  - Uses `embedding_service.py` for text embedding generation and search
  - Uses `image_embedding_service.py` for image embedding generation and search
  - Interacts with database models via SQLAlchemy
  - Connects to Qdrant vector database via `core/qdrant.py`

- **Authentication API (`backend/app/api/v1/endpoints/auth.py`)**
  - Uses `core/auth.py` for authentication logic
  - Interacts with `models/user.py` for user data

- **Auction API (`backend/app/api/v1/auctions.py`)**
  - Uses `auction_service.py` for auction business logic
  - Interacts with `models/auction.py` for auction data
  - Implements real-time bidding and auction management
  - Handles auction creation, bidding, and completion

- **Agreement API (`backend/app/api/v1/agreements.py`)**
  - Uses `agreement_service.py` for agreement generation and management
  - Interacts with `models/agreement.py` for agreement data
  - Handles digital signatures and agreement status updates
  - Generates PDF agreements from templates

- **Fraud Prevention API (`backend/app/api/v1/fraud_prevention.py`)**
  - Uses `fraud_detection_service.py` for risk assessment
  - Provides risk scoring for users and transactions
  - Handles fraud reporting and investigation
  - Integrates with AI fraud detection components

- **Chatbot API (`backend/app/api/v1/endpoints/chatbot.py`)**
  - Uses `ai/chatbot/agent.py` for AI-powered chat responses
  - Uses `ai/chatbot/fallback.py` for fallback responses
  - Handles customer support, feedback, and dispute resolution
  - Provides follow-up suggestions and intent detection
  - Stores chat sessions and feedback

- **API Router (`backend/app/api/v1/router.py`)**
  - Includes all endpoint modules
  - Defines API structure and routes
  - Integrates auction, agreement, fraud prevention, and chatbot endpoints

### Database Models and Services

Database models define the data structure, while services implement business logic:

#### Advanced Database Optimization Components

- **Connection Management (`backend/app/core/db/connection.py`)**
  - Implements connection pooling with PgBouncer
  - Provides connection recycling for long-running processes
  - Implements connection timeout and retry mechanisms
  - Used by all database operations for efficient connection handling

- **Query Builder (`backend/app/core/db/query_builder.py`)**
  - Implements efficient query construction
  - Provides query plan caching
  - Optimizes JOIN operations
  - Used by services for building optimized database queries

- **Index Optimization (`backend/app/models/indexes.py`)**
  - Provides utilities for index management
  - Implements BRIN index support for time-series data
  - Adds multi-column indexing strategies
  - Used by database scripts for index optimization

- **Item Model (`backend/app/models/item.py`)**
  - Used by `embedding_service.py` for generating embeddings
  - Used by items API for CRUD operations
  - Contains search metadata fields for vector search
  - Referenced by Auction model for auction items

- **User Model (`backend/app/models/user.py`)**
  - Used by authentication services
  - Referenced by Item model for ownership
  - Referenced by Auction model for sellers and bidders
  - Referenced by Agreement model for owners and renters
  - Used by fraud prevention for risk assessment

- **Auction Model (`backend/app/models/auction.py`)**
  - Used by auction API for auction management
  - Contains auction status, bidding history, and settings
  - References Item model for auction items
  - References User model for sellers and bidders
  - Used by agreement generation for auction-based rentals

- **Agreement Model (`backend/app/models/agreement.py`)**
  - Used by agreement API for agreement management
  - Contains agreement terms, signatures, and status
  - References User model for owners and renters
  - References Item model for rental items
  - May reference Auction model for auction-based agreements

### Vector Search Components

The vector search functionality involves several interconnected components:

- **Qdrant Integration (`backend/app/core/qdrant.py`)**
  - Provides vector database connection
  - Implements vector search operations
  - Used by embedding services

- **Text Embedding Service (`backend/app/services/embedding_service.py`)**
  - Generates text embeddings
  - Implements text-based search
  - Uses Qdrant for vector storage and retrieval

- **Image Embedding Service (`backend/app/services/image_embedding_service.py`)**
  - Generates image embeddings
  - Implements image-based search
  - Uses Qdrant for vector storage and retrieval

### AI Recommendation and Curation Components

The AI recommendation and curation system involves several interconnected components:

- **Recommendation Service (`backend/app/services/recommendation_service.py`)**
  - Provides core recommendation logic
  - Uses preference modeling and item embeddings
  - Implements personalized recommendations
  - Used by items API for recommendation endpoints

- **Preference Modeling (`backend/app/services/preference_modeling.py`)**
  - Tracks and analyzes user preferences
  - Generates preference vectors
  - Used by recommendation service
  - Connects to user interaction data

- **Item Embedding Service (`backend/app/services/item_embedding_service.py`)**
  - Creates enhanced item embeddings
  - Incorporates category and attribute information
  - Used by recommendation service
  - Builds on basic embedding service

- **Context Filtering (`backend/app/services/context_filtering.py`)**
  - Applies contextual filters to recommendations
  - Considers time, location, and user context
  - Used by recommendation service
  - Improves recommendation relevance

- **Visualization Service (`backend/app/services/visualization_service.py`)**
  - Generates visualizations for preferences and embeddings
  - Creates interactive visualization components
  - Used by frontend visualization components
  - Helps explain recommendations to users

- **AI Curation Service (`backend/app/services/ai_curation_service.py`)**
  - Implements content quality assessment
  - Provides automatic categorization and tagging
  - Assists with content moderation
  - Used by admin interface for content management

### AI Architecture Components

The AI architecture follows a Mixture of Experts (MoE) approach with specialized agents for different domains. The implementation is documented in `docs/dev_guide/devPhase6_ai/` with detailed technical specifications in `docs/dev_guide/devPhase6_ai/technical_specs/`.

#### Google ADK Migration (Phase 11 - Planned)

The AI architecture is prepared for migration to Google's Agent Development Kit (ADK) for modern agentic framework capabilities. The migration plan is documented in `docs/dev_guide/devPhase11/` with the following components:

- **Migration Analysis (`backend/adk_migration_output/agents_analysis.json`)**
  - Comprehensive analysis of existing 5 specialized agents
  - Identifies migration complexity and priority for each agent
  - Documents current functionality and tool integrations
  - Used for migration planning and resource allocation

- **Migration Roadmap (`backend/adk_migration_output/migration_plan.json`)**
  - 8-week structured migration plan with 4 phases
  - Defines infrastructure requirements and dependencies
  - Includes risk mitigation strategies and rollback procedures
  - Used for project planning and timeline management

- **ADK Agent Definitions (`backend/adk_migration_output/`)**
  - `recommendation_agent_adk.py`: ADK recommendation agent implementation
  - `fraud_detection_agent_adk.py`: ADK fraud detection agent implementation
  - `main_router_agent_adk.py`: ADK hierarchical router agent implementation
  - Production-ready agent definitions with tool integrations
  - Used as implementation templates for migration

- **Evaluation Framework (`backend/adk_migration_output/evaluation_framework.py`)**
  - Comprehensive testing framework for ADK agents
  - Implements performance benchmarking and quality metrics
  - Provides comparative analysis with existing system
  - Used for validation and quality assurance during migration

#### Current AI Architecture (Custom MoE Implementation)

- **AI Router (`backend/app/ai/router.py`)**
  - Central dispatcher for AI requests
  - Routes requests to appropriate specialized agents
  - Implements fallback strategies and error handling
  - Used by API endpoints that need AI capabilities
  - Documented in `docs/dev_guide/devPhase6_ai/technical_specs/implementation-plan.md`

- **AI Models (`backend/app/ai/models.py`)**
  - Defines Pydantic models for AI requests and responses
  - Used by the router and all AI agents
  - Provides validation and serialization for AI data
  - Documented in `docs/dev_guide/devPhase6_ai/technical_specs/implementation-plan.md`

- **Recommendation Agent (`backend/app/ai/recommendation/agent.py`)**
  - Provides item matching and personalized recommendations
  - Uses embedding service for similarity search
  - Implements caching for performance optimization
  - Used by item listing and detail pages
  - Model selection documented in `docs/dev_guide/devPhase6_ai/technical_specs/comparative-analysis.md`
  - Implementation details in `docs/dev_guide/devPhase6_ai/technical_specs/implementation-plan.md`

- **Fallback Recommendation Agent (`backend/app/ai/recommendation/fallback.py`)**
  - Provides faster, simpler recommendations when needed
  - Used as a backup when the primary agent is unavailable
  - Optimized for low latency over accuracy
  - Used by the router when performance requirements are strict
  - Documented in `docs/dev_guide/devPhase6_ai/technical_specs/implementation-plan.md`

- **Fraud Detection Agent (`backend/app/ai/fraud_detection/agent.py`)**
  - Identifies suspicious patterns and assesses risk
  - Used by user registration, item listing, and transaction flows
  - Integrates with external fraud detection APIs
  - Provides risk scores and explanations
  - Model selection documented in `docs/dev_guide/devPhase6_ai/technical_specs/comparative-analysis.md`
  - Implementation details in `docs/dev_guide/devPhase6_ai/technical_specs/implementation-plan.md`

- **Content Moderation Agent (`backend/app/ai/content_moderation/agent.py`)**
  - Filters inappropriate content and ensures listing quality
  - Used for text and image moderation
  - Implements policy violation detection and content quality assessment
  - Model selection documented in `docs/dev_guide/devPhase6_ai/technical_specs/comparative-analysis.md`
  - Implementation details in `docs/dev_guide/devPhase6_ai/technical_specs/implementation-plan.md`

- **Chatbot Agent (`backend/app/ai/chatbot/agent.py`)**
  - Provides conversational support for users
  - Handles customer support, feedback, and dispute resolution
  - Implements intent detection and contextual responses
  - Used by chatbot API endpoints
  - Documented in `docs/dev_guide/devPhase6_ai_chatbot.md`

- **Fallback Chatbot Agent (`backend/app/ai/chatbot/fallback.py`)**
  - Provides rule-based fallback for the chatbot
  - Used when the primary agent is unavailable
  - Implements pattern matching for intent detection
  - Used by the chatbot API endpoints
  - Documented in `docs/dev_guide/devPhase6_ai_chatbot.md`

- **Embedding Service (`backend/app/ai/utils/embedding_service.py`)**
  - Generates text embeddings for AI components
  - Integrates with OpenAI's embedding API
  - Implements caching and fallback mechanisms
  - Used by recommendation and other AI agents
  - Documented in `docs/dev_guide/devPhase6_ai/technical_specs/implementation-plan.md`

- **Cache Service (`backend/app/ai/utils/cache_service.py`)**
  - Provides caching for AI results
  - Implements TTL-based and pattern-based cache invalidation
  - Used by all AI agents for performance optimization
  - Integrates with Redis for distributed caching
  - Documented in `docs/dev_guide/devPhase6_ai/technical_specs/implementation-plan.md`

### Distributed Caching System

The distributed caching system from Phase 10 enhances the application's performance:

- **Redis Cluster Configuration (`backend/app/core/cache/distributed/redis_cluster.py`)**
  - Configures Redis cluster for distributed caching
  - Implements cache sharding for improved performance
  - Provides failover and high availability
  - Used by all caching components for distributed operation

- **Cache Sharding Strategy (`backend/app/core/cache/distributed/cache_sharding.py`)**
  - Implements cache sharding based on key patterns
  - Provides consistent hashing for key distribution
  - Optimizes cache access patterns
  - Used by Redis cluster configuration

- **Cache Invalidation Mechanisms (`backend/app/core/cache/invalidation/`)**
  - Implements event-based cache invalidation
  - Provides TTL-based invalidation
  - Adds pattern-based cache invalidation
  - Used by services to maintain cache consistency

- **Cache Strategies (`backend/app/core/cache/strategies/`)**
  - Implements write-through and write-behind caching
  - Provides cache-aside pattern for lazy loading
  - Adds intelligent cache eviction policies
  - Used by services for optimal caching behavior

- **Cache Service (`backend/app/services/cache_service.py`)**
  - Provides centralized cache management
  - Implements cache hit/miss monitoring
  - Adds cache size and memory usage tracking
  - Used by all components that need caching

## Frontend Relationships

### Page Components and Their Dependencies

Pages are the top-level components that represent different routes in the application. They typically depend on multiple other components and services.

#### Refactored Component Structure (May 2025 Guidelines)

According to the May 2025 code optimization guidelines, large components exceeding 600 lines should be refactored into smaller, more focused components. The following structure is used for refactored components:

```
frontend/src/pages/PageName/
├── index.tsx                      # Main export file (150-200 lines)
├── components/                    # Page-specific components
│   ├── SectionA.tsx               # Section component (100-200 lines)
│   ├── SectionB.tsx               # Section component (100-200 lines)
│   └── SectionC.tsx               # Section component (100-200 lines)
├── hooks/                         # Page-specific hooks
│   ├── usePageSpecificHookA.ts    # Custom hook (50-100 lines)
│   └── usePageSpecificHookB.ts    # Custom hook (50-100 lines)
└── utils/                         # Page-specific utilities
    ├── helperFunctions.ts         # Helper functions (50-100 lines)
    └── constants.ts               # Constants and types (50-100 lines)
```

This pattern has been applied to the following components:

- `ResponsiveDesignSystem`: Refactored from a 1088-line file into multiple smaller components
- `AuctionCreate`: Refactored from a 920-line file into multiple smaller components
- `RentPlannerInterface`: Refactored from a 859-line file into multiple smaller components
- `EmbeddingVisualization`: Refactored from a 853-line file into multiple smaller components
- `SearchResults`: Refactored from a 822-line file into multiple smaller components
- `ComprehensiveVisualizationDashboard`: Refactored from a 810-line file into multiple smaller components
- `AgreementPreview`: Refactored from a 778-line file into multiple smaller components
- `BusinessMembers`: Refactored from a 767-line file into multiple smaller components
- `BiddingInterface`: Refactored from a 762-line file into multiple smaller components
- `AICurationPanel`: Refactored from a 672-line file into multiple smaller components
- `AuctionDashboard`: Refactored from a 662-line file into multiple smaller components
- `VisualizationPage`: Refactored from a 654-line file into multiple smaller components
- `EnhancedPreferenceVisualization`: Refactored from a 643-line file into multiple smaller components
- `AgreementManagement`: Refactored from a 606-line file into multiple smaller components
- `Login`: Refactored from a 605-line file into multiple smaller components

#### Responsive Design System Page (`frontend/src/pages/ResponsiveDesignSystem/`)

```
ResponsiveDesignSystem/
├── index.tsx                           # Main component (150 lines)
├── components/                         # Extracted components
│   ├── ResponsiveTypography.tsx        # Typography section (50 lines)
│   ├── ResponsiveGridLayouts.tsx       # Grid layouts section (40 lines)
│   ├── ResponsiveCardComponents.tsx    # Card components section (50 lines)
│   ├── EnhancedCardComponents.tsx      # Enhanced card components (100 lines)
│   ├── TouchFriendlyControls.tsx       # Touch controls section (80 lines)
│   ├── ResponsiveStacking.tsx          # Stacking section (50 lines)
│   ├── ResponsiveImages.tsx            # Images section (80 lines)
│   ├── CollapsibleSections.tsx         # Collapsible sections (40 lines)
│   ├── AIFeaturesSection.tsx           # AI features section (120 lines)
│   ├── DesignGuidelinesSection.tsx     # Design guidelines section (100 lines)
│   └── TestingSection.tsx              # Testing section (100 lines)
└── utils/                              # Utility functions
    └── designSystemUtils.ts            # Shared utilities (80 lines)
```

#### Auction Create Page (`frontend/src/pages/AuctionCreate/`)

```
AuctionCreate/
├── index.tsx                           # Main component (100 lines)
├── components/                         # Extracted components
│   ├── StepIndicator.tsx               # Step indicator component (70 lines)
│   ├── BasicInfoStep.tsx               # Step 1 form (100 lines)
│   ├── PricingSettingsStep.tsx         # Step 2 form (200 lines)
│   ├── ImagesReviewStep.tsx            # Step 3 form (200 lines)
│   └── AuctionTips.tsx                 # Tips component (50 lines)
├── hooks/                              # Feature-specific hooks
│   └── useAuctionForm.ts               # Auction form state management (100 lines)
└── utils/                              # Utility functions
    ├── auctionValidation.ts            # Auction validation logic (80 lines)
    └── auctionConstants.ts             # Auction constants and types (60 lines)
```

#### BiddingInterface Component (`frontend/src/components/auctions/BiddingInterface/`)

```
BiddingInterface/
├── index.tsx                           # Main component (130 lines)
├── components/                         # UI Components
│   ├── AuctionInfo.tsx                 # Auction details display (100 lines)
│   ├── BidForm.tsx                     # Bid input and placement (90 lines)
│   ├── AutoBidSettings.tsx             # Auto-bidding controls (80 lines)
│   ├── BidHistory.tsx                  # Bid history and notifications (120 lines)
│   ├── BidConfirmationDialog.tsx       # Bid confirmation modal (50 lines)
│   └── NotificationSnackbar.tsx        # Notification display (40 lines)
├── hooks/                              # Custom Hooks
│   ├── useAuctionData.ts               # Auction data management (150 lines)
│   ├── useBiddingLogic.ts              # Bidding logic and state (180 lines)
│   ├── useWebSocketConnection.ts       # Real-time updates (120 lines)
│   └── useNotifications.ts             # Notification management (60 lines)
├── utils/                              # Utility Functions
│   ├── bidValidation.ts                # Bid validation logic (100 lines)
│   ├── bidFormatting.ts                # Formatting utilities (80 lines)
│   └── types.ts                        # TypeScript interfaces (200 lines)
└── README.md                           # Component documentation (300 lines)
```

#### AICurationPanel Component (`frontend/src/components/admin/AICurationPanel/`)

```
AICurationPanel/
├── index.tsx                           # Main component (120 lines)
├── components/                         # UI Components
│   ├── FilterControls.tsx              # Search and filtering (80 lines)
│   ├── BatchProcessingControls.tsx     # Batch operations (60 lines)
│   ├── ItemsTable.tsx                  # Items display table (140 lines)
│   ├── QualityDialog.tsx               # Quality assessment dialog (150 lines)
│   ├── CategoryDialog.tsx              # Category suggestions dialog (130 lines)
│   ├── ModerationDialog.tsx            # Content moderation dialog (140 lines)
│   └── NotificationSnackbar.tsx        # Notification display (40 lines)
├── hooks/                              # Custom Hooks
│   ├── useCurationData.ts              # Data fetching and management (90 lines)
│   ├── useFilterControls.ts            # Filter state management (80 lines)
│   ├── useBatchProcessing.ts           # Batch operations logic (100 lines)
│   ├── useDialogManagement.ts          # Dialog state and data (180 lines)
│   └── useNotifications.ts             # Notification management (70 lines)
├── utils/                              # Utility Functions
│   ├── types.ts                        # TypeScript interfaces (150 lines)
│   ├── curationUtils.ts                # Curation utility functions (200 lines)
│   └── constants.ts                    # Constants and configuration (120 lines)
└── README.md                           # Component documentation (300 lines)
```

#### AuctionDashboard Component (`frontend/src/components/auctions/AuctionDashboard/`)

```
AuctionDashboard/
├── index.tsx                           # Main component (100 lines)
├── components/                         # UI Components
│   ├── StatsCards.tsx                  # Statistics display (100 lines)
│   ├── TabsAndFilters.tsx              # Navigation and search (80 lines)
│   ├── AuctionsTable.tsx               # Auction listing table (120 lines)
│   ├── ActionMenu.tsx                  # Context menu actions (60 lines)
│   ├── DeleteConfirmationDialog.tsx    # Deletion confirmation (40 lines)
│   └── CreateAuctionButton.tsx         # Create auction CTA (30 lines)
├── hooks/                              # Custom Hooks
│   ├── useAuctionData.ts               # Data fetching and management (140 lines)
│   ├── useAuctionFiltering.ts          # Filtering and sorting logic (120 lines)
│   ├── useAuctionActions.ts            # Action handling (100 lines)
│   └── useAuctionStats.ts              # Statistics calculation (80 lines)
├── utils/                              # Utility Functions
│   ├── types.ts                        # TypeScript interfaces (180 lines)
│   ├── constants.ts                    # Configuration constants (150 lines)
│   └── dateUtils.ts                    # Date formatting utilities (120 lines)
└── README.md                           # Component documentation (300 lines)
```

#### VisualizationPage Component (`frontend/src/pages/VisualizationPage/`)

```
VisualizationPage/
├── index.tsx                           # Main component (80 lines)
├── components/                         # UI Components
│   ├── TabNavigation.tsx               # Tab navigation with keyboard support (80 lines)
│   ├── TabContent.tsx                  # Tab content management (60 lines)
│   ├── AuthenticationPrompt.tsx        # Unauthenticated user prompt (30 lines)
│   ├── InformationSection.tsx          # How recommendations work info (60 lines)
│   ├── FAQSection.tsx                  # Frequently asked questions (30 lines)
│   ├── VisualizationControls.tsx       # Control buttons for visualizations (60 lines)
│   └── tabs/                           # Individual tab components
│       ├── OverviewTab.tsx             # Overview tab content (50 lines)
│       ├── PreferencesTab.tsx          # Preferences visualization tab (40 lines)
│       ├── RelationshipsTab.tsx        # Item relationships tab (40 lines)
│       ├── RecommendationsTab.tsx      # Recommendations tab (20 lines)
│       ├── DashboardTab.tsx            # Dashboard tab (20 lines)
│       └── ComparisonTab.tsx           # Comparison tab (20 lines)
├── hooks/                              # Custom Hooks
│   ├── useTabManagement.ts             # Tab state and URL management (80 lines)
│   └── useAccessibility.ts             # Accessibility features (40 lines)
├── utils/                              # Utility Functions
│   ├── types.ts                        # TypeScript interfaces (120 lines)
│   └── constants.ts                    # Configuration constants (150 lines)
└── README.md                           # Component documentation (300 lines)
```

#### EnhancedPreferenceVisualization Component (`frontend/src/components/Visualization/EnhancedPreferenceVisualization/`)

```
EnhancedPreferenceVisualization/
├── index.tsx                           # Main component (120 lines)
├── components/                         # UI Components
│   ├── LoadingState.tsx                # Skeleton loading UI (60 lines)
│   ├── ErrorState.tsx                  # Error display with retry (80 lines)
│   ├── EmptyState.tsx                  # Empty data state (60 lines)
│   ├── FilterControls.tsx              # Interactive filters (80 lines)
│   ├── PreferenceList.tsx              # Preference list container (50 lines)
│   ├── PreferenceItem.tsx              # Individual preference item (120 lines)
│   ├── DataControls.tsx                # Download and share controls (120 lines)
│   └── InformationSection.tsx          # Educational content (30 lines)
├── hooks/                              # Custom Hooks
│   ├── usePreferenceData.ts            # Data fetching and management (100 lines)
│   ├── usePreferenceFiltering.ts       # Filtering functionality (40 lines)
│   ├── usePreferenceInteraction.ts     # User interactions (30 lines)
│   └── useAccessibility.ts             # Accessibility features (30 lines)
├── utils/                              # Utility Functions
│   ├── types.ts                        # TypeScript interfaces (150 lines)
│   ├── constants.ts                    # Configuration constants (120 lines)
│   └── validation.ts                   # Data validation utilities (80 lines)
└── README.md                           # Component documentation (300 lines)
```

#### AgreementManagement Page (`frontend/src/pages/AgreementManagement/`)

```
AgreementManagement/
├── index.tsx                           # Main component (80 lines)
├── components/                         # UI Components
│   ├── LoadingState.tsx                # Loading UI component (25 lines)
│   ├── ErrorState.tsx                  # Error display component (25 lines)
│   ├── StatisticsCards.tsx             # Agreement statistics cards (90 lines)
│   ├── TabNavigation.tsx               # Tab navigation component (40 lines)
│   ├── SearchAndFilters.tsx            # Search and filtering controls (100 lines)
│   ├── AgreementsTable.tsx             # Table container component (80 lines)
│   └── AgreementRow.tsx                # Individual agreement row (120 lines)
├── hooks/                              # Custom Hooks
│   ├── useAgreementData.ts             # Data fetching and management (100 lines)
│   ├── useAgreementFiltering.ts        # Filtering and search logic (80 lines)
│   ├── useAgreementPagination.ts       # Pagination management (50 lines)
│   └── useAgreementStats.ts            # Statistics calculation (50 lines)
├── utils/                              # Utility Functions
│   ├── types.ts                        # TypeScript interfaces (150 lines)
│   ├── constants.ts                    # Configuration constants (100 lines)
│   └── statusUtils.tsx                 # Status utility functions (100 lines)
└── README.md                           # Component documentation (300 lines)
```

#### Login Page (`frontend/src/pages/Login/`)

```
Login/
├── index.tsx                           # Main component (100 lines)
├── components/                         # UI Components
│   ├── LoginBranding.tsx               # Left side branding section (25 lines)
│   ├── LoginTabs.tsx                   # Tab navigation component (50 lines)
│   ├── MessageDisplay.tsx              # Message display component (40 lines)
│   ├── LoginForm.tsx                   # Password login form (100 lines)
│   ├── EmailField.tsx                  # Email input field (50 lines)
│   ├── PasswordField.tsx               # Password input with toggle (70 lines)
│   ├── CaptchaVerification.tsx         # CAPTCHA component (100 lines)
│   ├── SocialLoginButtons.tsx          # Social media login buttons (80 lines)
│   ├── QRCodeLogin.tsx                 # QR code authentication (100 lines)
│   └── SignUpLink.tsx                  # Registration link (25 lines)
├── hooks/                              # Custom Hooks
│   ├── useLoginState.ts                # UI state management (30 lines)
│   ├── useLoginSecurity.ts             # Security features (80 lines)
│   ├── useLoginMessages.ts             # Message state management (30 lines)
│   ├── useLoginSubmit.ts               # Form submission logic (100 lines)
│   ├── useSocialLogin.ts               # Social authentication (80 lines)
│   └── useQRCodeAuth.ts                # QR code authentication (110 lines)
├── utils/                              # Utility Functions
│   ├── types.ts                        # TypeScript interfaces (120 lines)
│   ├── validation.ts                   # Form validation utilities (80 lines)
│   ├── constants.ts                    # Configuration constants (60 lines)
│   └── authUtils.ts                    # Authentication utilities (70 lines)
├── styles/
│   └── login.css                       # Component-specific styles (150 lines)
└── README.md                           # Component documentation (300 lines)
```

#### Home Page (`frontend/src/pages/Home.tsx`)

```
Home.tsx
├── components/
│   ├── Home/
│   │   ├── Hero.tsx
│   │   ├── CallToAction.tsx
│   │   ├── HowItWorks.tsx
│   │   ├── Testimonials.tsx
│   │   └── TrustSection.tsx
│   ├── Category/
│   │   └── CategoryShowcase.tsx
│   └── Item/
│       └── FeaturedItems.tsx
├── hooks/
│   ├── useMediaQuery.ts (for responsive design)
│   └── useScrollDirection.ts (for sticky header)
└── services/
    └── authService.ts (for checking authentication status)
```

#### Item Details Page (`frontend/src/pages/ItemDetails.tsx`)

```
ItemDetails.tsx
├── components/
│   ├── Item/
│   │   ├── ItemCard.tsx
│   │   ├── ItemReliabilityBadge.tsx
│   │   ├── ItemReliabilityDetails.tsx
│   │   ├── AvailabilityCalendar.tsx
│   │   └── BookingForm.tsx
│   ├── Feedback/
│   │   ├── ReviewForm.tsx
│   │   └── RatingDisplay.tsx
│   └── Recommendations/
│       └── SimilarItemsSection.tsx
├── hooks/
│   ├── useApi.ts (for fetching item data)
│   └── useAuth.ts (for checking if user can book/review)
├── context/
│   └── ToastContext.tsx (for displaying notifications)
└── services/
    ├── itemService.ts (for item data)
    ├── recommendationService.ts (for similar items)
    └── paymentService.ts (for handling booking payments)
```

#### Login Page (`frontend/src/pages/Login.tsx`)

```
Login.tsx
├── shared/ui/
│   ├── Button.tsx
│   ├── Input.tsx
│   └── Checkbox.tsx
├── components/
│   └── Layout/
│       └── LoginTopBar.tsx
├── hooks/
│   ├── useAuth.ts (for authentication logic)
│   └── useToast.ts (for displaying notifications)
├── contexts/
│   ├── AuthContext.tsx (for managing auth state)
│   └── ToastContext.tsx (for displaying notifications)
└── services/
    └── authService.ts (for API calls)
```

#### Search Results Page (`frontend/src/pages/SearchResults.tsx`)

```
SearchResults.tsx
├── components/
│   ├── Search/
│   │   ├── MultiModalSearch.tsx (for search input)
│   │   ├── SearchResults.tsx (for displaying results)
│   │   ├── FilterSidebar.tsx (for filtering results)
│   │   ├── SearchResultsHeader.tsx (for results header)
│   │   ├── SimilarItemsSection.tsx (for recommendations)
│   │   └── SearchInsights.tsx (for analytics)
│   └── Layout/
│       └── ResponsiveContainer.tsx
├── hooks/
│   ├── useApi.ts (for search API calls)
│   └── useMediaQuery.ts (for responsive layout)
└── services/
    ├── itemService.ts (for search functionality)
    └── recommendationService.ts (for recommendations)
```

#### Auction Pages

```
AuctionList.tsx
├── components/
│   ├── Auction/
│   │   └── AuctionCard.tsx
│   └── Layout/
│       └── ResponsiveContainer.tsx
├── hooks/
│   └── useApi.ts (for fetching auctions)
└── services/
    └── auctionService.ts (for auction data)

AuctionDetail.tsx
├── components/
│   ├── Auction/
│   │   ├── AuctionCard.tsx
│   │   ├── AuctionTimer.tsx
│   │   ├── BidForm.tsx
│   │   └── BidHistory.tsx
│   └── Item/
│       └── ItemReliabilityBadge.tsx
├── hooks/
│   ├── useApi.ts (for fetching auction data)
│   └── useAuth.ts (for checking if user can bid)
└── services/
    └── auctionService.ts (for auction operations)
```

#### Business Account Pages

```
BusinessDashboard.tsx
├── components/
│   ├── Business/
│   │   └── BusinessAccountSwitcher.tsx
│   └── Layout/
│       └── ResponsiveContainer.tsx
├── hooks/
│   ├── useAuth.ts (for business account data and permissions)
│   └── useToast.ts (for notifications)
└── services/
    └── businessAccountService.ts (for business account operations)

BusinessCreate.tsx
├── hooks/
│   ├── useAuth.ts (for user data and updating with business account)
│   └── useToast.ts (for notifications)
├── services/
│   └── businessAccountService.ts (for creating business accounts)
└── types/
    └── BusinessAccount.ts (for business account types)

BusinessSelect.tsx
├── hooks/
│   ├── useAuth.ts (for user data and business accounts)
│   └── useToast.ts (for notifications)
└── services/
    └── businessAccountService.ts (for business account operations)

BusinessMembers.tsx
├── hooks/
│   ├── useAuth.ts (for business account data and permissions)
│   └── useToast.ts (for notifications)
└── services/
    └── businessAccountService.ts (for member management)

BusinessSettings.tsx
├── hooks/
│   ├── useAuth.ts (for business account data and permissions)
│   └── useToast.ts (for notifications)
└── services/
    └── businessAccountService.ts (for updating business accounts)
```

#### Agreement Pages

```
AgreementView.tsx
├── components/
│   └── Agreement/
│       ├── AgreementViewer.tsx
│       └── ClauseExplainer.tsx
├── hooks/
│   └── useApi.ts (for fetching agreement data)
└── services/
    └── agreementService.ts (for agreement operations)

AgreementSign.tsx
├── components/
│   └── Agreement/
│       ├── AgreementViewer.tsx
│       └── SignatureCanvas.tsx
├── hooks/
│   └── useApi.ts (for submitting signatures)
└── services/
    └── agreementService.ts (for agreement operations)
```

#### Verification Center

```
VerificationCenter.tsx
├── components/
│   └── Verification/
│       ├── VerificationStep.tsx
│       ├── DocumentUpload.tsx
│       ├── VerificationStatus.tsx
│       └── FraudAlert.tsx
├── hooks/
│   └── useApi.ts (for verification operations)
└── services/
    └── verificationService.ts (for verification operations)
```

#### Visualization Demo

```
VisualizationDemo.tsx
├── components/
│   └── Visualization/
│       ├── PreferenceVisualization.tsx
│       ├── EmbeddingVisualization.tsx
│       └── VisualizationDemo.tsx
├── hooks/
│   ├── useApi.ts (for fetching visualization data)
│   └── usePerformance.ts (for measuring component performance)
└── services/
    └── recommendationService.ts (for visualization data)

EnhancedVisualizationDemo.tsx
├── components/
│   ├── Visualization/
│   │   ├── PreferenceVisualization.tsx
│   │   └── EmbeddingVisualization.tsx (with interactive features)
│   ├── Recommendations/
│   │   └── RecommendationExplanation.tsx (explains recommendations)
│   └── UI/
│       ├── VirtualizedList.tsx (for efficient rendering)
│       ├── ResponsiveImage.tsx (for optimized images)
│       └── Tabs.tsx (for tabbed interface)
├── hooks/
│   ├── useApi.ts (for fetching data)
│   ├── usePerformance.ts (for measuring performance)
│   └── useVirtualization.ts (for list virtualization)
└── services/
    └── recommendationService.ts (for recommendation data)
```

#### Chatbot and Rent Planner

```
ChatbotInterface.tsx
├── components/
│   └── Chatbot/
│       ├── ChatbotButton.tsx (floating action button)
│       └── ChatbotInterface.tsx (main chat interface)
├── hooks/
│   ├── useApi.ts (for chatbot API calls)
│   └── useAuth.ts (for user authentication)
└── services/
    └── chatbotService.ts (for chatbot functionality)

RentPlannerInterface.tsx
├── components/
│   └── RentPlanner/
│       ├── RentPlannerButton.tsx (floating action button)
│       └── RentPlannerInterface.tsx (main planner interface)
├── hooks/
│   ├── useApi.ts (for rent planner API calls)
│   └── useAuth.ts (for user authentication)
└── services/
    └── rentPlannerService.ts (for rent planner functionality)

RentPlannerPage.tsx
├── components/
│   └── RentPlanner/
│       └── RentPlannerInterface.tsx (main planner interface)
├── hooks/
│   └── useAuth.ts (for user authentication)
└── Layout.tsx (page layout)
```

### Component Hierarchies

Components are organized in a hierarchical structure, with higher-level components using lower-level ones.

#### UI Component Hierarchy

```
shared/ui/ (Base UI components)
└── components/ui/ (Re-exports with potential extensions)
    └── Page components and other higher-level components
```

For example, a button would flow through:

```
shared/ui/Button.tsx (Base implementation)
└── components/ui/Button.tsx (Re-exports with potential extensions)
    └── components/CustomHeader.tsx (Uses the button)
        └── pages/Home.tsx (Uses the header)
```

#### Feature Component Hierarchy

Feature components are organized by domain:

```
components/Auction/
├── AuctionCard.tsx
├── AuctionTimer.tsx
├── BidForm.tsx
└── BidHistory.tsx
```

These components are then used in related pages:

```
pages/AuctionDetail.tsx
└── components/Auction/
    ├── AuctionCard.tsx
    ├── AuctionTimer.tsx
    ├── BidForm.tsx
    └── BidHistory.tsx
```

### Services and Components

Frontend services provide API integration for components:

- **Item Service (`frontend/src/services/itemService.ts`)**
  - Used by `MultiModalSearch.tsx` for search requests
  - Used by `SearchResults.tsx` for fetching results
  - Used by `ItemDetails.tsx` for item data

- **Auth Service (`frontend/src/services/authService.ts`)**
  - Used by `Login.tsx` and `Register.tsx`
  - Used by `AuthContext.tsx` for authentication state

- **Business Account Service (`frontend/src/services/businessAccountService.ts`)**
  - Used by `BusinessDashboard.tsx` for business account data
  - Used by `BusinessCreate.tsx` for creating business accounts
  - Used by `BusinessSelect.tsx` for switching between accounts
  - Used by `BusinessMembers.tsx` for member management
  - Used by `BusinessSettings.tsx` for updating business accounts
  - Used by `BusinessAccountSwitcher.tsx` for account switching UI

### Service and Hook Dependencies

Services and hooks provide functionality that is used across multiple components:

#### Authentication Flow

```
services/authService.ts (API calls)
└── hooks/useAuth.ts (React hook wrapper)
    └── context/AuthContext.tsx (Global state)
        └── Multiple components that need auth state
```

#### Toast Notification Flow

```
context/ToastContext.tsx (Global state and methods)
└── hooks/useToast.ts (React hook wrapper)
    └── Multiple components that need to show notifications
```

#### Performance Monitoring Flow

```
utils/performanceMonitoring.ts (Core monitoring utilities)
└── hooks/usePerformance.ts (React hook wrapper)
    └── Multiple components that need performance measurement
        └── Performance metrics collected and reported
```

#### Image Optimization Flow

```
utils/imageOptimization.ts (Image optimization utilities)
└── components/UI/ResponsiveImage.tsx (React component wrapper)
    └── Multiple components that need optimized images
```

#### Service Worker Flow

```
utils/serviceWorker.ts (Service worker registration)
└── main.tsx (Entry point that registers the service worker)
    └── Application with offline capabilities and caching
```

### Context Providers and Components

Context providers share state across components:

- **Auth Context (`frontend/src/context/AuthContext.tsx`)**
  - Used by protected routes
  - Used by components that need user info
  - Used by business account components for authentication and permissions
  - Provides business account switching functionality

- **Toast Context (`frontend/src/context/ToastContext.tsx`)**
  - Used by components that need to display notifications
  - Used by error handling in API calls

## Cross-Stack Relationships

### Frontend-Backend API Integration

The frontend and backend integrate through API calls:

- **Multi-Modal Search Flow**
  - Frontend: `MultiModalSearch.tsx` → `itemService.ts` → API call
  - Backend: API endpoint (`items.py`) → embedding services → Qdrant → database
  - Response: Backend → frontend → `SearchResults.tsx`

### Authentication Flow

Authentication involves both frontend and backend components:

- **Login Flow**
  - Frontend: `Login.tsx` → `authService.ts` → API call
  - Backend: Auth endpoint → JWT generation → database
  - Response: JWT token → frontend → `AuthContext.tsx` → localStorage

### Search Flow

The search functionality spans across the stack:

1. **User Input**
   - User enters text and/or uploads image in `MultiModalSearch.tsx`

2. **API Request**
   - `itemService.ts` sends multipart form data to backend

3. **Backend Processing**
   - `items.py` endpoint receives request
   - Text query processed by `embedding_service.py`
   - Image processed by `image_embedding_service.py`
   - Results combined with configurable weights

4. **Vector Search**
   - Embeddings used to search in Qdrant via `qdrant.py`
   - Filters applied based on user criteria

5. **Result Display**
   - Results returned to frontend
   - `SearchResults.tsx` displays items
   - `SearchMetadataDisplay.tsx` shows search details
   - `SimilarItemsSection.tsx` shows recommendations

### AI Integration Flow

The AI architecture integrates with the rest of the application:

1. **API Request**
   - Frontend component makes a request to an API endpoint
   - API endpoint calls the AI router with appropriate context

2. **AI Router Processing**
   - `router.py` analyzes the request type and context
   - Router selects the appropriate specialized agent
   - Request is forwarded to the selected agent

3. **Agent Processing**
   - Agent processes the request using its specialized logic
   - Agent may call external APIs or use internal models
   - Results are formatted according to the response model

4. **Response Handling**
   - Response is returned to the API endpoint
   - API endpoint integrates AI results with other data
   - Combined response is sent back to the frontend

5. **Frontend Integration**
   - Frontend displays AI-generated results
   - UI components adapt based on AI insights
   - User interactions may trigger new AI requests

## Database Relationships

The database models have relationships that mirror the business domain:

### User-Item Relationship

```
models/user.py (User model)
└── models/item.py (Item model with user_id foreign key)
```

This relationship is reflected in the API and frontend:

```
backend/app/api/v1/endpoints/items.py (API endpoints)
└── frontend/src/pages/UserItems.tsx (User's items page)
```

### Item-Category Relationship

```
models/category.py (Category model)
└── models/item.py (Item model with category_id foreign key)
```

This relationship is used in filtering and navigation:

```
backend/app/api/v1/endpoints/items.py (API endpoints with category filter)
└── frontend/src/components/CategorySelector.tsx (Category selection UI)
```

### Booking-Item-User Relationship

```
models/user.py (User model)
└── models/booking.py (Booking model with user_id foreign key)
    └── models/item.py (Item model referenced by booking)
```

This relationship is used in the booking flow:

```
backend/app/api/v1/endpoints/bookings.py (Booking API)
└── frontend/src/pages/BookingConfirmation.tsx (Booking confirmation page)
```

## Development Dependencies

The project has several development dependencies that are used across different files:

### Frontend Build Process

```
frontend/package.json (Defines dependencies)
├── frontend/tsconfig.json (TypeScript configuration)
├── frontend/vite.config.ts (Vite build configuration)
├── frontend/tailwind.config.ts (Tailwind CSS configuration)
└── frontend/postcss.config.js (PostCSS configuration)
```

### Backend Dependencies

```
backend/requirements.txt (Python dependencies)
├── backend/app/core/config.py (Configuration settings)
└── backend/Dockerfile (Container configuration)
```

### Database Migrations

```
backend/alembic.ini (Alembic configuration)
├── backend/alembic/env.py (Migration environment)
└── backend/alembic/versions/ (Migration scripts)
```

### Testing Dependencies

```
frontend/package.json (Frontend test dependencies)
├── frontend/playwright.config.ts (Playwright configuration)
└── frontend/cypress.config.ts (Cypress configuration)

backend/requirements.txt (Backend test dependencies)
├── backend/pytest.ini (Pytest configuration)
└── backend/tests/performance/ (Performance testing scripts)
    ├── benchmark_api.py (API benchmarking)
    ├── benchmark_database.py (Database benchmarking)
    ├── locustfile.py (Load testing with Locust)
    └── generate_report.py (Performance report generation)
```

### Performance Testing Components

The performance testing components from Phase 10 provide comprehensive benchmarking capabilities:

- **API Benchmarking (`backend/tests/performance/benchmark_api.py`)**
  - Measures API endpoint performance
  - Tests different concurrency levels
  - Collects response time metrics
  - Generates performance reports and visualizations

- **Database Benchmarking (`backend/tests/performance/benchmark_database.py`)**
  - Measures database query performance
  - Tests different query types (simple, joins, complex)
  - Analyzes query plans and execution times
  - Provides optimization recommendations

- **Load Testing (`backend/tests/performance/locustfile.py`)**
  - Simulates realistic user behavior
  - Tests concurrent user scenarios
  - Measures system performance under load
  - Identifies performance bottlenecks

- **Performance Reporting (`backend/tests/performance/generate_report.py`)**
  - Generates comprehensive performance reports
  - Creates performance visualizations
  - Compares benchmark results
  - Tracks performance trends over time

## Refactored Component Structure

### AuctionCreate Component (Refactored)

The AuctionCreate component has been refactored into a modular directory structure:

```
frontend/src/components/Auction/AuctionCreate/
├── index.tsx                      # Main component (100 lines)
├── components/                    # Step components
│   ├── StepIndicator.tsx          # Progress indicator (80 lines)
│   ├── BasicInfoStep.tsx          # Basic information form (150 lines)
│   ├── PricingSettingsStep.tsx    # Pricing and settings (120 lines)
│   ├── ImagesReviewStep.tsx       # Images and review (140 lines)
│   └── AuctionTips.tsx            # Contextual tips (60 lines)
├── hooks/                         # Custom hooks
│   └── useAuctionForm.ts          # Form state management (200 lines)
└── utils/                         # Utilities
    ├── auctionTypes.ts            # Type definitions (80 lines)
    └── formValidation.ts          # Validation logic (100 lines)
```

### RentPlanner Component (Refactored)

The RentPlannerInterface component has been refactored into a modular directory structure:

```
frontend/src/components/RentPlanner/
├── index.tsx                      # Main component (170 lines)
├── components/                    # Step components
│   ├── ChatInterface.tsx          # Chat interface (80 lines)
│   ├── ItemSelection.tsx          # Item selection (150 lines)
│   ├── PlanReview.tsx             # Plan review (200 lines)
│   ├── PlanComplete.tsx           # Completion screen (120 lines)
│   └── MessageBubble.tsx          # Message bubble (40 lines)
├── hooks/                         # Custom hooks
│   ├── useChatMessages.ts         # Chat state management (80 lines)
│   └── useRentalPlan.ts           # Rental plan management (150 lines)
└── utils/                         # Utilities
    ├── types.ts                   # Type definitions (30 lines)
    ├── formatters.ts              # Formatting utilities (50 lines)
    └── mockItemGenerators.ts      # Mock data generators (120 lines)
```

### EmbeddingVisualization Component (Refactored)

The EmbeddingVisualization component has been refactored into a modular directory structure:

```
frontend/src/components/Visualization/EmbeddingVisualization/
├── index.tsx                      # Main component (200 lines)
├── components/                    # UI components
│   ├── LoadingState.tsx           # Loading state (30 lines)
│   ├── ErrorState.tsx             # Error state (40 lines)
│   ├── EmptyState.tsx             # Empty state (40 lines)
│   ├── VisualizationControls.tsx  # Zoom/pan controls (80 lines)
│   ├── CategoryFilter.tsx         # Category filter buttons (60 lines)
│   ├── AdvancedFilters.tsx        # Advanced filtering (60 lines)
│   ├── VisualizationCanvas.tsx    # Main canvas (150 lines)
│   ├── VisualizationTooltip.tsx   # Point tooltips (60 lines)
│   ├── VisualizationLegend.tsx    # Color legend (40 lines)
│   └── VisualizationExplanation.tsx # Help text (50 lines)
├── hooks/                         # Custom hooks
│   ├── useEmbeddingData.ts        # Data fetching (100 lines)
│   └── useVisualizationInteraction.ts # Interaction handling (150 lines)
└── utils/                         # Utilities
    ├── types.ts                   # Type definitions (50 lines)
    ├── constants.ts               # Constants and colors (40 lines)
    └── calculations.ts            # Math utilities (120 lines)
```

### SearchResults Page (Refactored)

The SearchResults page component has been refactored into a modular directory structure:

```
frontend/src/pages/SearchResults/
├── index.tsx                      # Main component (100 lines)
├── components/                    # UI components
│   ├── SearchHeader.tsx           # Search bar header (20 lines)
│   ├── MultiModalInfo.tsx         # Multi-modal search info (50 lines)
│   ├── DevelopmentNotice.tsx      # Development notice (20 lines)
│   ├── ResultsHeader.tsx          # Results header with controls (60 lines)
│   ├── FiltersSidebar.tsx         # Filters sidebar (60 lines)
│   └── ResultsDisplay.tsx         # Results display (100 lines)
├── hooks/                         # Custom hooks
│   ├── useSearchParams.ts         # URL parameter management (80 lines)
│   └── useSearchResults.ts        # Search state management (150 lines)
└── utils/                         # Utilities
    ├── types.ts                   # Type definitions (70 lines)
    ├── constants.ts               # Constants and options (70 lines)
    ├── searchHelpers.ts           # Search utility functions (150 lines)
    └── mockData.ts                # Mock data for development (100 lines)
```

### ComprehensiveVisualizationDashboard Component (Refactored)

The ComprehensiveVisualizationDashboard component has been refactored into a modular directory structure:

```
frontend/src/components/Visualization/ComprehensiveVisualizationDashboard/
├── index.tsx                      # Main component (150 lines)
├── components/                    # UI components
│   ├── TabNavigation.tsx          # Tab navigation (60 lines)
│   ├── TimeFilter.tsx             # Time period filter (30 lines)
│   ├── OverviewTab.tsx            # Overview tab content (100 lines)
│   ├── PreferencesTab.tsx         # Preferences tab content (80 lines)
│   ├── EmbeddingsTab.tsx          # Embeddings tab content (90 lines)
│   ├── RecommendationsTab.tsx     # Recommendations tab content (80 lines)
│   └── DashboardTab.tsx           # Dashboard tab content (120 lines)
├── hooks/                         # Custom hooks
│   ├── useDashboardState.ts       # Dashboard state management (120 lines)
│   └── useRecommendationExplanation.ts # Explanation fetching (80 lines)
└── utils/                         # Utilities
    ├── types.ts                   # Type definitions (60 lines)
    ├── constants.ts               # Constants and mock data (80 lines)
    └── helpers.ts                 # Helper functions (120 lines)
```

### AgreementPreview Component (Refactored)

The AgreementPreview component has been refactored into a modular directory structure:

```
frontend/src/components/agreements/AgreementPreview/
├── index.tsx                      # Main component (120 lines)
├── components/                    # UI components
│   ├── LoadingState.tsx           # Loading state (20 lines)
│   ├── ErrorState.tsx             # Error state (30 lines)
│   ├── AgreementHeader.tsx        # Header with actions (80 lines)
│   ├── PdfViewer.tsx              # PDF viewer with controls (150 lines)
│   ├── AgreementSidebar.tsx       # Tabbed sidebar (60 lines)
│   ├── DetailsTab.tsx             # Agreement details tab (80 lines)
│   ├── PartiesTab.tsx             # Parties and signatures tab (70 lines)
│   ├── HistoryTab.tsx             # Version history tab (60 lines)
│   └── HelpDialogs.tsx            # Help and info dialogs (100 lines)
├── hooks/                         # Custom hooks
│   ├── useAgreementData.ts        # Agreement data fetching (80 lines)
│   ├── usePdfViewer.ts            # PDF viewer state (100 lines)
│   └── useDialogState.ts          # Dialog state management (60 lines)
└── utils/                         # Utilities
    ├── types.ts                   # Type definitions (80 lines)
    ├── constants.ts               # Constants and settings (40 lines)
    └── helpers.ts                 # Helper functions (120 lines)
```

### BusinessMembers Page (Refactored)

The BusinessMembers page component has been refactored into a modular directory structure:

```
frontend/src/pages/Business/BusinessMembers/
├── index.tsx                      # Main component (100 lines)
├── components/                    # UI components
│   ├── LoadingState.tsx           # Loading state (70 lines)
│   ├── PageHeader.tsx             # Page header with actions (40 lines)
│   ├── InviteForm.tsx             # Member invitation form (150 lines)
│   ├── MembersTable.tsx           # Members data table (120 lines)
│   └── InvitationsTable.tsx       # Pending invitations table (100 lines)
├── hooks/                         # Custom hooks
│   ├── useBusinessMembersData.ts  # Members data management (120 lines)
│   └── useInviteForm.ts           # Invite form state (150 lines)
└── utils/                         # Utilities
    ├── types.ts                   # Type definitions (80 lines)
    ├── constants.ts               # Constants and defaults (80 lines)
    └── helpers.ts                 # Helper functions (150 lines)
```

**Benefits of Refactoring:**
- Improved maintainability with focused components
- Enhanced testability through separation of concerns
- Better performance with potential code splitting
- Clearer code organization and navigation

## Conclusion

Understanding these file relationships is essential for effective development and maintenance of the RentUp project. When making changes, consider the impact on related files and components to ensure system integrity.

When adding new features, follow the established patterns of interaction between components, and update this document to reflect new relationships.

---

Last Updated: 2025-07-22
