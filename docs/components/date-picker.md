# Date Picker Component

A customizable date range picker for selecting booking periods and availability.

## Usage

```jsx
import DatePicker from '@/components/ui/DatePicker';

// Basic date range selection
<DatePicker
  ranges={{ startDate: checkIn, endDate: checkOut }}
  onChange={({ selection }) => {
    setCheckIn(selection.startDate);
    setCheckOut(selection.endDate);
  }}
/>

// Disable past dates and specific dates
<DatePicker
  ranges={{ ... }}
  minDate={new Date()}
  disabledDates={[new Date('2024-12-25'), new Date('2025-01-01')]}
/>

// Vertical layout for mobile
<DatePicker
  months={1}
  direction="vertical"
/>

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| priceData | PriceData[] | [] | Array of {date, price, status} |
| minNights | number | 1 | Minimum required nights stay |
| disabledDates | Date[] | - | Dates to disable explicitly |

## Best Practices (Enhanced)
- Load prices dynamically using onMonthChange
- Combine with backend validation for availability
- Use error boundaries for date parsing
- Add loading states for price data
- Localize currency formats based on user region

## Accessibility Updates
- Added aria-live regions for price changes
- Screen reader announcements for disabled dates
- Keyboard shortcuts documentation:
  - Arrow keys: Navigate dates
  - Enter: Select date
  - Escape: Close picker
object

required

{ startDate, endDate } onChange

function

required

Range update handler minDate

Date

today

Earliest selectable date maxDate

Date

-

Latest selectable date disabledDates

Date[]

-

Disabled dates array className

string

-

Container classes months

number

2

Months to display direction

string

horizontal

Layout direction
## Features
- Range selection
- Custom disabled dates
- Mobile-responsive
- Keyboard navigation
- Localization support
- Custom styling
- Multiple month views
## Best Practices
- Highlight check-in/out dates differently
- Disable dates from backend availability
- Show price per night in calendar
- Add clear/reset button
- Use horizontal layout for desktop
- Set min stay duration (3-7 days)
## Accessibility
- ARIA labels for all elements
- Keyboard navigation
- Screen reader support
- Color contrast compliant
- Touch device optimized
## Usage in Bookings

const BookingCalendar = () => {
  const [state, setState] = useState({
    startDate: new Date(),
    endDate: addDays(new Date(), 7)
  });

  return (
    <DatePicker
      ranges={state}
      onChange={({ selection }) => setState(selection)}
      disabledDates={getBlockedDatesFromAPI()}
      minDate={new Date()}
      className="border rounded-lg p-4"
    />
  );
};

## New Features Added

### Price Display
```jsx
const priceData = [
  { date: new Date('2024-12-01'), price: '$150', status: 'available' },
  { date: new Date('2024-12-05'), price: '$180', status: 'booked' },
];

<DatePicker
  priceData={priceData}
  minNights={3}
/>
```

### Minimum Stay Validation
```jsx
```
// Requires 3-night minimum stay
<DatePicker
  minNights={3}
  onChange={({ selection }) => {
    if (differenceInDays(selection.endDate, selection.startDate) < 3) {
      toast.error('Minimum 3 night stay required');
      return;
    }
    // Update state
  }}
/>

### Booking Status Integration
```jsx
```
// Disable booked dates from API
<DatePicker
  priceData={bookedDatesFromAPI}
  disabledDates={maintenanceDates}
/>