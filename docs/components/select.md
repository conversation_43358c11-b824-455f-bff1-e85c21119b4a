# Select Component

A customizable dropdown/select component for form inputs with single or multiple selection capabilities.

## Usage

```jsx
import Select from '@/components/ui/Select';

const options = [
  { value: 'apartment', label: 'Apartment' },
  { value: 'house', label: 'House' },
  { value: 'condo', label: 'Condo' }
];

// Single select
<Select
  label="Property Type"
  options={options}
  placeholder="Select property type"
  onSelect={(value) => console.log(value)}
/>

// Multiple select
<Select
  multiple
  label="Amenities"
  options={[
    { value: 'wifi', label: 'WiFi' },
    { value: 'parking', label: 'Parking' },
    { value: 'pool', label: 'Pool' }
  ]}
/>

// Searchable select
<Select
  searchable
  label="Cities"
  options={[
    { value: 'toronto', label: 'Toronto' },
    { value: 'vancouver', label: 'Vancouver' },
    { value: 'montreal', label: 'Montreal' }
  ]}
/>

## Props Prop Type Default Description options

array

required

Array of {value, label} objects label

string

-

Input label placeholder

string

'Select...'

Placeholder text multiple

boolean

false

Enable multiple selection searchable

boolean

false

Enable search functionality disabled

boolean

false

Disable the input error

string

-

Error message helperText

string

-

Helper/instruction text className

string

-

Additional CSS classes onSelect

function

-

Selection change handler defaultValue

string/array

-

Initial selected value(s)
## Features
- Single & multiple selection
- Search functionality
- Customizable options
- Error states
- Keyboard navigation
- Accessible ARIA labels
- Loading state (via disabled prop)
- Custom styling support
## Accessibility
- Keyboard navigation with arrow keys
- Screen reader support
- Proper focus management
- ARIA labels for all interactive elements
- Color contrast compliant
- Escape key to close dropdown
## Best Practices
- Use for 5+ options (for fewer options, consider radio buttons)
- Group related options when exceeding 10 items
- Always provide a label
- Use helper text for complex selections
- Limit multiple selections to 10 items
- Use search for lists with 15+ items