# Button Component

The Button component is a versatile, reusable button that follows the RentUp design system.

## Usage

```jsx
import { Button } from '@/components/ui/Button';

// Primary button (default)
<Button>Click me</Button>

// Secondary button
<Button variant="secondary">Click me</Button>

// With loading state
<Button isLoading>Processing</Button>

// With icon
<Button leftIcon={<IconName />}>With icon</Button>

// Full width
<Button fullWidth>Full width button</Button>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| children | ReactNode | Required | Button content |
| variant | 'primary' \| 'secondary' \| 'tertiary' \| 'danger' \| 'success' | 'primary' | Button style variant |
| size | 'sm' \| 'md' \| 'lg' | 'md' | Button size |
| fullWidth | boolean | false | Whether button should take full width |
| disabled | boolean | false | Disables the button |
| isLoading | boolean | false | Shows loading spinner |
| leftIcon | ReactNode | undefined | Icon to show before button text |
| rightIcon | ReactNode | undefined | Icon to show after button text |
| type | 'button' \| 'submit' \| 'reset' | 'button' | HTML button type |
| className | string | '' | Additional CSS classes |
| onClick | function | undefined | Click handler function |

## Variants

### Primary
The primary button is used for main actions and calls-to-action.
<Button variant="primary">Primary Action</Button>

### Secondary
Used for secondary actions that complement the primary action.
<Button variant="secondary">Secondary Action</Button>

### Tertiary
Used for less important actions or in space-constrained areas.
<Button variant="tertiary">Tertiary Action</Button>

### Danger
Used for destructive actions like delete or remove.
<Button variant="danger">Delete</Button>

### Success
Used for confirming positive actions.
<Button variant="success">Confirm</Button>

## Sizes

### Small
Compact buttons for smaller spaces.
<Button size="sm">Small Button</Button>

### Medium (Default)
Standard button size.
<Button>Medium Button</Button>

### Large
Large buttons for larger spaces.
<Button size="lg">Large Button</Button>

## States
### Default
The normal state of the button.

### Hover
Visual feedback when a user hovers over the button.

### Focus
Visual indication when the button receives keyboard focus.

### Active
Visual feedback when the button is being clicked.

### Disabled
Used when the action is not available. The button cannot be clicked.
<Button disabled>Disabled Button</Button>

## Loading State
The button can be in a loading state to indicate that an action is being processed.
<Button isLoading>Loading...</Button>

## Accessibility
- Buttons have appropriate focus states for keyboard navigation
- Loading state is announced to screen readers via aria-busy
- Disabled state is properly handled with aria-disabled
- Color contrast meets WCAG 2.1 AA standards (4.5:1 for normal text)
- Interactive elements have a minimum touch target size of 44x44px

## Best Practices
- Use primary buttons for the main action on a page
- Limit the number of primary buttons on a page to avoid confusion
- Use clear, concise text that describes the action
- Position buttons in a consistent location across the application
- For form submissions, use type="submit" and place the button at the bottom of the form
- For destructive actions, use the danger variant and consider adding confirmation