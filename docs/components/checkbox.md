# Checkbox Component

A customizable checkbox input for single selections and group options.

## Usage

```jsx
import Checkbox from '@/components/ui/Checkbox';

// Individual checkbox
<Checkbox 
  label="I agree to terms"
  checked={isChecked}
  onChange={(e) => setIsChecked(e.target.checked)}
/>

// Disabled state
<Checkbox
  label="Disabled option"
  disabled
/>

// Error state
<Checkbox
  label="Invalid selection"
  error="Must accept terms"
  checked={hasError}
/>

## Props Prop Type Default Description id

string

auto

Unique identifier label

string

-

Checkbox label checked

boolean

false

Checked state disabled

boolean

false

Disable interaction error

string

-

Error message helperText

string

-

Additional guidance className

string

-

Container classes onChange

function

required

Change handler
## Features
- Error states
- Helper text
- Custom styling
- Accessibility labels
- Group support (via CheckboxGroup)
## Accessibility
- Proper label association
- ARIA attributes for error states
- Keyboard navigation
- Screen reader support
- Color contrast compliant
## Best Practices
- Use for multiple selections
- Keep labels short (<3 words)
- Group related options
- Use helper text for complex choices
- Mark required fields with asterisk


**2. Radio Documentation:**
```markdown:c%3A%5CUsers%5CUser%5CDesktop%5Cfannlow%5CrentUp%5Cdocs%5Ccomponents%5Cradio.md
# Radio Component

A radio button component for single selection from multiple options.

## Usage

```jsx
import Radio from '@/components/ui/Radio';

// Radio group
<Radio
  name="propertyType"
  label="Apartment"
  value="apartment"
  checked={selectedType === 'apartment'}
  onChange={() => setSelectedType('apartment')}
/>

// Vertical group
<div className="space-y-2">
  <Radio name="lease" label="1 Year" value="1" />
  <Radio name="lease" label="2 Years" value="2" />
</div>

// Error state
<Radio
  name="payment"
  label="Credit Card"
  error="Payment method required"
/>

## Props Prop Type Default Description id

string

auto

Unique identifier name

string

required

Group identifier label

string

-

Radio label value

string

required

Option value checked

boolean

false

Selected state disabled

boolean

false

Disable interaction error

string

-

Error message helperText

string

-

Additional guidance className

string

-

Container classes onChange

function

required

Change handler
## Features
- Group selection
- Error states
- Helper text
- Accessibility compliant
- Custom styling
## Accessibility
- Fieldset/legend support
- ARIA role="radiogroup"
- Keyboard navigation
- Screen reader announcements
- Proper focus states
## Best Practices
- Use for exclusive choices
- Default selection recommended
- Vertical layout for <5 options
- Horizontal layout for 2-3 options
- Always use labels


**3. CheckboxGroup Documentation:**
```markdown:c%3A%5CUsers%5CUser%5CDesktop%5Cfannlow%5CrentUp%5Cdocs%5Ccomponents%5Ccheckbox-group.md
# CheckboxGroup Component

A grouped collection of checkboxes for multiple selections.

## Usage

```jsx
import CheckboxGroup from '@/components/ui/CheckboxGroup';

const amenitiesOptions = [
  { value: 'wifi', label: 'WiFi' },
  { value: 'parking', label: 'Parking' },
  { value: 'pool', label: 'Swimming Pool' }
];

<CheckboxGroup
  label="Select Amenities"
  options={amenitiesOptions}
  values={selectedAmenities}
  onChange={(value) => toggleAmenity(value)}
/>

// With error state
<CheckboxGroup
  label="Required Choices"
  error="Select at least one option"
  options={[...]}
  values={[...]}
/>

## Props Prop Type Default Description options

array

required

{value, label} objects label

string

-

Group label values

array

required

Selected values error

string

-

Error message helperText

string

-

Group guidance disabled

boolean

false

Disable all className

string

-

Container classes onChange

function

required

Change handler
## Features
- Bulk selection
- Error state handling
- Customizable layout
- Accessibility support
- Horizontal/vertical layouts
## Best Practices
- Use for 2-10 options
- Order options by priority
- Add "Select All" when >5 options
- Use helper text for requirements
- Avoid nested groups
## Accessibility
- Fieldset/legend structure
- Group ARIA roles
- Keyboard navigation
- Screen reader support
- Color contrast compliant