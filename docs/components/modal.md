# Modal Component

A flexible modal dialog component for focused user interactions like confirmations and forms.

## Usage

```jsx
import Modal from '@/components/ui/Modal';

// Basic modal
<Modal
  isOpen={isOpen}
  onClose={() => setIsOpen(false)}
  title="Confirm Booking"
>
  <p className="text-gray-600">Are you sure you want to book this property?</p>
</Modal>

// With actions
<Modal
  isOpen={isOpen}
  onClose={closeModal}
  title="Delete Listing"
  primaryAction={{
    label: 'Delete',
    onAction: handleDelete
  }}
  secondaryAction={{
    label: 'Cancel',
    onAction: closeModal
  }}
>
  <p className="text-gray-600">This action cannot be undone.</p>
</Modal>

// Large modal with form
<Modal
  isOpen={isOpen}
  onClose={closeModal}
  title="Submit Review"
  size="lg"
>
  <form onSubmit={handleSubmit}>
    {/* Form fields */}
  </form>
</Modal>

## Props Prop Type Default Description isOpen

boolean

required

Controls modal visibility onClose

function

required

Close handler title

string

required

Modal heading children

node

required

Modal content primaryAction

object

-

{ label, onAction } secondaryAction

object

-

{ label, onAction } size

string

'md'

sm/md/lg/xl disableOutsideClick

boolean

false

Disable click-outside-to-close
## Features
- Multiple sizes
- Esc key to close
- Click outside to close
- Accessible focus management
- Scrollable content
- Action buttons
- Customizable styling
## Accessibility
- Proper focus trapping
- ARIA roles and labels
- Keyboard navigation
- Screen reader support
- Semantic HTML structure
- Escape key support
## Best Practices
- Use for critical actions (confirmations)
- Keep titles under 60 characters
- Avoid complex forms in small modals
- Use primary action for main action
- Provide clear close options
- Limit modal height to 90% viewport