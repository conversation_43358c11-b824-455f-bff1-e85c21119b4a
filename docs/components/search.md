# Search Component

A smart search component with autocomplete and filtering capabilities for property discovery.

## Usage

```jsx
import Search from '@/components/ui/Search';

// Basic search
<Search 
  onSearch={(query) => handleSearch(query)}
/>

// With autocomplete
<Search
  autoCompleteItems={['Downtown', 'Suburbs', 'Waterfront']}
  recentSearches={['Toronto', 'Vancouver']}
/>

// With filters
<Search
  showFilters
  onSearch={(query, filters) => searchWithFilters(query, filters)}
/>

## Props Prop Type Default Description placeholder

string

'Search...'

Input placeholder autoCompleteItems

string[]

[]

Suggestions list recentSearches

string[]

[]

Recent search terms onSearch

function

required

Search handler onClear

function

-

Clear handler debounceTime

number

300

Delay in ms showFilters

boolean

false

Show filter controls filters

object

-

Active filter state
## Features
- Debounced search
- Autocomplete suggestions
- Recent search history
- Integrated filters
- Keyboard navigation
- Mobile-responsive
- Clear functionality
## Accessibility
- ARIA autocomplete attributes
- Keyboard navigation
- Screen reader support
- Focus management
- Color contrast compliant
## Best Practices
- Debounce API calls (300-500ms)
- Limit suggestions to 10 items
- Cache recent searches locally
- Use filters for complex queries
- Add loading states
- Implement empty states
## Integration Example

const PropertySearch = () => {
  const [filters, setFilters] = useState({});
  
  const handleSearch = async (query, newFilters) => {
    const finalFilters = { ...filters, ...newFilters };
    setFilters(finalFilters);
    const results = await api.searchProperties(query, finalFilters);
    // Update results state
  };

  return (
    <Search
      showFilters
      onSearch={handleSearch}
      autoCompleteItems={getLocationSuggestions()}
    />
  );
};

## New Features
## Map Integration (Mapbox)

```jsx
// Required in .env
NEXT_PUBLIC_MAPBOX_TOKEN=your_access_token

<Search
  enableGeoLocation
  showFilters
  onSearch={(query, filters) => {
    // Use filters.location for map center
  }}
/>
```
### Price Filtering
```jsx
```
<Search
  showFilters
  priceRange={[0, 5000]}
  onPriceChange={([min, max]) => setPriceRange([min, max])}
/>

### Search History
```jsx
```
// Save searches to localStorage
const [recentSearches, setRecentSearches] = useState(
  JSON.parse(localStorage.getItem('searchHistory')) || []
);

useEffect(() => {
  localStorage.setItem('searchHistory', JSON.stringify(recentSearches));
}, [recentSearches]);

