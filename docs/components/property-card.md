# Property Card Component

Responsive card component for displaying rental property listings.

## Usage
```jsx
import PropertyCard, { SkeletonPropertyCard } from '@/components/properties/PropertyCard';

// Basic usage
<PropertyCard
  title="Luxury Downtown Apartment"
  images={['/img/apt1.jpg', '/img/apt2.jpg']}
  pricePerNight={250}
  averagePrice={300}
  rating={4.9}
  beds={2}
  baths={2}
  sqft={1200}
/>

// Loading state
<SkeletonPropertyCard />

// With booking dates
<PropertyCard
  bookingDates={`${format(new Date(), 'MMM d')} - ${format(addDays(new Date(), 7), 'MMM d')}`}
/>

## Props Prop Type Description images

string[]

Array of image URLs title

string

Property title pricePerNight

number

Nightly rate averagePrice

number

Area average for comparison rating

number

Star rating (1-5) beds

number

Number of bedrooms baths

number

Number of bathrooms sqft

number

Square footage isFavorite

bool

Favorite state onToggleFavorite

func

Favorite toggle handler
## Features
- Image carousel
- Price comparison badges
- Favorite toggle
- Booking date display
- Responsive design
- Loading skeleton
- Rating display
## Best Practices
- Use 3-5 high-quality images
- Show price comparisons when available
- Implement lazy loading for images
- Use skeleton loading during API fetches
- Maintain aspect ratio (16:9) for images
- Combine with grid layout for listings