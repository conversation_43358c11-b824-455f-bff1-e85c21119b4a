# RentUp Component Library

This document provides an overview of the reusable UI components available in the RentUp platform.

## Core Components

### Buttons
- Primary Button
- Secondary Button
- Tertiary Button
- Icon Button
- Loading Button

### Form Elements
- Text Input
- Textarea
- Checkbox
- Radio Button
- Select Dropdown
- Date Picker
- File Upload

### Navigation
- Navbar
- Sidebar
- Tabs
- Breadcrumbs
- Pagination

### Feedback
- Alert
- Toast Notification
- Modal
- Progress Bar
- Skeleton Loader

### Data Display
- Card
- Table
- List
- Badge
- Avatar
- Rating

### Layout
- Container
- Grid
- Divider
- Spacer

## Domain-Specific Components

### Listing Components
- Listing Card
- Listing Detail
- Listing Gallery
- Price Display
- Availability Calendar

### User Components
- User Profile Card
- Review Card
- Verification Badge
- Trust Score Display

### Booking Components
- Booking Form
- Booking Summary
- Booking Timeline
- Payment Method Selector

## Component Implementation

Each component is implemented using React and Tailwind CSS, following our design system guidelines. Components are built to be:

- **Accessible**: Following WCAG 2.1 AA standards
- **Responsive**: Adapting to all screen sizes
- **Reusable**: Accepting props for customization
- **Consistent**: Following our design language

## Usage Examples

For detailed usage examples of each component, see the individual component documentation files in this directory.