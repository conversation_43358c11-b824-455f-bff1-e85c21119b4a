# Input Component

The Input component is a versatile form control that follows the RentUp design system.

## Usage

```jsx
import { Input } from '@/components/ui/Input';

// Basic input
<Input label="Email" placeholder="Enter your email" />

// With helper text
<Input 
  label="Username" 
  placeholder="Enter username" 
  helperText="Username must be at least 3 characters"
/>

// With error state
<Input 
  label="Password" 
  type="password" 
  error="Password is required"
/>

// With icons
<Input 
  label="Search" 
  placeholder="Search listings" 
  leftIcon={<SearchIcon className="h-5 w-5 text-gray-400" />}
  rightIcon={<XIcon className="h-5 w-5 text-gray-400" />}
/>

// Disabled state
<Input 
  label="Disabled input" 
  placeholder="This input is disabled" 
  disabled
/>

## Props Prop Type Default Description id

string

auto-generated

Unique input identifier label

string

-

Input label text type

string

'text'

Input type (text, email, password, etc) placeholder

string

-

Placeholder text helperText

string

-

Additional descriptive text error

string

-

Error message text disabled

bool

false

Disables the input required

bool

false

Marks as required field className

string

-

Additional CSS classes fullWidth

bool

true

Full-width display leftIcon

ReactNode

-

Left-side icon element rightIcon

ReactNode

-

Right-side icon element
## Variants
### Default Input
<Input label="First Name" placeholder="John Doe" />

### With Helper Text
```jsx
```
<Input
  label="Username"
  helperText="Letters and numbers only"
/>

### Error State
```jsx
```
<Input
  label="Email"
  error="Invalid email address"
/>

### Disabled State
```jsx
```
<Input
  label="Disabled Field"
  value="Non-editable text"
  disabled
/>

### With Icons
```jsx
```
<Input
  label="Search"
  leftIcon={<SearchIcon />}
  rightIcon={<FilterIcon />}
/>

## Input Types
### Text
```jsx
```
<Input type="text" label="Full Name" />

### Email
```jsx
```
<Input type="email" label="Work Email" />

### Password
```jsx
```
<Input type="number" label="Age" min="18" max="99" />

## Accessibility
- Properly associated labels using htmlFor / id
- ARIA attributes for error states ( aria-invalid , aria-describedby )
- Keyboard navigation support
- Screen reader announcements for errors
- Color contrast meeting WCAG 2.1 AA standards
## Best Practices
- Always provide a clear label
- Use helper text for additional guidance
- Validate inputs progressively
- Use appropriate input types
- Group related form fields
- Mark required fields consistently
- Provide clear error messages
- Use icons sparingly for clarity


This version includes all necessary sections with proper formatting and complete documentation for the Input component.