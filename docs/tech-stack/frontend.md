# Frontend Technology Stack

This document outlines the frontend technology stack used in the RentUp project, including the latest features, best practices, and implementation guidelines as of 2025.

## Core Technologies

### React 19

React 19 introduces several new features and improvements that we leverage in our application:

#### Key Features
- **React Compiler**: Automatically optimizes components for better performance without manual memoization
- **Actions**: Simplified data mutations with built-in loading and error states
- **Document Metadata**: Native support for managing document metadata (title, meta tags)
- **Asset Loading**: Improved asset loading with built-in suspense integration
- **Improved Server Components**: Enhanced server component architecture with better hydration

#### Best Practices
- Use the new `use` hook for data fetching and resource management
- Leverage React Server Components for improved performance
- Implement the new React Cache for efficient data caching
- Use the new `useFormStatus` and `useFormState` hooks for form handling
- Implement the new error boundary improvements for better error handling

#### Implementation Guidelines
- All new components should be created as functional components
- Use TypeScript for all component definitions
- Implement proper code splitting using React.lazy and Suspense
- Follow the React team's recommendations for file structure and naming conventions

### Next.js 14

Next.js 14 provides the framework for our React application with several advanced features:

#### Key Features
- **App Router**: Full support for the app directory structure
- **Server Actions**: Enhanced server-side functionality with improved type safety
- **Partial Prerendering**: Combines static and dynamic content for optimal performance
- **Turbopack**: Faster development experience with improved bundling
- **Image Optimization**: Advanced image optimization with next/image

#### Best Practices
- Use the app directory structure for all new routes
- Implement server components where appropriate
- Use server actions for form submissions and data mutations
- Leverage the built-in data fetching methods
- Implement proper metadata management for SEO

#### Implementation Guidelines
- Follow the Next.js recommended file structure
- Use the built-in routing system for all navigation
- Implement proper error handling with error.tsx files
- Use the built-in loading states with loading.tsx files
- Leverage the built-in image optimization with next/image

### Tailwind CSS 4

Tailwind CSS 4 provides our utility-first CSS framework with several new features:

#### Key Features
- **Lightning CSS**: Faster compilation and smaller bundle sizes
- **Container Queries**: Support for container-based responsive design
- **Scroll-Driven Animations**: Native support for scroll-based animations
- **Subgrid Support**: Enhanced grid layouts with subgrid support
- **Color Functions**: New color manipulation functions

#### Best Practices
- Use the new container query classes for responsive design
- Implement the new animation utilities for interactive elements
- Leverage the new color functions for consistent theming
- Use the new layout utilities for improved responsive design
- Implement the new form styling utilities for consistent form elements

#### Implementation Guidelines
- Follow the utility-first approach for all styling
- Use the @apply directive sparingly and only for repeated patterns
- Implement proper responsive design using the built-in breakpoints
- Use the theme configuration for consistent colors, spacing, and typography
- Leverage the JIT compiler for optimal performance

## State Management

### React Query + Zustand

We use a combination of React Query for server state and Zustand for client state:

#### Key Features
- **React Query 5**: Enhanced data fetching with improved suspense integration
- **Zustand 5**: Lightweight state management with improved TypeScript support
- **Immer Integration**: Built-in support for immutable state updates
- **Devtools**: Enhanced debugging capabilities
- **Persistence**: Improved persistence options for client state

#### Best Practices
- Use React Query for all server state management
- Implement Zustand for client-only state
- Leverage the built-in persistence options for user preferences
- Use the devtools during development for debugging
- Implement proper error handling and loading states

#### Implementation Guidelines
- Create separate stores for different domains
- Use TypeScript for all store definitions
- Implement proper selectors for optimal performance
- Use the built-in middleware for logging and persistence
- Follow the recommended patterns for combining React Query and Zustand

## Form Handling

### React Hook Form + Zod

We use React Hook Form with Zod for form validation:

#### Key Features
- **React Hook Form 8**: Enhanced performance with improved TypeScript support
- **Zod 4**: Schema-based validation with improved error messages
- **Form Context**: Simplified form state management
- **Field Arrays**: Improved support for dynamic form fields
- **Form Submission**: Enhanced submission handling with built-in error management

#### Best Practices
- Use Zod schemas for all form validation
- Implement controlled components for complex form elements
- Use the form context for form state management
- Leverage the built-in error handling
- Implement proper form submission handling

#### Implementation Guidelines
- Create reusable form components
- Use TypeScript for all form definitions
- Implement proper error messages for validation errors
- Use the built-in form state for loading and submission states
- Follow the recommended patterns for form submission

## Testing

### Jest + React Testing Library + Cypress

We use a combination of Jest, React Testing Library, and Cypress for testing:

#### Key Features
- **Jest 30**: Enhanced performance with improved TypeScript support
- **React Testing Library 15**: Improved component testing with better error messages
- **Cypress 13**: End-to-end testing with improved component testing support
- **MSW 2**: Mock Service Worker for API mocking
- **Testing Library Hooks**: Improved testing for React hooks

#### Best Practices
- Write unit tests for all utility functions
- Implement component tests for all UI components
- Use integration tests for complex interactions
- Implement end-to-end tests for critical user flows
- Use MSW for API mocking in tests

#### Implementation Guidelines
- Follow the Testing Library guiding principles
- Use the recommended patterns for testing hooks
- Implement proper test organization with describe and it blocks
- Use the built-in assertions for testing
- Follow the recommended patterns for testing async code

## Performance Optimization

### Performance Best Practices

We follow several best practices for frontend performance:

#### Key Techniques
- **Code Splitting**: Implement proper code splitting for optimal bundle sizes
- **Image Optimization**: Use next/image for automatic image optimization
- **Font Optimization**: Implement proper font loading strategies
- **CSS Optimization**: Use Tailwind's JIT compiler for minimal CSS
- **JavaScript Optimization**: Implement proper tree shaking and dead code elimination

#### Best Practices
- Use the built-in performance tools for monitoring
- Implement proper lazy loading for images and components
- Use the built-in caching mechanisms for optimal performance
- Implement proper error boundaries for resilience
- Use the built-in performance metrics for monitoring

#### Implementation Guidelines
- Follow the recommended patterns for code splitting
- Use the built-in tools for performance monitoring
- Implement proper caching strategies
- Use the built-in optimization features
- Follow the recommended patterns for performance optimization

## Security

### Security Best Practices

We follow several best practices for frontend security:

#### Key Techniques
- **Content Security Policy**: Implement proper CSP headers
- **HTTPS**: Enforce HTTPS for all connections
- **XSS Protection**: Implement proper XSS protection
- **CSRF Protection**: Implement proper CSRF protection
- **Input Validation**: Implement proper input validation

#### Best Practices
- Use the built-in security features of Next.js
- Implement proper authentication and authorization
- Use the built-in CSRF protection
- Implement proper input validation with Zod
- Use the built-in security headers

#### Implementation Guidelines
- Follow the OWASP Top 10 recommendations
- Use the built-in security features
- Implement proper error handling
- Use the built-in security headers
- Follow the recommended patterns for security

## Accessibility

### Accessibility Best Practices

We follow several best practices for frontend accessibility:

#### Key Techniques
- **Semantic HTML**: Use proper semantic HTML elements
- **ARIA Attributes**: Implement proper ARIA attributes
- **Keyboard Navigation**: Ensure proper keyboard navigation
- **Screen Reader Support**: Implement proper screen reader support
- **Color Contrast**: Ensure proper color contrast

#### Best Practices
- Use the built-in accessibility features of React
- Implement proper focus management
- Use the built-in accessibility testing tools
- Implement proper error messages
- Use the built-in accessibility attributes

#### Implementation Guidelines
- Follow the WCAG 2.2 guidelines
- Use the built-in accessibility features
- Implement proper focus management
- Use the built-in accessibility testing tools
- Follow the recommended patterns for accessibility

## Development Workflow

### Development Best Practices

We follow several best practices for frontend development:

#### Key Techniques
- **Git Flow**: Follow the Git Flow branching model
- **Code Reviews**: Implement proper code reviews
- **CI/CD**: Use GitHub Actions for CI/CD
- **Documentation**: Implement proper documentation
- **Testing**: Implement proper testing

#### Best Practices
- Use the built-in development tools
- Implement proper code reviews
- Use the built-in CI/CD features
- Implement proper documentation
- Use the built-in testing tools

#### Implementation Guidelines
- Follow the recommended patterns for development
- Use the built-in development tools
- Implement proper code reviews
- Use the built-in CI/CD features
- Follow the recommended patterns for documentation

## Resources

- [React Documentation](https://react.dev/)
- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [React Query Documentation](https://tanstack.com/query/latest)
- [Zustand Documentation](https://zustand-demo.pmnd.rs/)
- [React Hook Form Documentation](https://react-hook-form.com/)
- [Zod Documentation](https://zod.dev/)
- [Jest Documentation](https://jestjs.io/)
- [React Testing Library Documentation](https://testing-library.com/docs/react-testing-library/intro/)
- [Cypress Documentation](https://docs.cypress.io/)

---

Last Updated: July 10, 2025
