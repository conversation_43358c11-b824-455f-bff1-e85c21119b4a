# RentUp Technology Stack Documentation

This directory contains comprehensive documentation for the technology stack used in the RentUp project, including the latest features, best practices, and implementation guidelines as of 2025.

## Overview

RentUp is an AI-powered rental marketplace connecting people who want to rent items with those who have items to rent. The platform handles the entire rental process from discovery to payment and return, featuring auctions, digital agreements, and fraud prevention.

## Documentation Structure

The documentation is organized into the following sections:

1. [Frontend Technology Stack](./frontend.md)
   - React 19, Next.js 14, Tailwind CSS 4
   - React Query + Zustand (State Management)
   - React Hook Form + Zod (Form Handling)
   - Testing and Performance Optimization

2. [Backend Technology Stack](./backend.md)
   - Python 3.12+, FastAPI
   - PostgreSQL 17-alpine, Qdrant (Vector DB)
   - JWT + Social Login (Authentication)
   - Stripe API (Payments)
   - WebSockets (Real-time Communication)
   - Jinja2 (Templates)

3. [AI/ML Technology Stack](./ai-ml.md)
   - Nomic (Embeddings)
   - Hugging Face (LLMs)
   - Qdrant (Vector Database)
   - RAG (Retrieval-Augmented Generation)
   - Recommendation System
   - Fraud Detection

4. [Infrastructure and DevOps](./infrastructure.md)
   - Docker (Containerization)
   - AWS (Cloud Infrastructure)
   - GitHub Actions (CI/CD)
   - Datadog (Monitoring)
   - Terraform (Infrastructure as Code)

5. [Security Best Practices](./security.md)
   - Authentication and Authorization
   - Data Protection
   - Network Security
   - Application Security
   - Infrastructure Security
   - Monitoring and Incident Response
   - Compliance
   - Development Security

## How to Use This Documentation

This documentation serves as a reference for developers working on the RentUp project. It provides detailed information about the technology stack, best practices, and implementation guidelines.

### For New Developers

1. Start with the overview sections to understand the high-level architecture
2. Review the specific technology documentation relevant to your area of work
3. Follow the best practices and implementation guidelines
4. Use the resources section for additional information

### For Experienced Developers

1. Use the documentation as a reference for best practices
2. Ensure your implementations follow the guidelines
3. Contribute to the documentation by keeping it up-to-date
4. Share your knowledge with other developers

## Keeping Documentation Up-to-Date

This documentation should be kept up-to-date as the technology stack evolves. When making significant changes to the technology stack, please update the relevant documentation.

### Update Process

1. Identify the documentation that needs to be updated
2. Make the necessary changes
3. Update the "Last Updated" date at the bottom of the file
4. Submit a pull request for review

## Resources

- [RentUp Development Plan](../development-plan.md)
- [RentUp Architecture Documentation](../architecture/system-architecture.md)
- [RentUp API Specification](../api-specification.md)

---

Last Updated: July 10, 2025
