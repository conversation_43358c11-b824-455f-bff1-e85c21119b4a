# Apple Sign In Integration

This document provides an overview of the Apple Sign In integration in the RentUp platform.

## Overview

Apple Sign In provides a secure and privacy-friendly way for users to authenticate with the RentUp platform using their Apple ID. This integration complements our existing social login options (Google and Facebook) and enhances the user experience for Apple device users.

## Implementation Details

### Frontend Components

1. **Configuration**
   - Added Apple configuration to `auth.config.ts`
   - Configured client ID, redirect URI, and required scopes

2. **Login Component**
   - Integrated Apple Sign In button in the login page
   - Implemented Apple Sign In handler
   - Added error handling for Apple Sign In failures

3. **Authentication Service**
   - Updated `socialLogin` function to support Apple as a provider
   - Implemented token handling for Apple authentication

4. **Routing**
   - Added callback route for Apple Sign In in `App.tsx`

### Required Environment Variables

The following environment variables need to be set for Apple Sign In to work:

```
VITE_APPLE_CLIENT_ID=your_apple_client_id
VITE_APPLE_REDIRECT_URI=your_redirect_uri (optional, defaults to window.location.origin + '/auth/apple/callback')
```

## User Flow

1. User clicks the "Sign in with Apple" button on the login page
2. Apple's authentication popup appears
3. User authenticates with their Apple ID (and may choose to share or hide their email)
4. Upon successful authentication, Apple redirects to our callback URL with an authorization code
5. Our backend exchanges the authorization code for tokens
6. User is logged in and redirected to the home page

## Testing

Tests for the Apple Sign In functionality are available in `frontend/tests/apple-signin.spec.js`. These tests verify:

1. The presence of the Apple Sign In button on the login page
2. Proper error handling for failed Apple Sign In attempts
3. Correct redirect URI configuration

## Security Considerations

1. **Token Handling**: Apple tokens are handled securely and never exposed to the client
2. **HTTPS**: All communication with Apple's authentication servers is done over HTTPS
3. **Scope Limitation**: Only necessary scopes (email, name) are requested
4. **Privacy**: Users can choose to hide their email from our application

## Future Improvements

1. **Native App Integration**: Extend Apple Sign In to the mobile app
2. **Sign In with Apple Button**: Use Apple's official button component when available
3. **Account Linking**: Allow users to link their Apple ID to an existing account
4. **Refresh Token Handling**: Implement proper refresh token rotation for Apple Sign In

## References

- [Sign in with Apple Documentation](https://developer.apple.com/sign-in-with-apple/get-started/)
- [Apple Authentication Services](https://developer.apple.com/documentation/authenticationservices)
- [Implementing User Authentication with Sign in with Apple](https://developer.apple.com/documentation/authenticationservices/implementing_user_authentication_with_sign_in_with_apple)
