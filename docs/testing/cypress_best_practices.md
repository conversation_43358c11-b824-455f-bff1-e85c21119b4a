# Cypress Testing Best Practices

This document outlines best practices for writing Cypress tests for the RentUp platform.

## Table of Contents

- [Project Structure](#project-structure)
- [Selectors](#selectors)
- [Page Objects](#page-objects)
- [Commands](#commands)
- [Assertions](#assertions)
- [Network Requests](#network-requests)
- [Fixtures](#fixtures)
- [Performance](#performance)
- [Visual Testing](#visual-testing)
- [Continuous Integration](#continuous-integration)

## Project Structure

Organize your Cypress tests in a structured way:

```
cypress/
├── e2e/                     # End-to-end tests
│   ├── phase1/              # Phase 1 tests
│   ├── phase2/              # Phase 2 tests
│   └── phase3/              # Phase 3 tests
├── fixtures/                # Test data
│   ├── users.json           # User data
│   └── items.json           # Item data
├── support/                 # Support files
│   ├── commands.js          # Custom commands
│   └── page-objects/        # Page objects
│       ├── HomePage.js      # Home page object
│       └── ItemPage.js      # Item page object
└── plugins/                 # Plugins
    └── index.js             # Plugin configuration
```

## Selectors

Use selectors that are resilient to changes in the implementation:

```javascript
// Bad - brittle selectors
cy.get('.btn-primary').click();
cy.get('div > p > span').should('contain', 'Hello');

// Good - resilient selectors
cy.get('[data-testid="submit-button"]').click();
cy.contains('Submit').click();
cy.get('button').contains('Submit').click();
```

### Selector Priority

1. **Data attributes**: `data-testid`, `data-cy`, etc.
2. **Semantic selectors**: `cy.get('button').contains('Submit')`
3. **Class/ID selectors**: Only if they are stable and meaningful

## Page Objects

Use the Page Object Model (POM) pattern to organize your tests:

```javascript
// cypress/support/page-objects/HomePage.js
class HomePage {
  visit() {
    cy.visit('/');
    return this;
  }

  getSearchInput() {
    return cy.get('[data-testid="search-input"]');
  }

  getSearchButton() {
    return cy.get('[data-testid="search-button"]');
  }

  search(term) {
    this.getSearchInput().type(term);
    this.getSearchButton().click();
    return this;
  }
}

export default new HomePage();
```

Usage in tests:

```javascript
// cypress/e2e/phase1/home.spec.js
import HomePage from '../../support/page-objects/HomePage';

describe('Home Page', () => {
  beforeEach(() => {
    HomePage.visit();
  });

  it('should allow searching for items', () => {
    HomePage.search('bike');
    cy.url().should('include', '/search?q=bike');
    cy.get('[data-testid="search-results"]').should('be.visible');
  });
});
```

## Commands

Create custom commands for common operations:

```javascript
// cypress/support/commands.js
Cypress.Commands.add('login', (email, password) => {
  cy.visit('/login');
  cy.get('[data-testid="email-input"]').type(email);
  cy.get('[data-testid="password-input"]').type(password);
  cy.get('[data-testid="login-button"]').click();
  cy.url().should('include', '/dashboard');
});

Cypress.Commands.add('waitForNetworkIdle', (timeout = 10000) => {
  cy.window().then(win => {
    return new Cypress.Promise((resolve) => {
      let requestCounter = 0;
      const onRequestStart = () => requestCounter++;
      const onRequestEnd = () => {
        requestCounter--;
        if (requestCounter === 0) {
          setTimeout(resolve, 500); // Wait a bit to ensure no new requests
        }
      };

      win.addEventListener('fetch', onRequestStart);
      win.addEventListener('fetchdone', onRequestEnd);
      
      setTimeout(resolve, timeout); // Fallback timeout
    });
  });
});
```

Usage in tests:

```javascript
it('should login and see dashboard', () => {
  cy.login('<EMAIL>', 'password123');
  cy.get('[data-testid="dashboard-welcome"]').should('contain', 'Welcome');
});
```

## Assertions

Use explicit assertions with clear error messages:

```javascript
// Bad - implicit assertion
cy.get('[data-testid="item-price"]');

// Good - explicit assertion
cy.get('[data-testid="item-price"]').should('be.visible');
cy.get('[data-testid="item-price"]').should('contain', '$');

// Better - chained assertions with clear error messages
cy.get('[data-testid="item-price"]')
  .should('be.visible', { timeout: 10000 })
  .and('contain', '$')
  .and('not.contain', 'Loading');
```

## Network Requests

Use `cy.intercept()` to mock or spy on network requests:

```javascript
// Mock API response
cy.intercept('GET', '/api/items', { fixture: 'items.json' }).as('getItems');

// Spy on API request
cy.intercept('POST', '/api/login').as('loginRequest');
cy.get('[data-testid="login-button"]').click();
cy.wait('@loginRequest').its('response.statusCode').should('eq', 200);
```

## Fixtures

Use fixtures for test data:

```javascript
// cypress/fixtures/users.json
{
  "users": [
    {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "password": "password123"
    }
  ]
}
```

Usage in tests:

```javascript
it('should display user information', () => {
  cy.fixture('users').then((data) => {
    const user = data.users[0];
    cy.login(user.email, user.password);
    cy.get('[data-testid="user-name"]').should('contain', user.name);
  });
});
```

## Performance

Optimize test performance:

1. **Avoid unnecessary visits**: Use `cy.session()` to preserve login state
2. **Reduce wait times**: Use `cy.waitForNetworkIdle()` instead of arbitrary timeouts
3. **Batch commands**: Chain commands when possible
4. **Use aliases**: Define aliases for frequently used elements

```javascript
// Bad - multiple visits and arbitrary timeouts
beforeEach(() => {
  cy.visit('/login');
  cy.get('#email').type('<EMAIL>');
  cy.get('#password').type('password123');
  cy.get('button[type="submit"]').click();
  cy.wait(5000); // Arbitrary timeout
});

// Good - use session and wait for specific conditions
beforeEach(() => {
  cy.session('user', () => {
    cy.visit('/login');
    cy.get('[data-testid="email-input"]').type('<EMAIL>');
    cy.get('[data-testid="password-input"]').type('password123');
    cy.get('[data-testid="login-button"]').click();
    cy.url().should('include', '/dashboard');
  });
  cy.visit('/dashboard');
  cy.waitForNetworkIdle();
});
```

## Visual Testing

Use Cypress for visual regression testing:

```javascript
// Install cypress-image-snapshot
// npm install --save-dev cypress-image-snapshot

// cypress/plugins/index.js
const { addMatchImageSnapshotPlugin } = require('cypress-image-snapshot/plugin');

module.exports = (on, config) => {
  addMatchImageSnapshotPlugin(on, config);
};

// cypress/support/commands.js
import { addMatchImageSnapshotCommand } from 'cypress-image-snapshot/command';
addMatchImageSnapshotCommand();

// In tests
it('should match visual snapshot', () => {
  cy.visit('/');
  cy.matchImageSnapshot('home-page');
});
```

## Continuous Integration

Configure Cypress for CI:

```yaml
# .github/workflows/cypress.yml
name: Cypress Tests
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
jobs:
  cypress:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Cypress run
        uses: cypress-io/github-action@v5
        with:
          build: npm run build
          start: npm start
          wait-on: 'http://localhost:5173'
          record: true
        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
```

---

Last Updated: 2025-05-12
