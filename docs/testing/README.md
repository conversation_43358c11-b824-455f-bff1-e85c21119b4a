# RentUp Testing Documentation

This directory contains documentation related to testing the RentUp platform.

## Table of Contents

- [Test Methodology](test_methodology.md) - Comprehensive testing approach for the RentUp platform
- [Test Organization](#test-organization) - How tests are organized in the project
- [Running Tests](#running-tests) - How to run tests for the RentUp platform
- [Test Results](#test-results) - Where to find test results

## Test Organization

Tests are organized by phase and component:

```
tests/
├── phase0/                  # Phase 0 (Project Setup) tests
├── phase1/                  # Phase 1 (Frontend Scaffolding) tests
│   └── cypress/             # Cypress tests for Phase 1
│       └── e2e/             # End-to-end tests
│           └── phase1/      # Phase 1 specific test files
├── phase2/                  # Phase 2 (Backend Integration) tests
│   └── api-tests/           # API tests
├── phase3/                  # Phase 3 (Advanced Features) tests
│   └── implementation/      # Implementation tests
├── frontend/                # Frontend-specific tests
│   └── tests/               # Frontend tests
│       └── screenshots/     # Screenshots from frontend tests
└── backend/                 # Backend-specific tests
    └── tests/               # Backend tests
```

## Running Tests

### Running All Tests

```bash
./run_tests.py
```

### Running Phase-Specific Tests

```bash
./run_tests.py --phase 3
```

### Running Component-Specific Tests

```bash
./run_tests.py --component recommendations
```

### Running Frontend-Only Tests

```bash
./run_tests.py --frontend
```

### Running Backend-Only Tests

```bash
./run_tests.py --backend
```

### Running Tests with Verbose Output

```bash
./run_tests.py --verbose
```

## Test Results

Test results are stored in the `testResults` directory, organized by phase and component:

```
testResults/
├── phase0/                  # Phase 0 (Project Setup) test results
├── phase1/                  # Phase 1 (Frontend Scaffolding) test results
│   └── cypress/             # Cypress test results for Phase 1
│       └── e2e/             # End-to-end test results
│           └── phase1/      # Phase 1 specific test files
├── phase2/                  # Phase 2 (Backend Integration) test results
│   └── api-tests/           # API test results
├── phase3/                  # Phase 3 (Advanced Features) test results
│   └── implementation/      # Implementation reports
├── phase4/                  # Phase 4 (Optimization & Deployment) test results
├── frontend/                # Frontend-specific test results
│   └── tests/               # Frontend test results
│       └── screenshots/     # Screenshots from frontend tests
└── backend/                 # Backend-specific test results
    └── tests/               # Backend test results
```

---

Last Updated: 2025-05-12
