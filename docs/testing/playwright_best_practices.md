# Playwright Testing Best Practices

This document outlines best practices for writing Playwright tests for the RentUp platform.

## Table of Contents

- [Project Structure](#project-structure)
- [Selectors](#selectors)
- [Page Objects](#page-objects)
- [Auto-Waiting](#auto-waiting)
- [Assertions](#assertions)
- [Network Requests](#network-requests)
- [Fixtures](#fixtures)
- [Performance](#performance)
- [Visual Testing](#visual-testing)
- [Continuous Integration](#continuous-integration)

## Project Structure

Organize your Playwright tests in a structured way:

```
tests/
├── e2e/                     # End-to-end tests
│   ├── phase1/              # Phase 1 tests
│   ├── phase2/              # Phase 2 tests
│   └── phase3/              # Phase 3 tests
├── fixtures/                # Test data
│   ├── users.json           # User data
│   └── items.json           # Item data
├── page-objects/            # Page objects
│   ├── HomePage.ts          # Home page object
│   └── ItemPage.ts          # Item page object
└── playwright.config.ts     # Playwright configuration
```

## Selectors

Use selectors that are resilient to changes in the implementation:

```typescript
// Bad - brittle selectors
await page.click('.btn-primary');
await page.locator('div > p > span').textContent();

// Good - resilient selectors
await page.getByTestId('submit-button').click();
await page.getByRole('button', { name: 'Submit' }).click();
await page.getByText('Hello').isVisible();
```

### Selector Priority

1. **User-facing attributes**: Text, role, label, placeholder
2. **Data attributes**: `data-testid`, `data-test`, etc.
3. **CSS selectors**: Only if they are stable and meaningful

### Built-in Locators

Playwright provides built-in locators that are more resilient than CSS selectors:

```typescript
// Role-based locators
const button = page.getByRole('button', { name: 'Submit' });
const heading = page.getByRole('heading', { name: 'Welcome' });
const link = page.getByRole('link', { name: 'Learn more' });

// Text-based locators
const element = page.getByText('Hello, world!');
const element = page.getByText(/Hello/);

// Label-based locators
const input = page.getByLabel('Email');

// Placeholder-based locators
const searchInput = page.getByPlaceholder('Search...');

// TestID-based locators
const container = page.getByTestId('search-results');
```

## Page Objects

Use the Page Object Model (POM) pattern to organize your tests:

```typescript
// page-objects/HomePage.ts
import { Page, Locator } from '@playwright/test';

export class HomePage {
  readonly page: Page;

  // Define selectors for elements on the home page
  readonly selectors = {
    heroTitle: [
      'h1:has-text("Community Marketplace")',
      'h1:has-text("Sharing Possibilities")',
      'h1',
      '[class*="hero"] h1'
    ],
    searchInput: [
      'input[placeholder*="rent" i]',
      'input[type="search"]',
      'input[type="text"]'
    ],
    searchButton: [
      'button:has-text("Search")',
      'button[type="submit"]'
    ]
  };

  constructor(page: Page) {
    this.page = page;
  }

  /**
   * Navigate to the home page
   */
  async goto() {
    await this.page.goto('/');
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Find an element using multiple selector strategies
   */
  async findElement(selectors: string[]): Promise<Locator | null> {
    for (const selector of selectors) {
      const element = this.page.locator(selector);
      const count = await element.count();

      if (count > 0) {
        return element;
      }
    }

    return null;
  }

  /**
   * Perform a search
   */
  async search(searchTerm: string): Promise<boolean> {
    const searchInput = await this.findElement(this.selectors.searchInput);
    if (!searchInput) return false;

    await searchInput.fill(searchTerm);

    const searchButton = await this.findElement(this.selectors.searchButton);
    if (!searchButton) return false;

    await searchButton.click();

    return true;
  }
}
```

Usage in tests:

```typescript
// tests/e2e/phase1/home.spec.ts
import { test, expect } from '@playwright/test';
import { HomePage } from '../../page-objects/HomePage';

test.describe('Home Page', () => {
  let homePage;

  test.beforeEach(async ({ page }) => {
    homePage = new HomePage(page);
    await homePage.goto();
  });

  test('should allow searching for items', async () => {
    await homePage.search('bike');
    await expect(page).toHaveURL(/search\?q=bike/);
    await expect(page.getByTestId('search-results')).toBeVisible();
  });
});
```

## Auto-Waiting

Playwright has built-in auto-waiting for most operations:

```typescript
// No need for explicit waits in most cases
await page.getByRole('button', { name: 'Submit' }).click();

// For specific conditions, use explicit waits
await page.waitForURL('/dashboard');
await page.waitForSelector('.loaded-content');
await expect(page.getByText('Success')).toBeVisible({ timeout: 10000 });

// Wait for network activity to complete
await page.waitForLoadState('networkidle');

// Wait for a specific response
const responsePromise = page.waitForResponse(response =>
  response.url().includes('/api/data') && response.status() === 200
);
await page.getByRole('button', { name: 'Load Data' }).click();
await responsePromise;
```

## Assertions

Use explicit assertions with clear error messages:

```typescript
// Bad - no assertion
await page.getByTestId('item-price');

// Good - explicit assertion
await expect(page.getByTestId('item-price')).toBeVisible();
await expect(page.getByTestId('item-price')).toContainText('$');

// Better - assertions with clear error messages
await expect(page.getByTestId('item-price')).toBeVisible(
  { timeout: 10000 },
  'Price should be visible'
);
await expect(page.getByTestId('item-price')).toContainText(
  '$',
  'Price should contain $ symbol'
);
```

## Network Requests

Use `page.route()` to mock or spy on network requests:

```typescript
// Mock API response
await page.route('/api/items', async (route) => {
  await route.fulfill({
    status: 200,
    contentType: 'application/json',
    body: JSON.stringify({ items: [] })
  });
});

// Spy on API request
await page.route('/api/login', async (route) => {
  const request = route.request();
  console.log('Request body:', await request.postData());
  await route.continue();
});

// Wait for a specific response
const responsePromise = page.waitForResponse(response =>
  response.url().includes('/api/data') && response.status() === 200
);
await page.getByRole('button', { name: 'Load Data' }).click();
const response = await responsePromise;
const data = await response.json();
```

## Fixtures

Use fixtures for test data:

```typescript
// fixtures/users.json
{
  "users": [
    {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "password": "password123"
    }
  ]
}
```

Usage in tests:

```typescript
// Load fixture in test
import { test, expect } from '@playwright/test';
import fs from 'fs';
import path from 'path';

test('should display user information', async ({ page }) => {
  const users = JSON.parse(fs.readFileSync(
    path.join(__dirname, '../../fixtures/users.json'),
    'utf8'
  ));
  const user = users.users[0];
  
  // Use fixture data
  await page.goto('/login');
  await page.getByLabel('Email').fill(user.email);
  await page.getByLabel('Password').fill(user.password);
  await page.getByRole('button', { name: 'Login' }).click();
  await expect(page.getByTestId('user-name')).toContainText(user.name);
});
```

## Performance

Optimize test performance:

1. **Use authentication storage**: Save and reuse authentication state
2. **Reduce navigation**: Minimize page loads
3. **Parallelize tests**: Run tests in parallel
4. **Use request context**: Make API calls directly when possible

```typescript
// Save authentication state
test.describe('Authenticated tests', () => {
  test.beforeAll(async ({ browser }) => {
    const context = await browser.newContext();
    const page = await context.newPage();
    
    await page.goto('/login');
    await page.getByLabel('Email').fill('<EMAIL>');
    await page.getByLabel('Password').fill('password123');
    await page.getByRole('button', { name: 'Login' }).click();
    await page.waitForURL('/dashboard');
    
    // Save storage state
    await context.storageState({ path: 'auth.json' });
    await context.close();
  });
  
  test.use({ storageState: 'auth.json' });
  
  test('should access protected page', async ({ page }) => {
    await page.goto('/dashboard');
    await expect(page.getByText('Welcome')).toBeVisible();
  });
});
```

## Visual Testing

Use Playwright for visual regression testing:

```typescript
// playwright.config.ts
import { defineConfig } from '@playwright/test';

export default defineConfig({
  // ... other config
  expect: {
    toHaveScreenshot: {
      maxDiffPixels: 100,
    }
  }
});

// In tests
test('should match visual snapshot', async ({ page }) => {
  await page.goto('/');
  await expect(page).toHaveScreenshot('home-page.png');
  
  // Component-level screenshot
  const card = page.getByTestId('product-card');
  await expect(card).toHaveScreenshot('product-card.png');
});
```

## Continuous Integration

Configure Playwright for CI:

```yaml
# .github/workflows/playwright.yml
name: Playwright Tests
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - name: Install dependencies
        run: npm ci
      - name: Install Playwright browsers
        run: npx playwright install --with-deps
      - name: Run Playwright tests
        run: npx playwright test
      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30
```

---

Last Updated: 2025-05-12
