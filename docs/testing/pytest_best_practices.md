# pytest Best Practices

This document outlines best practices for writing pytest tests for the RentUp platform.

## Table of Contents

- [Project Structure](#project-structure)
- [Fixtures](#fixtures)
- [Markers](#markers)
- [Assertions](#assertions)
- [Mocking](#mocking)
- [Parametrization](#parametrization)
- [Configuration](#configuration)
- [Continuous Integration](#continuous-integration)

## Project Structure

Organize your pytest tests in a structured way:

```
backend/
├── app/
│   └── tests/
│       ├── conftest.py          # Shared fixtures
│       ├── test_auth.py         # Authentication tests
│       ├── test_items.py        # Item tests
│       ├── test_users.py        # User tests
│       ├── test_bookings.py     # Booking tests
│       ├── test_search.py       # Search tests
│       ├── test_uploads.py      # Upload tests
│       ├── test_recommendations.py  # Recommendation tests
│       ├── test_auctions.py     # Auction tests
│       ├── test_agreements.py   # Agreement tests
│       └── test_fraud_detection.py  # Fraud detection tests
└── pytest.ini                   # pytest configuration
```

## Fixtures

Use fixtures for test setup and teardown:

```python
# conftest.py
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.database import Base
from app.main import app
from app.dependencies import get_db

# Create test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

@pytest.fixture(scope="session")
def db_engine():
    Base.metadata.create_all(bind=engine)
    yield engine
    Base.metadata.drop_all(bind=engine)

@pytest.fixture(scope="function")
def db_session(db_engine):
    connection = db_engine.connect()
    transaction = connection.begin()
    session = TestingSessionLocal(bind=connection)
    
    yield session
    
    session.close()
    transaction.rollback()
    connection.close()

@pytest.fixture(scope="function")
def client(db_session):
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as client:
        yield client
    app.dependency_overrides.clear()

@pytest.fixture(scope="function")
def test_user(client, db_session):
    user_data = {
        "email": "<EMAIL>",
        "password": "password123",
        "name": "Test User"
    }
    response = client.post("/api/users/", json=user_data)
    assert response.status_code == 201
    return response.json()

@pytest.fixture(scope="function")
def authenticated_client(client, test_user):
    response = client.post("/api/auth/login", data={
        "username": test_user["email"],
        "password": "password123"
    })
    assert response.status_code == 200
    token = response.json()["access_token"]
    client.headers = {
        "Authorization": f"Bearer {token}"
    }
    return client
```

## Markers

Use markers to categorize tests:

```python
# pytest.ini
[pytest]
markers =
    unit: Unit tests
    integration: Integration tests
    api: API tests
    slow: Slow running tests
    phase0: Phase 0 tests
    phase1: Phase 1 tests
    phase2: Phase 2 tests
    phase3: Phase 3 tests
```

Usage in tests:

```python
# test_auth.py
import pytest
from fastapi.testclient import TestClient

@pytest.mark.api
@pytest.mark.phase2
def test_login_success(client):
    """Test successful login."""
    response = client.post(
        "/api/auth/login",
        data={"username": "<EMAIL>", "password": "password123"}
    )
    assert response.status_code == 200
    assert "access_token" in response.json()
    assert "token_type" in response.json()
    assert response.json()["token_type"] == "bearer"

@pytest.mark.api
@pytest.mark.phase2
def test_login_invalid_credentials(client):
    """Test login with invalid credentials."""
    response = client.post(
        "/api/auth/login",
        data={"username": "<EMAIL>", "password": "wrongpassword"}
    )
    assert response.status_code == 401
    assert "detail" in response.json()
    assert response.json()["detail"] == "Invalid credentials"
```

## Assertions

Use pytest's built-in assertions for clear error messages:

```python
# Basic assertions
assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"
assert "access_token" in response.json(), "Response should contain access_token"

# Collection assertions
assert len(response.json()["items"]) == 5, "Expected 5 items in response"
assert all(item["price"] > 0 for item in response.json()["items"]), "All items should have positive price"

# Exception assertions
with pytest.raises(ValueError, match="Invalid input"):
    function_that_raises()
```

## Mocking

Use pytest-mock for mocking:

```python
# Install pytest-mock
# pip install pytest-mock

# In tests
def test_recommendation_service(mocker):
    # Mock the embedding service
    mock_embedding = mocker.patch("app.services.item_embedding_service.get_embedding")
    mock_embedding.return_value = [0.1, 0.2, 0.3]
    
    # Mock the vector search
    mock_search = mocker.patch("app.services.vector_search.search")
    mock_search.return_value = [
        {"id": 1, "score": 0.9},
        {"id": 2, "score": 0.8}
    ]
    
    # Test the recommendation service
    from app.services.recommendation_service import get_recommendations
    recommendations = get_recommendations(user_id=1, limit=2)
    
    # Assertions
    assert len(recommendations) == 2
    assert recommendations[0]["id"] == 1
    assert recommendations[1]["id"] == 2
    
    # Verify mocks were called correctly
    mock_embedding.assert_called_once()
    mock_search.assert_called_once()
```

## Parametrization

Use parametrization to test multiple scenarios:

```python
@pytest.mark.parametrize("email,password,expected_status", [
    ("<EMAIL>", "password123", 200),
    ("<EMAIL>", "wrongpassword", 401),
    ("<EMAIL>", "password123", 401),
    ("invalid-email", "password123", 422),
])
def test_login_scenarios(client, email, password, expected_status):
    """Test various login scenarios."""
    response = client.post(
        "/api/auth/login",
        data={"username": email, "password": password}
    )
    assert response.status_code == expected_status
```

## Configuration

Configure pytest for your project:

```python
# pytest.ini
[pytest]
testpaths = backend/app/tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    unit: Unit tests
    integration: Integration tests
    api: API tests
    slow: Slow running tests
    phase0: Phase 0 tests
    phase1: Phase 1 tests
    phase2: Phase 2 tests
    phase3: Phase 3 tests
```

## Continuous Integration

Configure pytest for CI:

```yaml
# .github/workflows/pytest.yml
name: pytest Tests
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: '3.10'
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r backend/requirements.txt
          pip install pytest pytest-cov
      - name: Run pytest
        run: |
          cd backend
          pytest --cov=app app/tests/
      - name: Upload coverage report
        uses: codecov/codecov-action@v3
```

## Best Practices

1. **Test Isolation**: Each test should be independent of others
2. **Descriptive Names**: Use descriptive test names that explain what is being tested
3. **One Assertion Per Test**: Focus each test on a single behavior
4. **Arrange-Act-Assert**: Structure tests with clear setup, action, and verification
5. **Clean Fixtures**: Keep fixtures focused and reusable
6. **Fast Tests**: Optimize tests to run quickly
7. **Coverage**: Aim for high test coverage, especially for critical paths

Example of a well-structured test:

```python
def test_create_item_with_valid_data(authenticated_client):
    """Test creating an item with valid data."""
    # Arrange
    item_data = {
        "name": "Test Item",
        "description": "A test item",
        "price": 100.0,
        "category_id": 1
    }
    
    # Act
    response = authenticated_client.post("/api/items/", json=item_data)
    
    # Assert
    assert response.status_code == 201, f"Expected status code 201, got {response.status_code}"
    created_item = response.json()
    assert created_item["name"] == item_data["name"]
    assert created_item["description"] == item_data["description"]
    assert created_item["price"] == item_data["price"]
    assert created_item["category_id"] == item_data["category_id"]
    assert "id" in created_item
    assert "created_at" in created_item
```

---

Last Updated: 2025-05-12
