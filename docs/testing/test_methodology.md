# RentUp Testing Methodology (Updated May 2025)

This document outlines the comprehensive testing approach for the RentUp platform, covering both frontend and backend testing. It provides guidelines for using <PERSON><PERSON> and <PERSON>press for frontend testing, as well as pytest for backend testing, incorporating the latest best practices as of May 2025.

## Table of Contents

1. [Key Testing Principles](#key-testing-principles)
2. [Testing Loop Overview](#testing-loop-overview)
3. [Frontend Testing with Playwright](#frontend-testing-with-playwright)
4. [Frontend Testing with Cypress](#frontend-testing-with-cypress)
5. [Backend Testing with pytest](#backend-testing-with-pytest)
6. [Test Organization](#test-organization)
7. [Reporting](#reporting)
8. [Continuous Integration](#continuous-integration)
9. [Accessibility Testing](#accessibility-testing)
10. [Performance Testing](#performance-testing)
11. [Responsive Design Testing](#responsive-design-testing)

## Key Testing Principles

1. **Page Object Model (POM)**: We use the Page Object Model pattern to organize our tests, which improves maintainability, reusability, and readability.

2. **Resilient Selectors**: We use multiple selector strategies to make our tests resilient to changes in the implementation, with a preference for user-facing selectors (role, text, label) over implementation details.

3. **User-Centric Testing**: We focus on testing from the user's perspective, simulating real user interactions and workflows.

4. **Comprehensive Reporting**: We generate detailed reports that help identify issues and track progress, with visual evidence for failures.

5. **Test Independence**: Each test should be independent of others and should not rely on the state created by previous tests.

6. **Accessibility-First Testing**: We integrate accessibility testing into our regular testing workflow to ensure WCAG 2.1 AA compliance.

7. **Responsive Design Validation**: We test across all defined breakpoints (xs:375px, sm:640px, md:768px, lg:1024px, xl:1280px, 2xl:1536px) to ensure proper responsive behavior.

## Testing Loop Overview

```
┌─────────────────┐
│                 │
│  1. Analysis    │◄───────────────────┐
│                 │                    │
└────────┬────────┘                    │
         │                             │
         ▼                             │
┌─────────────────┐                    │
│                 │                    │
│  2. Test Script │                    │
│     Enhancement │                    │
│                 │                    │
└────────┬────────┘                    │
         │                             │
         ▼                             │
┌─────────────────┐                    │
│                 │                    │
│  3. Test        │                    │
│     Execution   │                    │
│                 │                    │
└────────┬────────┘                    │
         │                             │
         ▼                             │
┌─────────────────┐                    │
│                 │                    │
│  4. Issue       │                    │
│  Identification │                    │
│                 │                    │
└────────┬────────┘                    │
         │                             │
         ▼                             │
┌─────────────────┐                    │
│                 │                    │
│  5. Verification│────────────────────┘
│     & Iteration │
│                 │
└─────────────────┘
```

## Frontend Testing with Playwright

### Setup and Configuration

```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests',
  timeout: 30000,
  expect: {
    timeout: 5000
  },
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html', { outputFolder: 'testResults/frontend/playwright-report' }],
    ['json', { outputFile: 'testResults/frontend/playwright-results.json' }]
  ],
  use: {
    baseURL: 'http://localhost:5173',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
  ],
});
```

### Page Object Model

```typescript
// page-objects/HomePage.ts
import { Page, Locator } from '@playwright/test';

export class HomePage {
  readonly page: Page;

  // Define selectors for elements on the home page
  readonly selectors = {
    heroTitle: [
      'h1:has-text("Community Marketplace")',
      'h1:has-text("Sharing Possibilities")',
      'h1',
      '[class*="hero"] h1'
    ],
    searchInput: [
      'input[placeholder*="rent" i]',
      'input[type="search"]',
      'input[type="text"]'
    ],
    searchButton: [
      'button:has-text("Search")',
      'button[type="submit"]'
    ]
  };

  constructor(page: Page) {
    this.page = page;
  }

  /**
   * Navigate to the home page
   */
  async goto() {
    await this.page.goto('/');
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Find an element using multiple selector strategies
   */
  async findElement(selectors: string[]): Promise<Locator | null> {
    for (const selector of selectors) {
      const element = this.page.locator(selector);
      const count = await element.count();

      if (count > 0) {
        return element;
      }
    }

    return null;
  }

  /**
   * Perform a search
   */
  async search(searchTerm: string): Promise<boolean> {
    const searchInput = await this.findElement(this.selectors.searchInput);
    if (!searchInput) return false;

    await searchInput.fill(searchTerm);

    const searchButton = await this.findElement(this.selectors.searchButton);
    if (!searchButton) return false;

    await searchButton.click();

    return true;
  }
}
```

### Best Practices for Playwright

1. **Element-Specific Waits**: Use element-specific waits instead of fixed timeouts.

```typescript
// Bad - fixed timeout
await page.waitForTimeout(2000);

// Good - wait for specific element
await expect(page.locator('h1')).toBeVisible();
```

2. **Resilient Selectors**: Use multiple selector strategies to make tests resilient to changes.

```typescript
// Preferred user-facing locators
const button = page.getByRole('button', { name: 'Submit' });
const heading = page.getByRole('heading', { name: 'Welcome' });
const link = page.getByRole('link', { name: 'Learn more' });
const input = page.getByLabel('Email');
const searchInput = page.getByPlaceholder('Search...');

// Data-testid for elements without clear user-facing attributes
const container = page.getByTestId('search-results');
```

3. **Auto-Waiting**: Rely on Playwright's built-in auto-waiting for most operations.

```typescript
// Auto-waiting (preferred)
await page.getByRole('button').click(); // Waits for button to be actionable

// Wait for specific conditions when needed
await page.waitForURL('/dashboard');
await page.waitForSelector('.loaded-content');
await expect(page.getByText('Success')).toBeVisible({ timeout: 10000 });
```

## Frontend Testing with Cypress

### Setup and Configuration

```javascript
// cypress.config.js
const { defineConfig } = require('cypress');

module.exports = defineConfig({
  e2e: {
    baseUrl: 'http://localhost:5173',
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    viewportWidth: 1280,
    viewportHeight: 720,
    video: true,
    screenshotOnRunFailure: true,
    reporter: 'mochawesome',
    reporterOptions: {
      reportDir: 'testResults/frontend/cypress-report',
      overwrite: false,
      html: true,
      json: true
    }
  },
});
```

### Page Object Model

```javascript
// cypress/support/page-objects/HomePage.js
class HomePage {
  // Define selectors for elements on the home page
  selectors = {
    heroTitle: [
      'h1:contains("Community Marketplace")',
      'h1:contains("Sharing Possibilities")',
      'h1',
      '[class*="hero"] h1'
    ],
    searchInput: [
      'input[placeholder*="rent" i]',
      'input[type="search"]',
      'input[type="text"]'
    ],
    searchButton: [
      'button:contains("Search")',
      'button[type="submit"]'
    ]
  };

  /**
   * Navigate to the home page
   */
  visit() {
    cy.visit('/');
    cy.waitForNetworkIdle();
    return this;
  }

  /**
   * Find an element using multiple selector strategies
   */
  findElement(selectors) {
    for (const selector of selectors) {
      const element = cy.get(selector, { timeout: 10000, log: false });
      if (element) {
        return element;
      }
    }
    return null;
  }

  /**
   * Perform a search
   */
  search(searchTerm) {
    this.findElement(this.selectors.searchInput).type(searchTerm);
    this.findElement(this.selectors.searchButton).click();
    return this;
  }
}

export default new HomePage();
```

### Best Practices for Cypress

1. **Custom Commands**: Use custom commands for common operations.

```javascript
// cypress/support/commands.js
Cypress.Commands.add('waitForNetworkIdle', (timeout = 10000) => {
  cy.window().then(win => {
    return new Cypress.Promise((resolve) => {
      let requestCounter = 0;
      const onRequestStart = () => requestCounter++;
      const onRequestEnd = () => {
        requestCounter--;
        if (requestCounter === 0) {
          setTimeout(resolve, 500); // Wait a bit to ensure no new requests
        }
      };

      win.addEventListener('fetch', onRequestStart);
      win.addEventListener('fetchdone', onRequestEnd);

      setTimeout(resolve, timeout); // Fallback timeout
    });
  });
});
```

2. **Intercept Network Requests**: Use `cy.intercept()` to mock or spy on network requests.

```javascript
// Mock API response
cy.intercept('GET', '/api/items', { fixture: 'items.json' }).as('getItems');

// Spy on API request
cy.intercept('POST', '/api/login').as('loginRequest');
cy.get('button').contains('Login').click();
cy.wait('@loginRequest').its('response.statusCode').should('eq', 200);
```

## Backend Testing with pytest

### Setup and Configuration

```python
# pytest.ini
[pytest]
testpaths = backend/app/tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    unit: Unit tests
    integration: Integration tests
    api: API tests
    slow: Slow running tests
    phase0: Phase 0 tests
    phase1: Phase 1 tests
    phase2: Phase 2 tests
    phase3: Phase 3 tests
```

### Test Organization

```python
# backend/app/tests/test_auth.py
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

@pytest.mark.api
@pytest.mark.phase2
def test_login_success():
    """Test successful login."""
    response = client.post(
        "/api/auth/login",
        json={"email": "<EMAIL>", "password": "password123"}
    )
    assert response.status_code == 200
    assert "access_token" in response.json()
    assert "token_type" in response.json()
    assert response.json()["token_type"] == "bearer"

@pytest.mark.api
@pytest.mark.phase2
def test_login_invalid_credentials():
    """Test login with invalid credentials."""
    response = client.post(
        "/api/auth/login",
        json={"email": "<EMAIL>", "password": "wrongpassword"}
    )
    assert response.status_code == 401
    assert "detail" in response.json()
    assert response.json()["detail"] == "Invalid credentials"
```

## Test Organization

Tests are organized by phase and component:

```
tests/
├── phase0/                  # Phase 0 (Project Setup) tests
├── phase1/                  # Phase 1 (Frontend Scaffolding) tests
│   └── cypress/             # Cypress tests for Phase 1
│       └── e2e/             # End-to-end tests
│           └── phase1/      # Phase 1 specific test files
├── phase2/                  # Phase 2 (Backend Integration) tests
│   └── api-tests/           # API tests
├── phase3/                  # Phase 3 (Advanced Features) tests
│   └── implementation/      # Implementation tests
├── frontend/                # Frontend-specific tests
│   └── tests/               # Frontend tests
│       └── screenshots/     # Screenshots from frontend tests
└── backend/                 # Backend-specific tests
    └── tests/               # Backend tests
```

## Running Tests

### Running All Tests

```bash
./test_all_phases.py
```

### Running Phase-Specific Tests

```bash
./test_all_phases.py --phase 3
```

### Running Component-Specific Tests

```bash
./test_all_phases.py --component recommendations
```

## Continuous Integration

Tests are integrated into the CI/CD pipeline to ensure continuous quality:

```yaml
# .github/workflows/tests.yml
name: Tests
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 20
      - name: Install dependencies
        run: npm ci
      - name: Install Playwright browsers
        run: npx playwright install --with-deps
      - name: Run Playwright tests
        run: npx playwright test
      - name: Run accessibility tests
        run: npx playwright test --project=accessibility
      - name: Run responsive tests
        run: npx playwright test --project=responsive
      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: playwright-report
          path: testResults/frontend/playwright-report/
          retention-days: 30
```

## Accessibility Testing

Accessibility testing is a critical part of our testing strategy, ensuring our application is usable by everyone, including people with disabilities. According to 2025 research, implementing accessibility features can increase conversion rates by up to 20% and is essential for reaching the 15% of the global population with disabilities.

### Automated Accessibility Testing

We use automated tools to catch common accessibility issues:

```typescript
// Playwright accessibility test example
test('Home page should not have accessibility violations', async ({ page }) => {
  await page.goto('/');

  // Run axe accessibility tests
  const accessibilityScanResults = await page.evaluate(() => {
    return new Promise(resolve => {
      // @ts-ignore
      const axe = window.axe;
      axe.run((err, results) => {
        if (err) throw err;
        resolve(results);
      });
    });
  });

  // Assert no violations
  expect(accessibilityScanResults.violations).toEqual([]);
});
```

### Key Accessibility Checks

1. **Color Contrast**: Ensure sufficient contrast (4.5:1 for normal text, 3:1 for large text)
2. **Keyboard Navigation**: Verify all interactive elements are keyboard accessible
3. **Screen Reader Compatibility**: Test with screen readers like NVDA or VoiceOver
4. **Focus Management**: Ensure proper focus indicators and focus order
5. **ARIA Attributes**: Verify correct ARIA roles and attributes
6. **Alternative Text**: Check all images have appropriate alt text
7. **Form Accessibility**: Ensure forms have proper labels and error messages

### Accessibility Testing in CI

Accessibility tests are run as part of our CI pipeline to catch issues early.

## Performance Testing

Performance testing ensures our application meets speed and responsiveness requirements, which is critical as most users abandon sites that take more than 3 seconds to load.

### Core Web Vitals Testing

We test against Google's Core Web Vitals metrics:

1. **Largest Contentful Paint (LCP)**: Should be under 2.5 seconds
2. **First Input Delay (FID)**: Should be under 100 milliseconds
3. **Cumulative Layout Shift (CLS)**: Should be under 0.1

### Load Time Testing

```typescript
// Playwright performance test example
test('Home page should load within performance budget', async ({ page }) => {
  const startTime = Date.now();

  // Navigate to the page
  await page.goto('/');

  // Wait for the page to be fully loaded
  await page.waitForLoadState('networkidle');

  const loadTime = Date.now() - startTime;

  // Assert load time is within budget (3 seconds)
  expect(loadTime).toBeLessThan(3000);
});
```

## Responsive Design Testing

We test our application across all defined breakpoints to ensure proper responsive behavior:

```typescript
// Playwright responsive design test
const breakpoints = [
  { width: 375, height: 667, name: 'xs' },  // Mobile
  { width: 640, height: 960, name: 'sm' },  // Small tablet
  { width: 768, height: 1024, name: 'md' }, // Tablet
  { width: 1024, height: 768, name: 'lg' }, // Laptop
  { width: 1280, height: 800, name: 'xl' }, // Desktop
  { width: 1536, height: 960, name: '2xl' } // Large desktop
];

for (const bp of breakpoints) {
  test(`Home page should render correctly at ${bp.name} breakpoint`, async ({ page }) => {
    // Set viewport size
    await page.setViewportSize({ width: bp.width, height: bp.height });

    // Navigate to the page
    await page.goto('/');

    // Take a screenshot for visual comparison
    await page.screenshot({ path: `home-${bp.name}.png` });

    // Check specific elements for this breakpoint
    if (bp.width < 768) {
      // Mobile-specific checks
      await expect(page.locator('.mobile-menu-button')).toBeVisible();
      await expect(page.locator('.desktop-menu')).not.toBeVisible();
    } else {
      // Desktop-specific checks
      await expect(page.locator('.desktop-menu')).toBeVisible();
      await expect(page.locator('.mobile-menu-button')).not.toBeVisible();
    }
  });
}
```

---

Last Updated: 2025-05-20
