# rentUP Color System

## Primary Colors

### Blue (#3B82F6)
Usage:
- Headers and navigation
- Primary interactive elements
- Trust indicators
- Verification badges
- Security-related UI elements
- Login/Authentication areas
- Payment sections

Variants:
- Light: #60A5FA (hover states, backgrounds)
- Dark: #2563EB (active states, emphasis)

### Secondary Teal (#0D9488)
Usage:
- Community features
- Progress indicators
- Success states
- Verified user badges
- Environmental/sustainability features
- Secondary CTAs
- Map markers for available properties

Variants:
- Light: #14B8A6 (hover states)
- Dark: #0F766E (active states)

### Accent Orange (#F97316)
Usage:
- Primary CTAs ("Post Listing", "Apply Now")
- Important notifications (non-critical)
- Highlight new features
- Premium listings
- Special offers
- Time-sensitive content
- Rent-to-Buy badges and indicators

Variants:
- Light: #FB923C (hover states)
- Dark: #EA580C (active states)

## Feedback Colors

### Success Green (#10B981)
Usage:
- Successful transactions
- Positive status indicators
- Completion messages
- Verification success
- Payment confirmed
- Application accepted

### Warning Amber (#F59E0B)
Usage:
- Attention required
- Pending actions
- Moderate risk alerts
- Identity verification needed
- Incomplete profiles
- Payment pending

### Error Red (#EF4444)
Usage:
- Critical errors
- Security warnings
- Failed transactions
- Invalid input
- Suspicious activity alerts
- Account security issues

## Neutral Colors

### White (#FFFFFF)
Usage:
- Primary background
- Content areas
- Cards and containers
- Clean spaces requiring focus

### Light Gray (#F3F4F6)
Usage:
- Secondary backgrounds
- Disabled states
- Subtle separators
- Form backgrounds
- Inactive tabs

### Medium Gray (#9CA3AF)
Usage:
- Disabled text
- Secondary text
- Placeholder text
- Inactive icons
- Subtle borders

### Dark Gray (#4B5563)
Usage:
- Body text
- Secondary headings
- Labels
- Input text

### Near Black (#1F2937)
Usage:
- Primary headings
- Important text
- High-emphasis content
- Footer text

## Reliability Score Colors

### Excellent (#10B981)
Usage:
- Reliability scores 90-100
- Items with exceptional history and maintenance records
- Highly trusted items

### Good (#3B82F6)
Usage:
- Reliability scores 70-89
- Items with good history and regular maintenance
- Trusted items

### Average (#F59E0B)
Usage:
- Reliability scores 50-69
- Items with moderate history or some maintenance gaps
- Moderately trusted items

### Poor (#EF4444)
Usage:
- Reliability scores below 50
- Items with limited history or maintenance concerns
- Items requiring additional verification

## Implementation Guidelines

1. Accessibility
- Maintain minimum contrast ratio of 4.5:1 for normal text
- Use 7:1 contrast ratio for small text
- Test all color combinations with color blindness simulators
- Ensure color is not the only means of conveying information

2. Trust & Security
- Use consistent color patterns for security-related elements
- Reserve warning colors exclusively for alerts
- Implement color gradients for trust levels
- Use reliability score colors consistently across the platform

3. Interactive States
- Hover: Lighten primary colors by 10%
- Active: Darken primary colors by 10%
- Focus: Add blue ring with 2px offset
- Loading: Use subtle animation with primary colors

4. Dark Mode Considerations
- Invert contrast ratios
- Adjust saturation for better readability
- Maintain brand recognition
- Ensure reliability score colors remain distinguishable

## Technical Implementation

Colors are implemented in two ways:

1. **CSS Variables**: Defined in `index.css` for use throughout the application
2. **Tailwind Configuration**: Mapped in `tailwind.config.ts` for use with Tailwind classes

Always use the color variables or Tailwind classes rather than hardcoding hex values to ensure consistency and make future updates easier.