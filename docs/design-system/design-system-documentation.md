# RentUp Design System Documentation

This document provides a comprehensive overview of the RentUp design system, including typography, colors, spacing, components, and more.

## Table of Contents

1. [Introduction](#introduction)
2. [Typography](#typography)
3. [Colors](#colors)
4. [Spacing](#spacing)
5. [Components](#components)
6. [Animations](#animations)
7. [Accessibility](#accessibility)
8. [Usage Guidelines](#usage-guidelines)

## Introduction

The RentUp design system is a comprehensive set of design guidelines, components, and patterns that ensure consistency and quality across the RentUp platform. It serves as a single source of truth for designers and developers, enabling faster development and a cohesive user experience.

## Typography

### Font Families

- **Sans-serif (Primary)**: Inter - Used for body text, UI elements, and general content
- **Display (Secondary)**: Montserrat - Used for headings, titles, and emphasis
- **Monospace**: SF Mono, Ubuntu Mono, etc. - Used for code snippets and technical content

### Font Sizes

| Name | Size (rem) | Size (px) | Usage |
|------|------------|-----------|-------|
| xs   | 0.75rem    | 12px      | Small labels, footnotes |
| sm   | 0.875rem   | 14px      | Secondary text, captions |
| base | 1rem       | 16px      | Body text, default size |
| lg   | 1.125rem   | 18px      | Large body text |
| xl   | 1.25rem    | 20px      | Subheadings |
| 2xl  | 1.5rem     | 24px      | Small headings (h6, h5) |
| 3xl  | 1.875rem   | 30px      | Medium headings (h4) |
| 4xl  | 2.25rem    | 36px      | Large headings (h3) |
| 5xl  | 3rem       | 48px      | Extra large headings (h2) |
| 6xl  | 3.75rem    | 60px      | Display headings (h1) |
| 7xl  | 4.5rem     | 72px      | Hero headings |
| 8xl  | 6rem       | 96px      | Massive display text |

### Font Weights

- **Light**: 300 - Used for large display text
- **Regular**: 400 - Default body text
- **Medium**: 500 - Emphasis, subheadings
- **Semibold**: 600 - Headings, buttons
- **Bold**: 700 - Strong emphasis, primary headings

### Line Heights

- **None**: 1 - Used for headings where tight spacing is needed
- **Tight**: 1.2 - Headings and short text blocks
- **Snug**: 1.375 - Subheadings
- **Normal**: 1.5 - Body text (default)
- **Relaxed**: 1.625 - Long-form content
- **Loose**: 2 - Widely spaced content for readability

## Colors

### Primary Colors (Blue)

Primary colors are used for primary actions, links, and key UI elements.

| Name | Hex Code | Usage |
|------|----------|-------|
| primary-50 | #eff6ff | Very light backgrounds, hover states |
| primary-100 | #dbeafe | Light backgrounds, hover states |
| primary-200 | #bfdbfe | Light backgrounds, borders |
| primary-300 | #93c5fd | Borders, icons |
| primary-400 | #60a5fa | Icons, secondary buttons |
| primary-500 | #3b82f6 | Secondary actions, highlights |
| primary-600 | #2563eb | Primary actions, buttons, links |
| primary-700 | #1d4ed8 | Hover states for primary actions |
| primary-800 | #1e40af | Active states, focus states |
| primary-900 | #1e3a8a | Very dark accents |
| primary-950 | #172554 | Extremely dark accents |

### Secondary Colors (Teal)

Secondary colors are used for secondary actions, success states, and complementary UI elements.

| Name | Hex Code | Usage |
|------|----------|-------|
| secondary-50 | #f0fdfa | Very light backgrounds, hover states |
| secondary-100 | #ccfbf1 | Light backgrounds, hover states |
| secondary-200 | #99f6e4 | Light backgrounds, borders |
| secondary-300 | #5eead4 | Borders, icons |
| secondary-400 | #2dd4bf | Icons, secondary buttons |
| secondary-500 | #14b8a6 | Secondary actions, highlights |
| secondary-600 | #0d9488 | Primary actions, buttons |
| secondary-700 | #0f766e | Hover states for primary actions |
| secondary-800 | #115e59 | Active states, focus states |
| secondary-900 | #134e4a | Very dark accents |
| secondary-950 | #042f2e | Extremely dark accents |

### Accent Colors (Amber)

Accent colors are used for highlighting important information, warnings, and attention-grabbing elements.

| Name | Hex Code | Usage |
|------|----------|-------|
| accent-50 | #fffbeb | Very light backgrounds, hover states |
| accent-100 | #fef3c7 | Light backgrounds, hover states |
| accent-200 | #fde68a | Light backgrounds, borders |
| accent-300 | #fcd34d | Borders, icons |
| accent-400 | #fbbf24 | Icons, highlights |
| accent-500 | #f59e0b | Accents, highlights |
| accent-600 | #d97706 | Primary accent actions |
| accent-700 | #b45309 | Hover states for accent actions |
| accent-800 | #92400e | Active states, focus states |
| accent-900 | #78350f | Very dark accents |
| accent-950 | #451a03 | Extremely dark accents |

### Semantic Colors

| Type | Usage |
|------|-------|
| Success | Used for positive feedback, successful actions, and confirmations |
| Warning | Used for warnings, cautionary messages, and potential issues |
| Error | Used for errors, destructive actions, and critical issues |
| Info | Used for informational messages and neutral notifications |

### Neutral Colors

| Name | Hex Code | Usage |
|------|----------|-------|
| white | #ffffff | Backgrounds, text on dark backgrounds |
| black | #000000 | Text on light backgrounds, icons |
| gray-50 to gray-950 | Various | Backgrounds, borders, text, etc. |

## Spacing

The spacing system is based on a 4px (0.25rem) unit, providing consistent spacing throughout the interface.

| Name | Size (rem) | Size (px) | Usage |
|------|------------|-----------|-------|
| px | 1px | 1px | Hairline borders |
| 0 | 0 | 0 | No spacing |
| 0.5 | 0.125rem | 2px | Tiny spacing |
| 1 | 0.25rem | 4px | Very small spacing |
| 2 | 0.5rem | 8px | Small spacing |
| 3 | 0.75rem | 12px | Small-medium spacing |
| 4 | 1rem | 16px | Medium spacing (default) |
| 5 | 1.25rem | 20px | Medium-large spacing |
| 6 | 1.5rem | 24px | Large spacing |
| 8 | 2rem | 32px | Very large spacing |
| 10 | 2.5rem | 40px | Extra large spacing |
| 12 | 3rem | 48px | Huge spacing |
| 16 | 4rem | 64px | Very huge spacing |
| 20 | 5rem | 80px | Extreme spacing |
| 24 | 6rem | 96px | Very extreme spacing |

## Components

The RentUp design system includes a comprehensive set of UI components that can be combined to create consistent and accessible interfaces.

### Core Components

- **Button**: Primary interaction element with multiple variants (primary, secondary, tertiary, etc.)
- **Input**: Text input field with validation states
- **Select**: Dropdown selection component
- **Checkbox**: Binary selection component
- **Radio**: Single selection from multiple options
- **Card**: Container for related content
- **Badge**: Small status indicator
- **Alert**: Feedback message with different severity levels
- **Modal**: Overlay dialog for focused interactions
- **Tooltip**: Contextual information on hover
- **Tabs**: Content organization with tabbed navigation
- **Pagination**: Navigation for multi-page content
- **Breadcrumb**: Hierarchical navigation indicator
- **Avatar**: User or entity representation
- **Progress**: Visual indicator of progress or loading

### Component Variants

Most components support multiple variants:

- **Size**: sm, md, lg
- **Variant**: default, primary, secondary, etc.
- **State**: default, hover, active, disabled, loading, error, etc.

## Animations

The animation system provides consistent motion patterns across the interface.

### Duration

- **Fastest**: 50ms - Micro-interactions
- **Faster**: 100ms - Quick transitions
- **Fast**: 150ms - Standard transitions
- **Normal**: 200ms - Default transitions
- **Slow**: 300ms - Emphasized transitions
- **Slower**: 400ms - Major transitions
- **Slowest**: 500ms - Dramatic transitions

### Timing Functions

- **Linear**: Constant speed
- **Ease-in**: Slow start, fast end
- **Ease-out**: Fast start, slow end
- **Ease-in-out**: Slow start, fast middle, slow end
- **Bounce**: Slight overshoot and bounce back
- **Elastic**: Springy motion

### Animation Patterns

- **Fade**: Opacity transitions
- **Slide**: Position transitions
- **Scale**: Size transitions
- **Spin**: Rotation transitions
- **Pulse**: Attention-grabbing pulsing effect
- **Bounce**: Playful bouncing effect

## Accessibility

The design system is built with accessibility in mind, following WCAG 2.1 AA guidelines.

### Color Contrast

All color combinations meet the minimum contrast ratio requirements:
- 4.5:1 for normal text
- 3:1 for large text and UI components

### Keyboard Navigation

All interactive elements are keyboard accessible with visible focus states.

### Screen Reader Support

Proper semantic HTML and ARIA attributes are used to ensure screen reader compatibility.

### Reduced Motion

Alternative animations are provided for users with motion sensitivity.

## Usage Guidelines

### Component Selection

Choose the appropriate component for each use case based on:
- User needs and expectations
- Information hierarchy
- Interaction requirements
- Space constraints

### Composition Patterns

Combine components to create consistent and predictable interfaces:
- Forms
- Lists
- Cards
- Dialogs
- Navigation
- Data tables
- Dashboards

### Responsive Design

Use the responsive utilities to create interfaces that work well on all screen sizes:
- Fluid typography
- Responsive spacing
- Flexible layouts
- Appropriate component sizes

### Performance Considerations

Optimize for performance by:
- Using appropriate image formats and sizes
- Minimizing layout shifts
- Implementing proper loading states
- Avoiding unnecessary animations
