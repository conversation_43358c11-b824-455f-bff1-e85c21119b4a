# rentUP Design System

This comprehensive design system provides guidelines, components, and patterns to ensure a consistent, accessible, and user-friendly experience across the rentUP platform. It serves as the source of truth for all design decisions and implementations.

## Core Principles

1. **User-Centered**: Prioritize user needs and experiences in all design decisions
2. **Consistency**: Maintain visual and behavioral consistency across all touchpoints
3. **Accessibility**: Ensure all components meet WCAG 2.1 AA standards
4. **Flexibility**: Design components that adapt to different contexts and screen sizes
5. **Trust**: Emphasize reliability, transparency, and security in all interactions

## Responsive Design Principles

### Breakpoints
- xs: 375px (Mobile)
- sm: 640px (Small tablets)
- md: 768px (Tablets)
- lg: 1024px (Laptops)
- xl: 1280px (Desktops)
- 2xl: 1536px (Large screens)

### Container Queries
We use container queries for component-level responsive design:
- Micro layouts (cards, widgets)
- Content adaptability
- Component-specific breakpoints

### Fluid Typography
- Base size: 16px
- Scale ratio: 1.125 to 1.25
- Responsive scaling between breakpoints

### Grid Systems
- Auto-fit for dynamic columns
- Gap customization
- Responsive padding
- Nested grids support

### Animation Principles
- Enter/exit animations
- Micro-interactions
- Performance considerations
- Reduced motion support

## Implementation
All components follow these responsive patterns using our custom Tailwind configuration and utility classes. The design system is implemented through a combination of:

1. **Tailwind Configuration**: Custom theme settings in `tailwind.config.ts`
2. **CSS Variables**: Global variables defined in `index.css`
3. **Component Library**: Reusable UI components in the `components/ui` directory
4. **Utility Hooks**: Custom React hooks for consistent behavior

## Color Palette

### Primary Colors
- **Primary Blue**: #3B82F6 - Used for primary buttons, links, and brand elements
- **Secondary Teal**: #0D9488 - Used for secondary UI elements and accents
- **Accent Orange**: #F97316 - Used for calls-to-action and highlighting important elements

### Feedback Colors
- **Success Green**: #10B981 - Indicates successful actions and positive states
- **Warning Amber**: #F59E0B - Highlights caution or attention required
- **Error Red**: #EF4444 - Signals errors or important alerts

### Neutral Colors
- **White**: #FFFFFF - Primary background color
- **Light Gray**: #F3F4F6 - Secondary background color
- **Medium Gray**: #9CA3AF - Used for disabled states and subtle UI elements
- **Dark Gray**: #4B5563 - Used for body text
- **Near Black**: #1F2937 - Used for headings and important text

### Color Usage Guidelines
- Use primary blue for main interactive elements
- Use accent orange sparingly for important actions
- Maintain sufficient contrast ratios for accessibility (minimum 4.5:1 for normal text)
- Ensure color is not the only means of conveying information

## Typography

### Font Families
- **Primary Font**: Inter - Used for all UI elements and body text
- **Secondary Font**: Montserrat - Used for headings and emphasis

### Font Sizes
- **Headings**:
  - H1: 48px (3rem)
  - H2: 36px (2.25rem)
  - H3: 30px (1.875rem)
  - H4: 24px (1.5rem)
  - H5: 20px (1.25rem)
  - H6: 18px (1.125rem)
- **Body**:
  - Default: 16px (1rem)
  - Small: 14px (0.875rem)
  - Extra Small: 12px (0.75rem)
- **Display**:
  - Display 1: 64px (4rem)
  - Display 2: 56px (3.5rem)

### Font Weights
- Regular (400): Default body text
- Medium (500): Emphasis and subheadings
- Semibold (600): Buttons and important UI elements
- Bold (700): Main headings and strong emphasis

### Line Heights
- Headings: 1.2
- Body text: 1.5
- Buttons and UI elements: 1.25

### Typography Best Practices
- Maintain a clear hierarchy with consistent heading styles
- Use appropriate line lengths (50-75 characters per line)
- Ensure sufficient contrast between text and background
- Use proper text alignment (left-aligned for most content)

## Spacing System

### Base Unit
Our spacing system is built on a 4px base unit.

### Spacing Scale
- xs: 4px (0.25rem)
- sm: 8px (0.5rem)
- md: 16px (1rem)
- lg: 24px (1.5rem)
- xl: 32px (2rem)
- 2xl: 48px (3rem)
- 3xl: 64px (4rem)
- 4xl: 96px (6rem)

### Component Spacing
- Card padding: 16px
- Section margins: 64px
- Form field gaps: 16px
- Button padding: 8px 16px (horizontal), 8px (vertical)
- Grid gutters: 24px
- List item spacing: 8px

### Spacing Guidelines
- Use consistent spacing throughout the interface
- Apply appropriate white space to improve readability
- Maintain consistent margins and padding within component types
- Use the spacing scale for all layout decisions

## Iconography

### Icon Style
- Outlined with 2px stroke
- Rounded corners (4px radius)
- Consistent visual weight
- Clear, recognizable shapes

### Icon Sizes
- Small: 16px (for dense UI areas)
- Medium: 24px (standard UI elements)
- Large: 32px (featured areas)

### Icon Library
We use a custom icon set based on Phosphor Icons. All icons should be from this library to maintain consistency.

### Icon Usage Guidelines
- Use icons consistently for similar actions
- Pair icons with text for clarity
- Maintain adequate touch targets (minimum 44px)
- Use color sparingly for emphasis
- Ensure icons are recognizable and intuitive

## Components

### Interactive Elements

#### Buttons
- **Primary**: Blue background, white text, rounded corners
- **Secondary**: Teal background, white text, rounded corners
- **Accent**: Orange background, white text, rounded corners
- **Outline**: Transparent background, colored border, primary text
- **Glass**: Semi-transparent white with blur effect
- **Danger**: Red background, white text, rounded corners
- **Success**: Green background, white text, rounded corners
- **Disabled**: Gray background, darker gray text, no hover effects

#### Button Sizes
- **Small**: Compact size for dense UIs (24px height)
- **Medium**: Standard size for most uses (36px height)
- **Large**: Prominent size for main CTAs (48px height)

#### Links
- **Primary**: Blue text, optional underline on hover
- **Secondary**: Teal text, optional underline on hover
- **Accent**: Orange text, optional underline on hover
- **White**: White text for dark backgrounds
- **Dark**: Near-black text for light backgrounds

#### Interactive Behaviors
- **Hover Effects**: All interactive elements have visible hover states
- **Focus States**: Clear focus indicators for keyboard navigation
- **Active States**: Visual feedback when pressed/clicked
- **Loading States**: Spinner animation during async operations
- **Ripple Effect**: Material Design-inspired feedback on click

#### Accessibility Features
- **Title Attributes**: All interactive elements have descriptive titles
- **ARIA Support**: Proper ARIA attributes for screen readers
- **Keyboard Navigation**: Full keyboard support with visible focus
- **High Contrast Mode**: Special styles for Windows High Contrast Mode
- **Touch Targets**: Minimum size of 44px for touch interfaces

For detailed implementation guidelines, see our [Interactive Elements Documentation](./interactive-elements.md).

### Form Elements
- **Text Inputs**: Light border, rounded corners, clear focus state
- **Checkboxes**: Custom design with clear checked/unchecked states
- **Radio Buttons**: Custom design with clear selected/unselected states
- **Dropdowns**: Consistent with text inputs, clear dropdown indicator
- **Error States**: Red border and error text below the input

### Cards
- Consistent padding (16px)
- Light shadow
- Rounded corners (8px)
- Clear hierarchy of information
- Hover effects for interactive cards
- Badge system for status indicators

### Item Cards
- Consistent design across all item listings
- Primary image with hover effect to show secondary image
- Overlay actions on hover (quick view, wishlist)
- Prominent display of price, reliability score, and owner rating
- Clear call-to-action buttons
- Badge system for special features (Rent-to-Buy, New, etc.)

### Navigation
- Clear active states
- Consistent spacing
- Visible hover effects
- Mobile-friendly touch targets
- Dropdown menus for categories
- Mega menu support for complex navigation

## Responsive Design

### Breakpoints
- Mobile: 0-639px
- Tablet: 640px-1023px
- Desktop: 1024px+

### Responsive Principles
- Mobile-first approach
- Fluid layouts that adapt to screen size
- Appropriate component sizing for different devices
- Consistent experience across all screen sizes

### Mobile Experience
- Collapsible search and navigation
- Persistent logo and account access
- Bottom navigation bar for primary actions
- Horizontal scrolling category icons
- Optimized item cards with larger tap targets
- Simplified forms with full-width inputs

## Accessibility

All design elements must meet WCAG 2.1 AA standards:
- Sufficient color contrast (minimum 4.5:1 for normal text)
- Keyboard navigability
- Screen reader compatibility
- Focus indicators
- Alternative text for images
- Proper heading structure

## Design Assets

All design assets are available in the following formats:
- Figma components
- SVG icons
- Color swatches
- Font files

## Implementation with Tailwind CSS

Our design system is implemented using Tailwind CSS with custom configuration. See the `tailwind.config.ts` file for specific implementation details.

### Key Implementation Files
- `tailwind.config.ts`: Core configuration for colors, spacing, typography, etc.
- `index.css`: Global styles and CSS variables
- `interactive.css`: Styles for interactive elements
- `ripple.css`: Ripple effect implementation
- `components/ui/`: Reusable UI components
- `hooks/useInteractive.ts`: Hook for consistent interactive behavior

## Domain-Specific Components

### Reliability System Components
- **Reliability Badge**
  - Color-coded score display (Excellent, Good, Average, Poor)
  - Numerical score (0-100)
  - Compact and detailed variants

- **Item History Display**
  - Age information (how long owner has had item)
  - Maintenance records timeline
  - Rental history statistics
  - Success rate visualization

- **Pre-Rental Testing**
  - Testing checklist
  - Last tested date
  - Verification status
  - Backup availability indicator

### Financial Components
- **Transaction Card**
  - Status indicators (Completed, Pending, Failed)
  - Amount display with currency
  - Action buttons (Download, Dispute, Pay)

- **Balance Display**
  - Available balance
  - Pending amounts
  - Security deposits
  - Credit limit

- **Payment Forms**
  - Add funds
  - Withdraw
  - Schedule payment
  - Update payment method

- **Financial Charts**
  - Transaction history
  - Revenue trends
  - Category breakdown
  - Projection graphs

### Status Indicators
- Success: #10B981
- Warning: #F59E0B
- Danger: #EF4444
- Info: #3B82F6
- Processing: #6366F1

### Financial Typography
- Amounts: Monospace font for alignment
- Currency: Semi-bold weight
- Status: Badge-style display
- Dates: Consistent format with timezone
