# Interactive Elements System

The rentUP Interactive Elements System provides a comprehensive framework for creating consistent, accessible, and user-friendly interactive components across the platform. This system ensures that all buttons, links, and other interactive elements follow the same design patterns, accessibility standards, and behavioral principles.

## Core Principles

1. **Consistency**: All interactive elements share common behaviors and visual cues
2. **Accessibility**: Every element meets WCAG 2.1 AA standards
3. **Extensibility**: Components can be easily extended for specialized functionality
4. **Maintainability**: Centralized styling and behavior for easier updates
5. **Performance**: Optimized for minimal bundle size and maximum responsiveness

## Implementation

The Interactive Elements System consists of three main parts:

1. **CSS Framework**: Centralized styles for all interactive elements
2. **React Components**: Reusable components with built-in accessibility
3. **Utility Hooks**: Custom hooks for consistent behavior

### CSS Framework

The CSS framework is implemented in two main files:

- `interactive.css`: Core styles for buttons, links, and other interactive elements
- `ripple.css`: Styles for the ripple effect feedback

These files are imported in the main `index.css` file to ensure they're available throughout the application.

### React Components

Two main components form the foundation of the system:

- `InteractiveButton`: Enhanced button component with consistent styling and behavior
- `InteractiveLink`: Enhanced link component that works with React Router

Both components support:
- Multiple variants and sizes
- Loading states
- Icons (left and right)
- Full width option
- Title attributes for accessibility
- ARIA attributes for screen readers
- Ripple effect feedback

### Utility Hooks

The `useInteractive` hook provides consistent behavior for all interactive elements:

- Handles click events with loading state management
- Manages ripple effect creation and cleanup
- Provides accessibility attributes
- Handles disabled states

## Usage

### Button Example

```tsx
import { InteractiveButton } from '../components/ui';

// Basic usage
<InteractiveButton>Click Me</InteractiveButton>

// With variant and size
<InteractiveButton
  variant="primary"
  size="lg"
  title="Submit the form"
>
  Submit
</InteractiveButton>

// With loading state
<InteractiveButton
  isLoading={isSubmitting}
  onClick={handleSubmit}
>
  {isSubmitting ? 'Submitting...' : 'Submit'}
</InteractiveButton>

// With icons
<InteractiveButton
  leftIcon={<SearchIcon />}
  variant="secondary"
>
  Search
</InteractiveButton>
```

### Link Example

```tsx
import { InteractiveLink } from '../components/ui';

// Basic usage
<InteractiveLink to="/about">About Us</InteractiveLink>

// With variant
<InteractiveLink
  to="/contact"
  variant="secondary"
  title="Contact our support team"
>
  Contact Us
</InteractiveLink>

// As a button
<InteractiveLink
  to="/signup"
  asButton
  buttonVariant="primary"
  buttonSize="lg"
>
  Sign Up
</InteractiveLink>

// External link
<InteractiveLink
  external
  externalHref="https://example.com"
  title="Visit our partner site"
>
  Partner Site
</InteractiveLink>
```

## Available Options

### Button Variants

- `primary`: Blue background, white text (default)
- `secondary`: Teal background, white text
- `accent`: Orange background, white text
- `outline`: Transparent background, colored border
- `glass`: Semi-transparent white with blur effect
- `danger`: Red background for destructive actions
- `success`: Green background for success actions

### Link Variants

- `primary`: Blue text (default)
- `secondary`: Teal text
- `accent`: Orange text
- `white`: White text (for dark backgrounds)
- `dark`: Near-black text

### Sizes

- `sm`: Small size
- `md`: Medium size (default)
- `lg`: Large size

## Accessibility Features

The Interactive Elements System includes several features to ensure accessibility:

1. **Title Attributes**: All components accept a `title` prop that sets the HTML title attribute
2. **ARIA Attributes**: Appropriate ARIA attributes are automatically added based on component state
3. **Keyboard Navigation**: All components are fully keyboard navigable
4. **Focus Styles**: Clear focus indicators for keyboard navigation
5. **Screen Reader Support**: Proper labeling and state announcements
6. **High Contrast Mode Support**: Special styles for Windows High Contrast Mode

## Extending the System

The Interactive Elements System is designed to be extended for specialized needs:

1. **Custom Variants**: Add new variants by extending the CSS classes
2. **Custom Components**: Create specialized components that use the base components
3. **Custom Hooks**: Extend the `useInteractive` hook for specialized behavior

## Demo

A demo component is available at `/components/InteractiveDemo.tsx` that showcases all the available options and combinations.

## Best Practices

1. **Always provide a title**: Use the `title` prop to provide a descriptive title for screen readers
2. **Use appropriate variants**: Choose the right variant for the context (primary for main actions, etc.)
3. **Be consistent**: Use the same variant for similar actions across the application
4. **Provide feedback**: Use loading states to indicate when an action is being processed
5. **Keep it simple**: Avoid cluttering the interface with too many buttons or links
6. **Test with keyboard**: Ensure all interactive elements can be accessed and used with keyboard only
7. **Test with screen readers**: Verify that screen readers announce the elements correctly
8. **Ensure cursor pointer**: All interactive elements should change the cursor to a pointer on hover
9. **Consistent hover effects**: All similar interactive elements should have the same hover effect
10. **Use the interactive components**: Always use `InteractiveButton` and `InteractiveLink` instead of native elements

## Migration Guide

If you're updating existing components to use the Interactive Elements System:

1. Replace `<button>` with `<InteractiveButton>`
2. Replace `<Link>` with `<InteractiveLink>`
3. Replace `<a>` with `<InteractiveLink external externalHref="...">`
4. Add appropriate `title` attributes to all interactive elements
5. Update CSS classes to use the new system

## Browser Support

The Interactive Elements System is compatible with all modern browsers:

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Opera (latest)

Internet Explorer is not supported.

## Rental Platform-Specific Interactions

### Item Card Interactions
- **Hover**: Show secondary image and overlay actions
- **Quick View**: Open modal with item details without leaving the page
- **Wishlist**: Add to wishlist with visual feedback
- **Rent Now**: Direct to booking flow

### Reliability Badge Interactions
- **Hover**: Show detailed reliability information
- **Click**: Navigate to full reliability history

### Category Navigation
- **Hover**: Show subcategories in dropdown
- **Click**: Navigate to category page
- **Mobile Tap**: Expand/collapse subcategories
