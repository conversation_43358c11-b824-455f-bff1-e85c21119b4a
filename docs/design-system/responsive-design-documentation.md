# RentUp Responsive Design System Documentation

This document provides comprehensive guidelines for implementing responsive design across the RentUp platform, ensuring optimal user experience on all device sizes.

## Table of Contents

1. [Introduction](#introduction)
2. [Breakpoints](#breakpoints)
3. [Responsive Typography](#responsive-typography)
4. [Responsive Layouts](#responsive-layouts)
5. [Touch-Friendly Interfaces](#touch-friendly-interfaces)
6. [Responsive Components](#responsive-components)
7. [AI Feature Responsiveness](#ai-feature-responsiveness)
8. [Testing Guidelines](#testing-guidelines)
9. [Best Practices](#best-practices)

## Introduction

The RentUp responsive design system ensures that all interfaces adapt seamlessly to different screen sizes and device capabilities. This system is built on the following principles:

- **Mobile-first approach**: Design for mobile first, then enhance for larger screens
- **Fluid layouts**: Use flexible grids and containers that adapt to screen size
- **Responsive typography**: Implement fluid typography that scales appropriately
- **Touch-friendly interfaces**: Ensure all interactive elements are usable on touch devices
- **Performance optimization**: Optimize assets and code for all device capabilities

## Breakpoints

The RentUp platform uses the following breakpoints to define different device sizes:

| Name | Size | Device Type |
|------|------|-------------|
| xs | 375px | Mobile phones (small) |
| sm | 640px | Mobile phones (large) |
| md | 768px | Tablets (portrait) |
| lg | 1024px | Tablets (landscape) / Small desktops |
| xl | 1280px | Desktops |
| 2xl | 1536px | Large desktops |

These breakpoints are defined as CSS variables:

```css
:root {
  --breakpoint-xs: 375px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}
```

### Media Query Usage

Use these breakpoints consistently in media queries:

```css
/* Mobile first approach */
.element {
  /* Base styles for mobile */
}

@media (min-width: 640px) {
  .element {
    /* Styles for sm breakpoint and up */
  }
}

@media (min-width: 768px) {
  .element {
    /* Styles for md breakpoint and up */
  }
}

/* etc. */
```

## Responsive Typography

The RentUp platform uses fluid typography to ensure text scales appropriately across all device sizes.

### Fluid Typography

Fluid typography uses `clamp()` functions to define font sizes that scale smoothly between breakpoints:

```css
:root {
  --fluid-h1: clamp(1.75rem, calc(1.75rem + 1.5vw), 3rem);
  --fluid-h2: clamp(1.5rem, calc(1.5rem + 0.75vw), 2.25rem);
  --fluid-h3: clamp(1.25rem, calc(1.25rem + 0.625vw), 1.875rem);
  --fluid-h4: clamp(1.125rem, calc(1.125rem + 0.375vw), 1.5rem);
  --fluid-h5: clamp(1rem, calc(1rem + 0.25vw), 1.25rem);
  --fluid-h6: clamp(0.875rem, calc(0.875rem + 0.125vw), 1.125rem);
  --fluid-body: clamp(0.875rem, calc(0.875rem + 0.125vw), 1rem);
  --fluid-small: clamp(0.75rem, calc(0.75rem + 0.0625vw), 0.875rem);
}
```

### Usage

Apply these fluid typography variables to text elements:

```css
h1 {
  font-size: var(--fluid-h1);
}

h2 {
  font-size: var(--fluid-h2);
}

/* etc. */
```

## Responsive Layouts

The RentUp platform uses several responsive layout patterns to ensure content adapts appropriately to different screen sizes.

### Responsive Container

The `container-responsive` class creates a centered container with appropriate padding that adapts to screen size:

```css
.container-responsive {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-4);
  padding-right: var(--spacing-4);
}

@media (max-width: 639px) {
  .container-responsive {
    padding-left: var(--spacing-3);
    padding-right: var(--spacing-3);
  }
}

@media (min-width: 640px) {
  .container-responsive {
    max-width: var(--container-sm);
  }
}

/* etc. */
```

### Responsive Grid

The `grid-responsive-cards` class creates a grid layout that adjusts the number of columns based on screen size:

```css
.grid-responsive-cards {
  display: grid;
  gap: var(--spacing-4);
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .grid-responsive-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-5);
  }
}

@media (min-width: 1024px) {
  .grid-responsive-cards {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-6);
  }
}

@media (min-width: 1280px) {
  .grid-responsive-cards {
    grid-template-columns: repeat(4, 1fr);
  }
}
```

### Responsive Stacking

The `stack-responsive` class creates a layout that stacks elements vertically on mobile and horizontally on larger screens:

```css
.stack-responsive {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

@media (min-width: 768px) {
  .stack-responsive {
    flex-direction: row;
    align-items: center;
  }
}
```

## Touch-Friendly Interfaces

The RentUp platform ensures all interactive elements are usable on touch devices by following these guidelines:

### Touch Target Size

All interactive elements should have a minimum touch target size of 44x44 pixels, as defined by WCAG 2.1:

```css
:root {
  --touch-target-size: 44px;
  --touch-target-spacing: 8px;
}

.touch-friendly {
  min-height: var(--touch-target-size);
  min-width: var(--touch-target-size);
  padding: var(--spacing-2) var(--spacing-4);
}

@media (max-width: 639px) {
  .touch-friendly {
    padding: var(--spacing-3) var(--spacing-4);
  }
}
```

### Spacing Between Touch Targets

Ensure adequate spacing between touch targets to prevent accidental taps:

```css
.touch-targets-container {
  display: flex;
  gap: var(--touch-target-spacing);
}
```

## Responsive Components

The RentUp platform includes several responsive components that adapt to different screen sizes.

### Responsive Card

The Card component includes responsive options:

```jsx
<Card 
  responsivePadding // Adjusts padding based on screen size
  stackOnMobile // Stacks content vertically on mobile
>
  <Card.Header title="Responsive Card" />
  <Card.Content>Content here</Card.Content>
  <Card.Footer>Footer here</Card.Footer>
</Card>
```

### Responsive Media

The CardMedia component includes responsive options:

```jsx
<Card.Media 
  src="image.jpg" 
  alt="Description"
  responsiveAspectRatio // Maintains aspect ratio across screen sizes
  aspectRatio="16-9" // Defines the aspect ratio (16:9, 4:3, 1:1)
/>
```

### Collapsible Sections

For complex interfaces on mobile, use collapsible sections:

```jsx
<div className="collapsible-section">
  <button 
    className="collapsible-section-header"
    onClick={() => setExpanded(!expanded)}
    aria-expanded={expanded}
  >
    Section Title
    <span>{expanded ? '−' : '+'}</span>
  </button>
  <div className={`collapsible-section-content ${expanded ? 'expanded' : ''}`}>
    Content here
  </div>
</div>
```

## AI Feature Responsiveness

The RentUp platform includes several AI features that require special consideration for responsive design.

### Responsive Recommendation Section

The ResponsiveRecommendationSection component displays AI recommendations in a responsive grid:

```jsx
<ResponsiveRecommendationSection
  title="Recommended for You"
  type="personalized"
  limit={4}
  collapsible={true} // Can be collapsed on mobile
  initiallyExpanded={true} // Initially expanded on mobile
/>
```

### Responsive AI Curation Interface

The ResponsiveAICurationInterface component provides a simplified interface on mobile:

```jsx
<ResponsiveAICurationInterface
  title="AI Curation"
  description="Use AI to curate items based on your preferences"
/>
```

### Responsive Analytics Dashboard

The ResponsiveAnalyticsDashboard component uses collapsible sections on mobile:

```jsx
<ResponsiveAnalyticsDashboard
  title="Analytics Dashboard"
  period="month"
/>
```

## Testing Guidelines

To ensure responsive designs work correctly across all devices, follow these testing guidelines:

### Device Testing

Test the responsive design on the following devices:

- **Mobile phones**: iPhone 12/13/14, Samsung Galaxy S21/S22
- **Tablets**: iPad, Samsung Galaxy Tab
- **Desktops**: Various screen sizes (1024px, 1280px, 1536px+)

### Browser Testing

Test the responsive design on the following browsers:

- **Chrome**: Latest version
- **Firefox**: Latest version
- **Safari**: Latest version
- **Edge**: Latest version

### Orientation Testing

Test both portrait and landscape orientations on mobile and tablet devices.

### Responsive Testing Tools

Use the following tools for responsive testing:

- **Browser Developer Tools**: Use the device mode to simulate different screen sizes
- **BrowserStack**: Test on real devices
- **Lighthouse**: Test performance on mobile and desktop

## Best Practices

Follow these best practices for responsive design:

### Mobile-First Approach

Always start with mobile designs and enhance for larger screens. This ensures a solid foundation for all devices.

### Fluid Layouts

Use flexible layouts that adapt to screen size:

- Avoid fixed widths and heights
- Use relative units (%, em, rem) instead of pixels
- Implement responsive grids and containers

### Performance Optimization

Optimize performance for all devices:

- Use responsive images with `srcset` and `sizes` attributes
- Implement code splitting to reduce bundle size
- Minimize HTTP requests
- Optimize CSS and JavaScript

### Accessibility

Ensure accessibility across all screen sizes:

- Maintain proper color contrast
- Ensure touch targets are large enough
- Provide keyboard navigation
- Test with screen readers

### Testing and Iteration

Continuously test and iterate on responsive designs:

- Test on real devices whenever possible
- Gather user feedback
- Monitor analytics for mobile vs. desktop usage
- Iterate based on feedback and data
