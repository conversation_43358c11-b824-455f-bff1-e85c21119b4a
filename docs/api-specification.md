# RentUp API Specification

## API Overview

The RentUp API is a RESTful API that provides access to the core functionality of the RentUp platform. It follows OpenAPI 3.0 standards and uses JSON for data exchange.

### Base URL

- Production: `https://api.rentup.com/v1`
- Staging: `https://api-staging.rentup.com/v1`
- Development: `https://api-dev.rentup.com/v1`

### Authentication

The API uses JWT (JSON Web Tokens) for authentication. Tokens are obtained through the `/auth/login` endpoint (supporting password, social login, and QR code authentication) and must be included in the `Authorization` header of all authenticated requests.

Authorization: Bearer {token}
### Rate Limiting

- Standard rate limit: 100 requests per minute
- Enhanced rate limit (for partners): 300 requests per minute
- Rate limit headers are included in all responses:
  - `X-RateLimit-Limit`: Maximum requests per minute
  - `X-RateLimit-Remaining`: Remaining requests in the current window
  - `X-RateLimit-Reset`: Time when the rate limit resets (Unix timestamp)

### Error Handling

All errors follow a standard format:
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {
      "field": "Additional information about the error"
    }
  }
}

Common HTTP status codes:

- 200 OK : Request succeeded
- 201 Created : Resource created successfully
- 400 Bad Request : Invalid request parameters
- 401 Unauthorized : Authentication required
- 403 Forbidden : Insufficient permissions
- 404 Not Found : Resource not found
- 422 Unprocessable Entity : Validation error
- 429 Too Many Requests : Rate limit exceeded
- 500 Internal Server Error : Server error

## API Endpoints
### Authentication POST /auth/register
Create a new user account.

Request Body:
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "firstName": "John",
  "lastName": "Doe",
  "phoneNumber": "+**********"
}

Response (201 Created):
{
  "user": {
    "id": "usr_123456789",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "phoneNumber": "+**********",
    "createdAt": "2025-03-23T12:00:00Z"
  },
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}

POST /auth/login
Authenticate a user and get an access token.

Request Body:
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}

Response (200 OK):
{
  "user": {
    "id": "usr_123456789",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe"
  },
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}

POST /auth/refresh
Refresh an access token.

Request Headers:
Authorization: Bearer {expired_token}

Response (200 OK):
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}

POST /auth/logout
Invalidate the current access token.

Request Headers:
Authorization: Bearer {token}

Response (204 No Content)

### Users
GET /users/me
Get the current user's profile.

Request Headers:
Authorization: Bearer {token}

Response (200 OK):
{
  "id": "usr_123456789",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "phoneNumber": "+**********",
  "profileImage": "https://storage.rentup.com/profiles/usr_123456789.jpg",
  "bio": "I love renting and sharing items!",
  "location": {
    "city": "San Francisco",
    "state": "CA",
    "country": "USA",
    "coordinates": {
      "latitude": 37.7749,
      "longitude": -122.4194
    }
  },
  "trustScore": 4.8,
  "verifications": {
    "email": true,
    "phone": true,
    "government": false,
    "facebook": true
  },
  "createdAt": "2025-03-23T12:00:00Z",
  "updatedAt": "2025-03-23T12:00:00Z"
}

PUT /users/me
Update the current user's profile.

Request Headers:
Authorization: Bearer {token}

Request Body:
{
  "firstName": "John",
  "lastName": "Doe",
  "phoneNumber": "+**********",
  "bio": "I love renting and sharing items!",
  "location": {
    "city": "San Francisco",
    "state": "CA",
    "country": "USA",
    "coordinates": {
      "latitude": 37.7749,
      "longitude": -122.4194
    }
  }
}

Response (200 OK):
{
  "id": "usr_123456789",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "phoneNumber": "+**********",
  "profileImage": "https://storage.rentup.com/profiles/usr_123456789.jpg",
  "bio": "I love renting and sharing items!",
  "location": {
    "city": "San Francisco",
    "state": "CA",
    "country": "USA",
    "coordinates": {
      "latitude": 37.7749,
      "longitude": -122.4194
    }
  },
  "updatedAt": "2025-03-23T12:30:00Z"
}

POST /users/me/profile-image
Upload a profile image.

Request Headers:
Authorization: Bearer {token}
Content-Type: multipart/form-data

Request Body:
file: [binary data]

Response (200 OK):
{
  "profileImage": "https://storage.rentup.com/profiles/usr_123456789.jpg",
  "updatedAt": "2025-03-23T12:45:00Z"
}

GET /users/{userId}
Get a user's public profile.

Request Headers:
Authorization: Bearer {token}

Response (200 OK):
{
  "id": "usr_987654321",
  "firstName": "Jane",
  "lastName": "Smith",
  "profileImage": "https://storage.rentup.com/profiles/usr_987654321.jpg",
  "bio": "I have many items to rent!",
  "location": {
    "city": "San Francisco",
    "state": "CA",
    "country": "USA"
  },
  "trustScore": 4.9,
  "verifications": {
    "email": true,
    "phone": true,
    "government": true,
    "facebook": true
  },
  "memberSince": "2024-01-15T00:00:00Z",
  "responseRate": 98,
  "responseTime": "within 1 hour"
}

### Listings
GET /listings
Search for listings.

Query Parameters:

- query (string): Search term
- category (string): Category ID
- location (string): Location name or coordinates
- radius (number): Search radius in kilometers
- minPrice (number): Minimum price
- maxPrice (number): Maximum price
- availableFrom (string): Start date (ISO 8601)
- availableTo (string): End date (ISO 8601)
- sort (string): Sort order (relevance, price_asc, price_desc, distance)
- page (number): Page number
- limit (number): Items per page
Response (200 OK):
{
  "items": [
    {
      "id": "lst_123456789",
      "title": "Professional DSLR Camera",
      "description": "Canon EOS 5D Mark IV with 24-70mm lens",
      "category": {
        "id": "cat_photography",
        "name": "Photography"
      },
      "price": {
        "daily": 50.00,
        "weekly": 300.00,
        "monthly": 900.00,
        "currency": "USD"
      },
      "securityDeposit": 500.00,
      "images": [
        "https://storage.rentup.com/listings/lst_123456789_1.jpg",
        "https://storage.rentup.com/listings/lst_123456789_2.jpg"
      ],
      "location": {
        "city": "San Francisco",
        "state": "CA",
        "country": "USA",
        "distance": 2.5
      },
      "owner": {
        "id": "usr_987654321",
        "firstName": "Jane",
        "lastName": "S.",
        "profileImage": "https://storage.rentup.com/profiles/usr_987654321.jpg",
        "trustScore": 4.9
      },
      "rating": 4.8,
      "reviewCount": 24,
      "available": true
    }
  ],
  "pagination": {
    "total": 156,
    "page": 1,
    "limit": 20,
    "pages": 8
  }
}

POST /listings
Create a new listing.

Request Headers:
Authorization: Bearer {token}

Request Body:
{
  "title": "Professional DSLR Camera",
  "description": "Canon EOS 5D Mark IV with 24-70mm lens",
  "categoryId": "cat_photography",
  "price": {
    "hourly": 10.00,
    "daily": 50.00,
    "weekly": 300.00,
    "monthly": 900.00,
    "currency": "USD"
  },
  "securityDeposit": 500.00,
  "location": {
    "address": "123 Main St",
    "city": "San Francisco",
    "state": "CA",
    "zipCode": "94105",
    "country": "USA"
  },
  "specifications": {
    "brand": "Canon",
    "model": "EOS 5D Mark IV",
    "condition": "Excellent",
    "yearManufactured": 2020
  },
  "rules": {
    "pickupInstructions": "Please bring a valid ID for pickup",
    "returnInstructions": "Please return with all accessories",
    "cancellationPolicy": "Flexible"
  },
  "availability": {
    "instantBooking": true,
    "minRentalDuration": 1,
    "maxRentalDuration": 30
  }
}

Response (201 Created):
{
  "id": "lst_123456789",
  "title": "Professional DSLR Camera",
  "description": "Canon EOS 5D Mark IV with 24-70mm lens",
  "category": {
    "id": "cat_photography",
    "name": "Photography"
  },
  "price": {
    "hourly": 10.00,
    "daily": 50.00,
    "weekly": 300.00,
    "monthly": 900.00,
    "currency": "USD"
  },
  "securityDeposit": 500.00,
  "images": [],
  "location": {
    "address": "123 Main St",
    "city": "San Francisco",
    "state": "CA",
    "zipCode": "94105",
    "country": "USA"
  },
  "owner": {
    "id": "usr_123456789",
    "firstName": "John",
    "lastName": "D.",
    "profileImage": "https://storage.rentup.com/profiles/usr_123456789.jpg",
    "trustScore": 4.8
  },
  "specifications": {
    "brand": "Canon",
    "model": "EOS 5D Mark IV",
    "condition": "Excellent",
    "yearManufactured": 2020
  },
  "rules": {
    "pickupInstructions": "Please bring a valid ID for pickup",
    "returnInstructions": "Please return with all accessories",
    "cancellationPolicy": "Flexible"
  },
  "availability": {
    "instantBooking": true,
    "minRentalDuration": 1,
    "maxRentalDuration": 30
  },
  "createdAt": "2025-03-23T13:00:00Z",
  "updatedAt": "2025-03-23T13:00:00Z"
}

GET /listings/{listingId}
Get a listing by ID.

Response (200 OK):
{
  "id": "lst_123456789",
  "title": "Professional DSLR Camera",
  "description": "Canon EOS 5D Mark IV with 24-70mm lens",
  "category": {
    "id": "cat_photography",
    "name": "Photography"
  },
  "price": {
    "hourly": 10.00,
    "daily": 50.00,
    "weekly": 300.00,
    "monthly": 900.00,
    "currency": "USD"
  },
  "securityDeposit": 500.00,
  "images": [
    "https://storage.rentup.com/listings/lst_123456789_1.jpg",
    "https://storage.rentup.com/listings/lst_123456789_2.jpg",
    "https://storage.rentup.com/listings/lst_123456789_3.jpg"
  ],
  "location": {
    "address": "123 Main St",
    "city": "San Francisco",
    "state": "CA",
    "zipCode": "94105",
    "country": "USA",
    "coordinates": {
      "latitude": 37.7749,
      "longitude": -122.4194
    }
  },
  "owner": {
    "id": "usr_987654321",
    "firstName": "Jane",
    "lastName": "Smith",
    "profileImage": "https://storage.rentup.com/profiles/usr_987654321.jpg",
    "trustScore": 4.9,
    "responseRate": 98,
    "responseTime": "within 1 hour"
  },
  "specifications": {
    "brand": "Canon",
    "model": "EOS 5D Mark IV",
    "condition": "Excellent",
    "yearManufactured": 2020
  },
  "rules": {
    "pickupInstructions": "Please bring a valid ID for pickup",
    "returnInstructions": "Please return with all accessories",
    "cancellationPolicy": "Flexible"
  },
  "availability": {
    "calendar": "https://api.rentup.com/v1/listings/lst_123456789/availability",
    "instantBooking": true,
    "minRentalDuration": 1,
    "maxRentalDuration": 30
  },
  "rating": 4.8,
  "reviewCount": 24,
  "reviews": [
    {
      "id": "rev_123456789",
      "user": {
        "id": "usr_123456789",
        "firstName": "John",
        "lastName": "D.",
        "profileImage": "https://storage.rentup.com/profiles/usr_123456789.jpg"
      },
      "rating": 5,
      "comment": "Excellent camera, worked perfectly for my photoshoot!",
      "createdAt": "2025-02-15T12:00:00Z"
    }
  ],
  "createdAt": "2024-12-01T12:00:00Z",
  "updatedAt": "2025-03-01T12:00:00Z"
}

PUT /listings/{listingId}
Update a listing.

Request Headers:
Authorization: Bearer {token}

Request Body:
{
  "title": "Professional DSLR Camera Kit",
  "description": "Canon EOS 5D Mark IV with 24-70mm lens and accessories",
  "price": {
    "hourly": 12.00,
    "daily": 60.00,
    "weekly": 350.00,
    "monthly": 1000.00,
    "currency": "USD"
  },
  "securityDeposit": 600.00,
  "specifications": {
    "condition": "Like New"
  },
  "rules": {
    "pickupInstructions": "Please bring a valid ID and credit card for pickup"
  },
  "availability": {
    "instantBooking": false
  }
}

Response (200 OK):
{
  "id": "lst_123456789",
  "title": "Professional DSLR Camera Kit",
  "description": "Canon EOS 5D Mark IV with 24-70mm lens and accessories",
  "category": {
    "id": "cat_photography",
    "name": "Photography"
  },
  "price": {
    "hourly": 12.00,
    "daily": 60.00,
    "weekly": 350.00,
    "monthly": 1000.00,
    "currency": "USD"
  },
  "securityDeposit": 600.00,
  "updatedAt": "2025-03-23T14:00:00Z"
}

DELETE /listings/{listingId}
Delete a listing.

Request Headers:
Authorization: Bearer {token}

Response (204 No Content)

POST /listings/{listingId}/images
Upload images for a listing.

Request Headers:
Authorization: Bearer {token}
Content-Type: multipart/form-data

Request Body:
files[0]: [binary data]
files[1]: [binary data]
files[2]: [binary data]

Response (200 OK):
{
  "images": [
    "https://storage.rentup.com/listings/lst_123456789_1.jpg",
    "https://storage.rentup.com/listings/lst_123456789_2.jpg",
    "https://storage.rentup.com/listings/lst_123456789_3.jpg"
  ],
  "updatedAt": "2025-03-23T14:15:00Z"
}

DELETE /listings/{listingId}/images/{imageId}
Delete an image from a listing.

Request Headers:
Authorization: Bearer {token}

Response (204 No Content)

GET /listings/{listingId}/availability
Get availability calendar for a listing.

Query Parameters:

- startDate (string): Start date (ISO 8601)
- endDate (string): End date (ISO 8601)
Response (200 OK):
{
  "listingId": "lst_123456789",
  "availability": [
    {
      "date": "2025-04-01",
      "available": true,
      "price": 50.00
    },
    {
      "date": "2025-04-02",
      "available": true,
      "price": 50.00
    },
    {
      "date": "2025-04-03",
      "available": false,
      "bookingId": "bkg_123456789"
    },
    {
      "date": "2025-04-04",
      "available": false,
      "bookingId": "bkg_123456789"
    }
  ]
}

PUT /listings/{listingId}/availability
Update availability for a listing.

Request Headers:
Authorization: Bearer {token}

Request Body:
{
  "dates": [
    {
      "date": "2025-04-10",
      "available": false
    },
    {
      "date": "2025-04-11",
      "available": false
    },
    {
      "date": "2025-04-12",
      "available": false
    }
  ]
}

Response (200 OK):
{
  "success": true,
  "updatedDates": 3
}

### Bookings
POST /bookings
Create a booking request.

Request Headers:
Authorization: Bearer {token}

Request Body:
{
  "listingId": "lst_123456789",
  "startDate": "2025-04-15T10:00:00Z",
  "endDate": "2025-04-17T18:00:00Z",
  "message": "I would like to rent this camera for a weekend photoshoot."
}

Response (201 Created):
{
  "id": "bkg_123456789",
  "listing": {
    "id": "lst_123456789",
    "title": "Professional DSLR Camera Kit",
    "image": "https://storage.rentup.com/listings/lst_123456789_1.jpg"
  },
  "renter": {
    "id": "usr_123456789",
    "firstName": "John",
    "lastName": "D.",
    "profileImage": "https://storage.rentup.com/profiles/usr_123456789.jpg"
  },
  "owner": {
    "id": "usr_987654321",
    "firstName": "Jane",
    "lastName": "S.",
    "profileImage": "https://storage.rentup.com/profiles/usr_987654321.jpg"
  },
  "status": "pending",
  "startDate": "2025-04-15T10:00:00Z",
  "endDate": "2025-04-17T18:00:00Z",
  "duration": {
    "days": 2,
    "hours": 8
  },
  "price": {
    "basePrice": 120.00,
    "serviceFee": 24.00,
    "securityDeposit": 600.00,
    "total": 144.00,
    "currency": "USD"
  },
  "message": "I would like to rent this camera for a weekend photoshoot.",
  "createdAt": "2025-03-23T15:00:00Z",
  "expiresAt": "2025-03-24T15:00:00Z"
}

GET /bookings
Get all bookings for the current user.

Request Headers:
Authorization: Bearer {token}

Query Parameters:

- role (string): Filter by role (renter, owner, all)
- status (string): Filter by status (pending, approved, rejected, canceled, completed)
- page (number): Page number
- limit (number): Items per page
Response (200 OK):
{
  "items": [
    {
      "id": "bkg_123456789",
      "listing": {
        "id": "lst_123456789",
        "title": "Professional DSLR Camera Kit",
        "image": "https://storage.rentup.com/listings/lst_123456789_1.jpg"
      },
      "renter": {
        "id": "usr_123456789",
        "firstName": "John",
        "lastName": "D.",
        "profileImage": "https://storage.rentup.com/profiles/usr_123456789.jpg"
      },
      "owner": {
        "id": "usr_987654321",
        "firstName": "Jane",
        "lastName": "S.",
        "profileImage": "https://storage.rentup.com/profiles/usr_987654321.jpg"
      },
      "status": "pending",
      "startDate": "2025-04-15T10:00:00Z",
      "endDate": "2025-04-17T18:00:00Z",
      "price": {
        "total": 144.00,
        "currency": "USD"
      },
      "createdAt": "2025-03-23T15:00:00Z"
    }
  ],
  "pagination": {
    "total": 5,
    "page": 1,
    "limit": 20,
    "pages": 1
  }
}

GET /bookings/{bookingId}
Get a booking by ID.

Request Headers:
Authorization: Bearer {token}

Response (200 OK):
{
  "id": "bkg_123456789",
  "listing": {
    "id": "lst_123456789",
    "title": "Professional DSLR Camera Kit",
    "description": "Canon EOS 5D Mark IV with 24-70mm lens and accessories",
    "image": "https://storage.rentup.com/listings/lst_123456789_1.jpg",
    "location": {
      "address": "123 Main St",
      "city": "San Francisco",
      "state": "CA",
      "zipCode": "94105",
      "country": "USA"
    }
  },
  "renter": {
    "id": "usr_123456789",
    "firstName": "John",
    "lastName": "Doe",
    "profileImage": "https://storage.rentup.com/profiles/usr_123456789.jpg",
    "phoneNumber": "+**********",
    "trustScore": 4.8
  },
  "owner": {
    "id": "usr_987654321",
    "firstName": "Jane",
    "lastName": "Smith",
    "profileImage": "https://storage.rentup.com/profiles/usr_987654321.jpg",
    "phoneNumber": "+1987654321",
    "trustScore": 4.9
  },
  "status": "pending",
  "startDate": "2025-04-15T10:00:00Z",
  "endDate": "2025-04-17T18:00:00Z",
  "duration": {
    "days": 2,
    "hours": 8
  },
  "price": {
    "basePrice": 120.00,
    "serviceFee": 24.00,
    "securityDeposit": 600.00,
    "total": 144.00,
    "currency": "USD"
  },
  "message": "I would like to rent this camera for a weekend photoshoot.",
  "timeline": [
    {
      "status": "created",
      "timestamp": "2025-03-23T15:00:00Z",
      "actor": "renter"
    }
  ],
  "createdAt": "2025-03-23T15:00:00Z",
  "expiresAt": "2025-03-24T15:00:00Z"
}

PUT /bookings/{bookingId}/approve
Approve a booking request.

Request Headers:
Authorization: Bearer {token}

Response (200 OK):
{
  "id": "bkg_123456789",
  "status": "approved",
  "timeline": [
    {
      "status": "created",
      "timestamp": "2025-03-23T15:00:00Z",
      "actor": "renter"
    },
    {
      "status": "approved",
      "timestamp": "2025-03-23T16:00:00Z",
      "actor": "owner"
    }
  ],
  "updatedAt": "2025-03-23T16:00:00Z"
}

PUT /bookings/{bookingId}/reject
Reject a booking request.

Request Headers:
Authorization: Bearer {token}

Request Body:
{
  "reason": "Item is not available on those dates."
}

Response (200 OK):
{
  "id": "bkg_123456789",
  "status": "rejected",
  "rejectionReason": "Item is not available on those dates.",
  "timeline": [
    {
      "status": "created",
      "timestamp": "2025-03-23T15:00:00Z",
      "actor": "renter"
    },
    {
      "status": "rejected",
      "timestamp": "2025-03-23T16:00:00Z",
      "actor": "owner",
      "reason": "Item is not available on those dates."
    }
  ],
  "updatedAt": "2025-03-23T16:00:00Z"
}

PUT /bookings/{bookingId}/cancel
Cancel a booking.

Request Headers:
Authorization: Bearer {token}

Request Body:
{
  "reason": "Plans changed, no longer need the item."
}

Response (200 OK):
{
  "id": "bkg_123456789",
  "status": "canceled",
  "cancellationReason": "Plans changed, no longer need the item.",
  "timeline": [
    {
      "status": "created",
      "timestamp": "2025-03-23T15:00:00Z",
      "actor": "renter"
    },
    {
      "status": "approved",
      "timestamp": "2025-03-23T16:00:00Z",
      "actor": "owner"
    },
    {
      "status": "canceled",
      "timestamp": "2025-03-23T17:00:00Z",
      "actor": "renter",
      "reason": "Plans changed, no longer need the item."
    }
  ],
  "updatedAt": "2025-03-23T17:00:00Z"
}

PUT /bookings/{bookingId}/complete
Mark a booking as completed.

Request Headers:
Authorization: Bearer {token}

Response (200 OK):
{
  "id": "bkg_123456789",
  "status": "completed",
  "timeline": [
    {
      "status": "created",
      "timestamp": "2025-03-23T15:00:00Z",
      "actor": "renter"
    },
    {
      "status": "approved",
      "timestamp": "2025-03-23T16:00:00Z",
      "actor": "owner"
    },
    {
      "status": "completed",
      "timestamp": "2025-04-17T18:30:00Z",
      "actor": "owner"
    }
  ],
  "updatedAt": "2025-04-17T18:30:00Z"
}

### Messages
GET /conversations
Get all conversations for the current user.

Request Headers:
Authorization: Bearer {token}

Query Parameters:
- page (number): Page number
- limit (number): Items per page

Response (200 OK):
{
  "items": [
    {
      "id": "conv_123456789",
      "participants": [
        {
          "id": "usr_123456789",
          "firstName": "John",
          "lastName": "D.",
          "profileImage": "https://storage.rentup.com/profiles/usr_123456789.jpg"
        },
        {
          "id": "usr_987654321",
          "firstName": "Jane",
          "lastName": "S.",
          "profileImage": "https://storage.rentup.com/profiles/usr_987654321.jpg"
        }
      ],
      "listing": {
        "id": "lst_123456789",
        "title": "Professional DSLR Camera Kit",
        "image": "https://storage.rentup.com/listings/lst_123456789_1.jpg"
      },
      "lastMessage": {
        "content": "What time would you like to meet for pickup?",
        "sender": "usr_987654321",
        "timestamp": "2025-03-23T16:30:00Z",
        "read": false
      },
      "unreadCount": 1,
      "updatedAt": "2025-03-23T16:30:00Z"
    }
  ],
  "pagination": {
    "total": 3,
    "page": 1,
    "limit": 20,
    "pages": 1
  }
}

GET /conversations/{conversationId}
Get a conversation by ID.

Request Headers:
Authorization: Bearer {token}

Response (200 OK):
{
  "id": "conv_123456789",
  "participants": [
    {
      "id": "usr_123456789",
      "firstName": "John",
      "lastName": "Doe",
      "profileImage": "https://storage.rentup.com/profiles/usr_123456789.jpg"
    },
    {
      "id": "usr_987654321",
      "firstName": "Jane",
      "lastName": "Smith",
      "profileImage": "https://storage.rentup.com/profiles/usr_987654321.jpg"
    }
  ],
  "listing": {
    "id": "lst_123456789",
    "title": "Professional DSLR Camera Kit",
    "image": "https://storage.rentup.com/listings/lst_123456789_1.jpg"
  },
  "booking": {
    "id": "bkg_123456789",
    "status": "approved"
  },
  "createdAt": "2025-03-23T15:30:00Z",
  "updatedAt": "2025-03-23T16:30:00Z"
}

GET /conversations/{conversationId}/messages
Get messages for a conversation.

Request Headers:
Authorization: Bearer {token}

Query Parameters:
- before (string): Get messages before this timestamp
- limit (number): Number of messages to return

Response (200 OK):
{
  "items": [
    {
      "id": "msg_123456789",
      "content": "Hello, I'm interested in renting your camera.",
      "sender": {
        "id": "usr_123456789",
        "firstName": "John",
        "lastName": "D.",
        "profileImage": "https://storage.rentup.com/profiles/usr_123456789.jpg"
      },
      "timestamp": "2025-03-23T15:30:00Z",
      "read": true
    },
    {
      "id": "msg_987654321",
      "content": "Great! When would you like to rent it?",
      "sender": {
        "id": "usr_987654321",
        "firstName": "Jane",
        "lastName": "S.",
        "profileImage": "https://storage.rentup.com/profiles/usr_987654321.jpg"
      },
      "timestamp": "2025-03-23T15:45:00Z",
      "read": true
    },
    {
      "id": "msg_456789123",
      "content": "I'd like to rent it for the weekend of April 15-17.",
      "sender": {
        "id": "usr_123456789",
        "firstName": "John",
        "lastName": "D.",
        "profileImage": "https://storage.rentup.com/profiles/usr_123456789.jpg"
      },
      "timestamp": "2025-03-23T16:00:00Z",
      "read": true
    },
    {
      "id": "msg_789123456",
      "content": "What time would you like to meet for pickup?",
      "sender": {
        "id": "usr_987654321",
        "firstName": "Jane",
        "lastName": "S.",
        "profileImage": "https://storage.rentup.com/profiles/usr_987654321.jpg"
      },
      "timestamp": "2025-03-23T16:30:00Z",
      "read": false
    }
  ],
  "pagination": {
    "hasMore": false
  }
}

POST /conversations/{conversationId}/messages
Send a message in a conversation.

Request Headers:
Authorization: Bearer {token}

Request Body:
{
  "content": "I can meet at 10 AM on April 15 for pickup."
}

Response (201 Created):
{
  "id": "msg_234567891",
  "content": "I can meet at 10 AM on April 15 for pickup.",
  "sender": {
    "id": "usr_123456789",
    "firstName": "John",
    "lastName": "D.",
    "profileImage": "https://storage.rentup.com/profiles/usr_123456789.jpg"
  },
  "timestamp": "2025-03-23T17:00:00Z",
  "read": false
}

POST /conversations
Start a new conversation.

Request Headers:
Authorization: Bearer {token}

Request Body:
{
  "listingId": "lst_123456789",
  "message": "Hello, I'm interested in renting your camera."
}

Response (201 Created):
{
  "id": "conv_123456789",
  "participants": [
    {
      "id": "usr_123456789",
      "firstName": "John",
      "lastName": "D.",
      "profileImage": "https://storage.rentup.com/profiles/usr_123456789.jpg"
    },
    {
      "id": "usr_987654321",
      "firstName": "Jane",
      "lastName": "S.",
      "profileImage": "https://storage.rentup.com/profiles/usr_987654321.jpg"
    }
  ],
  "listing": {
    "id": "lst_123456789",
    "title": "Professional DSLR Camera Kit",
    "image": "https://storage.rentup.com/listings/lst_123456789_1.jpg"
  },
  "messages": [
    {
      "id": "msg_123456789",
      "content": "Hello, I'm interested in renting your camera.",
      "sender": {
        "id": "usr_123456789",
        "firstName": "John",
        "lastName": "D.",
        "profileImage": "https://storage.rentup.com/profiles/usr_123456789.jpg"
      },
      "timestamp": "2025-03-23T15:30:00Z",
      "read": false
    }
  ],
  "createdAt": "2025-03-23T15:30:00Z",
  "updatedAt": "2025-03-23T15:30:00Z"
}

### Reviews

POST /reviews
Create a review.

Request Headers:
Authorization: Bearer {token}

Request Body:
{
  "bookingId": "bkg_123456789",
  "rating": 5,
  "comment": "Excellent camera, worked perfectly for my photoshoot!"
}

Response (201 Created):
{
  "id": "rev_123456789",
  "booking": {
    "id": "bkg_123456789",
    "listing": {
      "id": "lst_123456789",
      "title": "Professional DSLR Camera Kit"
    }
  },
  "reviewer": {
    "id": "usr_123456789",
    "firstName": "John",
    "lastName": "D.",
    "profileImage": "https://storage.rentup.com/profiles/usr_123456789.jpg"
  },
  "reviewee": {
    "id": "usr_987654321",
    "firstName": "Jane",
    "lastName": "S.",
    "profileImage": "https://storage.rentup.com/profiles/usr_987654321.jpg"
  },
  "rating": 5,
  "comment": "Excellent camera, worked perfectly for my photoshoot!",
  "createdAt": "2025-04-18T12:00:00Z"
}

GET /reviews
Get reviews for the current user.

Request Headers:
Authorization: Bearer {token}

Query Parameters:
- role (string): Filter by role (reviewer, reviewee)
- page (number): Page number
- limit (number): Items per page

Response (200 OK):
{
  "items": [
    {
      "id": "rev_987654321",
      "booking": {
        "id": "bkg_987654321",
        "listing": {
          "id": "lst_987654321",
          "title": "Mountain Bike"
        }
      },
      "reviewer": {
        "id": "usr_345678912",
        "firstName": "Alice",
        "lastName": "B.",
        "profileImage": "https://storage.rentup.com/profiles/usr_345678912.jpg"
      },
      "reviewee": {
        "id": "usr_123456789",
        "firstName": "John",
        "lastName": "D.",
        "profileImage": "https://storage.rentup.com/profiles/usr_123456789.jpg"
      },
      "rating": 4,
      "comment": "Great renter, returned the bike in good condition.",
      "createdAt": "2025-03-15T12:00:00Z"
    }
  ],
  "pagination": {
    "total": 5,
    "page": 1,
    "limit": 20,
    "pages": 1
  }
}

GET /users/{userId}/reviews
Get reviews for a user.

Query Parameters:
- role (string): Filter by role (reviewer, reviewee)
- page (number): Page number
- limit (number): Items per page

Response (200 OK):
{
  "items": [
    {
      "id": "rev_987654321",
      "booking": {
        "id": "bkg_987654321",
        "listing": {
          "id": "lst_987654321",
          "title": "Mountain Bike"
        }
      },
      "reviewer": {
        "id": "usr_345678912",
        "firstName": "Alice",
        "lastName": "B.",
        "profileImage": "https://storage.rentup.com/profiles/usr_345678912.jpg"
      },
      "reviewee": {
        "id": "usr_123456789",
        "firstName": "John",
        "lastName": "D.",
        "profileImage": "https://storage.rentup.com/profiles/usr_123456789.jpg"
      },
      "rating": 4,
      "comment": "Great renter, returned the bike in good condition.",
      "createdAt": "2025-03-15T12:00:00Z"
    }
  ],
  "pagination": {
    "total": 12,
    "page": 1,
    "limit": 20,
    "pages": 1
  }
}

### Categories

GET /categories
Get all categories.

Response (200 OK):
{
  "items": [
    {
      "id": "cat_photography",
      "name": "Photography",
      "icon": "camera",
      "subcategories": [
        {
          "id": "cat_cameras",
          "name": "Cameras",
          "icon": "camera"
        },
        {
          "id": "cat_lenses",
          "name": "Lenses",
          "icon": "lens"
        },
        {
          "id": "cat_lighting",
          "name": "Lighting",
          "icon": "flash"
        }
      ]
    },
    {
      "id": "cat_outdoor",
      "name": "Outdoor",
      "icon": "tree",
      "subcategories": [
        {
          "id": "cat_camping",
          "name": "Camping",
          "icon": "tent"
        },
        {
          "id": "cat_bikes",
          "name": "Bikes",
          "icon": "bike"
        },
        {
          "id": "cat_watersports",
          "name": "Water Sports",
          "icon": "waves"
        }
      ]
    }
  ]
}

### Payments

POST /payments/methods
Add a payment method.

Request Headers:
Authorization: Bearer {token}

Request Body:
{
  "type": "card",
  "cardNumber": "****************",
  "expiryMonth": 12,
  "expiryYear": 2025,
  "cvc": "123",
  "name": "John Doe",
  "billingAddress": {
    "line1": "123 Main St",
    "line2": "Apt 4B",
    "city": "San Francisco",
    "state": "CA",
    "zipCode": "94105",
    "country": "USA"
  }
}

Response (201 Created):
{
  "id": "pm_123456789",
  "type": "card",
  "brand": "Visa",
  "last4": "4242",
  "expiryMonth": 12,
  "expiryYear": 2025,
  "name": "John Doe",
  "isDefault": true,
  "createdAt": "2025-03-23T18:00:00Z"
}

GET /payments/methods
Get all payment methods for the current user.

Request Headers:
Authorization: Bearer {token}

Response (200 OK):
{
  "items": [
    {
      "id": "pm_123456789",
      "type": "card",
      "brand": "Visa",
      "last4": "4242",
      "expiryMonth": 12,
      "expiryYear": 2025,
      "name": "John Doe",
      "isDefault": true,
      "createdAt": "2025-03-23T18:00:00Z"
    }
  ]
}

DELETE /payments/methods/{paymentMethodId}
Delete a payment method.

Request Headers:
Authorization: Bearer {token}

Response (204 No Content)

PUT /payments/methods/{paymentMethodId}/default
Set a payment method as default.

Request Headers:
Authorization: Bearer {token}

Response (200 OK):
{
  "id": "pm_123456789",
  "isDefault": true,
  "updatedAt": "2025-03-23T18:30:00Z"
}

POST /payments/payout-methods
Add a payout method.

Request Headers:
Authorization: Bearer {token}

Request Body:
{
  "type": "bank_account",
  "accountNumber": "************",
  "routingNumber": "*********",
  "accountHolderName": "John Doe",
  "accountType": "checking",
  "billingAddress": {
    "line1": "123 Main St",
    "line2": "Apt 4B",
    "city": "San Francisco",
    "state": "CA",
    "zipCode": "94105",
    "country": "USA"
  }
}

Response (201 Created):
{
  "id": "pm_987654321",
  "type": "bank_account",
  "last4": "6789",
  "accountHolderName": "John Doe",
  "accountType": "checking",
  "isDefault": true,
  "createdAt": "2025-03-23T19:00:00Z"
}

GET /payments/payout-methods
Get all payout methods for the current user.

Request Headers:
Authorization: Bearer {token}

Response (200 OK):
{
  "items": [
    {
      "id": "pm_987654321",
      "type": "bank_account",
      "last4": "6789",
      "accountHolderName": "John Doe",
      "accountType": "checking",
      "isDefault": true,
      "createdAt": "2025-03-23T19:00:00Z"
    }
  ]
}

DELETE /payments/payout-methods/{payoutMethodId}
Delete a payout method.

Request Headers:
Authorization: Bearer {token}

Response (204 No Content)

PUT /payments/payout-methods/{payoutMethodId}/default
Set a payout method as default.

Request Headers:
Authorization: Bearer {token}

Response (200 OK):
{
  "id": "pm_987654321",
  "isDefault": true,
  "updatedAt": "2025-03-23T19:30:00Z"
}

### Notifications

GET /notifications
Get all notifications for the current user.

Request Headers:
Authorization: Bearer {token}

Query Parameters:
- read (boolean): Filter by read status
- page (number): Page number
- limit (number): Items per page

Response (200 OK):
{
  "items": [
    {
      "id": "not_123456789",
      "type": "booking_request",
      "title": "New Booking Request",
      "message": "John D. wants to rent your Professional DSLR Camera Kit",
      "data": {
        "bookingId": "bkg_123456789",
        "listingId": "lst_123456789"
      },
      "read": false,
      "createdAt": "2025-03-23T15:00:00Z"
    },
    {
      "id": "not_987654321",
      "type": "message",
      "title": "New Message",
      "message": "Jane S. sent you a message about Professional DSLR Camera Kit",
      "data": {
        "conversationId": "conv_123456789",
        "messageId": "msg_987654321"
      },
      "read": true,
      "createdAt": "2025-03-23T15:45:00Z"
    }
  ],
  "pagination": {
    "total": 8,
    "page": 1,
    "limit": 20,
    "pages": 1
  }
}

PUT /notifications/{notificationId}/read
Mark a notification as read.

Request Headers:
Authorization: Bearer {token}

Response (200 OK):
{
  "id": "not_123456789",
  "read": true,
  "updatedAt": "2025-03-23T20:00:00Z"
}

PUT /notifications/read-all
Mark all notifications as read.

Request Headers:
Authorization: Bearer {token}

Response (200 OK):
{
  "success": true,
  "count": 3,
  "updatedAt": "2025-03-23T20:15:00Z"
}

### Reports

POST /reports
Create a report.

Request Headers:
Authorization: Bearer {token}

Request Body:
{
  "type": "user",
  "userId": "usr_987654321",
  "reason": "inappropriate_behavior",
  "details": "User was rude and threatening during our conversation."
}

Response (201 Created):
{
  "id": "rep_123456789",
  "type": "user",
  "status": "pending",
  "createdAt": "2025-03-23T21:00:00Z"
}

### Webhooks

POST /webhooks
Register a webhook.

Request Headers:
Authorization: Bearer {token}

Request Body:
{
  "url": "https://example.com/webhook",
  "events": [
    "booking.created",
    "booking.approved",
    "booking.rejected",
    "booking.canceled",
    "booking.completed",
    "message.created"
  ],
  "secret": "whsec_123456789"
}

Response (201 Created):
{
  "id": "wh_123456789",
  "url": "https://example.com/webhook",
  "events": [
    "booking.created",
    "booking.approved",
    "booking.rejected",
    "booking.canceled",
    "booking.completed",
    "message.created"
  ],
  "active": true,
  "createdAt": "2025-03-23T22:00:00Z"
}

GET /webhooks
Get all webhooks.

Request Headers:
Authorization: Bearer {token}

Response (200 OK):
{
  "items": [
    {
      "id": "wh_123456789",
      "url": "https://example.com/webhook",
      "events": [
        "booking.created",
        "booking.approved",
        "booking.rejected",
        "booking.canceled",
        "booking.completed",
        "message.created"
      ],
      "active": true,
      "createdAt": "2025-03-23T22:00:00Z"
    }
  ]
}

DELETE /webhooks/{webhookId}
Delete a webhook.

Request Headers:
Authorization: Bearer {token}

Response (204 No Content)

### Auctions

GET /auctions
Get all auctions.

Request Headers:
Authorization: Bearer {token}

Query Parameters:
- status (string): Filter by status (active, completed, cancelled)
- category (string): Filter by category ID
- page (number): Page number
- limit (number): Items per page

Response (200 OK):
{
  "items": [
    {
      "id": "auc_123456789",
      "title": "Professional DSLR Camera",
      "description": "Canon EOS 5D Mark IV with 24-70mm lens",
      "item": {
        "id": "itm_123456789",
        "title": "Professional DSLR Camera",
        "image": "https://storage.rentup.com/items/itm_123456789_1.jpg"
      },
      "seller": {
        "id": "usr_987654321",
        "firstName": "Jane",
        "lastName": "S.",
        "profileImage": "https://storage.rentup.com/profiles/usr_987654321.jpg",
        "trustScore": 4.9
      },
      "startingPrice": 50.00,
      "currentPrice": 75.00,
      "currency": "USD",
      "bidCount": 5,
      "startTime": "2025-03-20T12:00:00Z",
      "endTime": "2025-03-27T12:00:00Z",
      "status": "active"
    }
  ],
  "pagination": {
    "total": 12,
    "page": 1,
    "limit": 20,
    "pages": 1
  }
}

POST /auctions
Create a new auction.

Request Headers:
Authorization: Bearer {token}

Request Body:
{
  "itemId": "itm_123456789",
  "title": "Professional DSLR Camera",
  "description": "Canon EOS 5D Mark IV with 24-70mm lens",
  "startingPrice": 50.00,
  "reservePrice": 100.00,
  "currency": "USD",
  "startTime": "2025-03-20T12:00:00Z",
  "endTime": "2025-03-27T12:00:00Z",
  "incrementAmount": 5.00,
  "autoExtend": true,
  "autoExtendMinutes": 5
}

Response (201 Created):
{
  "id": "auc_123456789",
  "title": "Professional DSLR Camera",
  "description": "Canon EOS 5D Mark IV with 24-70mm lens",
  "item": {
    "id": "itm_123456789",
    "title": "Professional DSLR Camera",
    "image": "https://storage.rentup.com/items/itm_123456789_1.jpg"
  },
  "seller": {
    "id": "usr_987654321",
    "firstName": "Jane",
    "lastName": "S.",
    "profileImage": "https://storage.rentup.com/profiles/usr_987654321.jpg"
  },
  "startingPrice": 50.00,
  "reservePrice": 100.00,
  "currentPrice": 50.00,
  "currency": "USD",
  "bidCount": 0,
  "startTime": "2025-03-20T12:00:00Z",
  "endTime": "2025-03-27T12:00:00Z",
  "incrementAmount": 5.00,
  "autoExtend": true,
  "autoExtendMinutes": 5,
  "status": "scheduled",
  "createdAt": "2025-03-15T12:00:00Z"
}

GET /auctions/{auctionId}
Get an auction by ID.

Request Headers:
Authorization: Bearer {token}

Response (200 OK):
{
  "id": "auc_123456789",
  "title": "Professional DSLR Camera",
  "description": "Canon EOS 5D Mark IV with 24-70mm lens",
  "item": {
    "id": "itm_123456789",
    "title": "Professional DSLR Camera",
    "images": [
      "https://storage.rentup.com/items/itm_123456789_1.jpg",
      "https://storage.rentup.com/items/itm_123456789_2.jpg"
    ],
    "category": {
      "id": "cat_photography",
      "name": "Photography"
    },
    "specifications": {
      "brand": "Canon",
      "model": "EOS 5D Mark IV",
      "condition": "Excellent"
    }
  },
  "seller": {
    "id": "usr_987654321",
    "firstName": "Jane",
    "lastName": "Smith",
    "profileImage": "https://storage.rentup.com/profiles/usr_987654321.jpg",
    "trustScore": 4.9
  },
  "startingPrice": 50.00,
  "reservePrice": 100.00,
  "currentPrice": 75.00,
  "currency": "USD",
  "bidCount": 5,
  "bids": [
    {
      "id": "bid_123456789",
      "amount": 75.00,
      "bidder": {
        "id": "usr_123456789",
        "firstName": "John",
        "lastName": "D."
      },
      "timestamp": "2025-03-21T15:30:00Z"
    },
    {
      "id": "bid_987654321",
      "amount": 70.00,
      "bidder": {
        "id": "usr_345678912",
        "firstName": "Alice",
        "lastName": "B."
      },
      "timestamp": "2025-03-21T14:45:00Z"
    }
  ],
  "startTime": "2025-03-20T12:00:00Z",
  "endTime": "2025-03-27T12:00:00Z",
  "incrementAmount": 5.00,
  "autoExtend": true,
  "autoExtendMinutes": 5,
  "status": "active",
  "createdAt": "2025-03-15T12:00:00Z",
  "updatedAt": "2025-03-21T15:30:00Z"
}

POST /auctions/{auctionId}/bids
Place a bid on an auction.

Request Headers:
Authorization: Bearer {token}

Request Body:
{
  "amount": 80.00
}

Response (201 Created):
{
  "id": "bid_234567891",
  "amount": 80.00,
  "auction": {
    "id": "auc_123456789",
    "title": "Professional DSLR Camera",
    "currentPrice": 80.00,
    "endTime": "2025-03-27T12:00:00Z"
  },
  "bidder": {
    "id": "usr_123456789",
    "firstName": "John",
    "lastName": "D."
  },
  "status": "accepted",
  "timestamp": "2025-03-22T10:15:00Z"
}

GET /auctions/{auctionId}/bids
Get all bids for an auction.

Request Headers:
Authorization: Bearer {token}

Response (200 OK):
{
  "items": [
    {
      "id": "bid_234567891",
      "amount": 80.00,
      "bidder": {
        "id": "usr_123456789",
        "firstName": "John",
        "lastName": "D.",
        "profileImage": "https://storage.rentup.com/profiles/usr_123456789.jpg"
      },
      "status": "accepted",
      "timestamp": "2025-03-22T10:15:00Z"
    },
    {
      "id": "bid_123456789",
      "amount": 75.00,
      "bidder": {
        "id": "usr_123456789",
        "firstName": "John",
        "lastName": "D.",
        "profileImage": "https://storage.rentup.com/profiles/usr_123456789.jpg"
      },
      "status": "accepted",
      "timestamp": "2025-03-21T15:30:00Z"
    }
  ],
  "pagination": {
    "total": 6,
    "page": 1,
    "limit": 20,
    "pages": 1
  }
}

PUT /auctions/{auctionId}/cancel
Cancel an auction.

Request Headers:
Authorization: Bearer {token}

Request Body:
{
  "reason": "Item no longer available"
}

Response (200 OK):
{
  "id": "auc_123456789",
  "status": "cancelled",
  "cancelReason": "Item no longer available",
  "updatedAt": "2025-03-22T11:00:00Z"
}

### Agreements

GET /agreements
Get all agreements for the current user.

Request Headers:
Authorization: Bearer {token}

Query Parameters:
- status (string): Filter by status (draft, pending, signed, expired, cancelled)
- role (string): Filter by role (owner, renter)
- page (number): Page number
- limit (number): Items per page

Response (200 OK):
{
  "items": [
    {
      "id": "agr_123456789",
      "title": "Rental Agreement - Professional DSLR Camera",
      "owner": {
        "id": "usr_987654321",
        "firstName": "Jane",
        "lastName": "Smith",
        "profileImage": "https://storage.rentup.com/profiles/usr_987654321.jpg"
      },
      "renter": {
        "id": "usr_123456789",
        "firstName": "John",
        "lastName": "Doe",
        "profileImage": "https://storage.rentup.com/profiles/usr_123456789.jpg"
      },
      "item": {
        "id": "itm_123456789",
        "title": "Professional DSLR Camera",
        "image": "https://storage.rentup.com/items/itm_123456789_1.jpg"
      },
      "booking": {
        "id": "bkg_123456789",
        "startDate": "2025-04-15T10:00:00Z",
        "endDate": "2025-04-17T18:00:00Z"
      },
      "status": "signed",
      "documentUrl": "https://storage.rentup.com/agreements/agr_123456789.pdf",
      "createdAt": "2025-03-23T12:00:00Z",
      "updatedAt": "2025-03-24T15:30:00Z"
    }
  ],
  "pagination": {
    "total": 3,
    "page": 1,
    "limit": 20,
    "pages": 1
  }
}

GET /agreements/{agreementId}
Get an agreement by ID.

Request Headers:
Authorization: Bearer {token}

Response (200 OK):
{
  "id": "agr_123456789",
  "title": "Rental Agreement - Professional DSLR Camera",
  "owner": {
    "id": "usr_987654321",
    "firstName": "Jane",
    "lastName": "Smith",
    "profileImage": "https://storage.rentup.com/profiles/usr_987654321.jpg",
    "email": "<EMAIL>",
    "phone": "+**********"
  },
  "renter": {
    "id": "usr_123456789",
    "firstName": "John",
    "lastName": "Doe",
    "profileImage": "https://storage.rentup.com/profiles/usr_123456789.jpg",
    "email": "<EMAIL>",
    "phone": "+1987654321"
  },
  "item": {
    "id": "itm_123456789",
    "title": "Professional DSLR Camera",
    "description": "Canon EOS 5D Mark IV with 24-70mm lens",
    "images": [
      "https://storage.rentup.com/items/itm_123456789_1.jpg"
    ],
    "specifications": {
      "brand": "Canon",
      "model": "EOS 5D Mark IV",
      "condition": "Excellent",
      "serialNumber": "12345-67890"
    }
  },
  "booking": {
    "id": "bkg_123456789",
    "startDate": "2025-04-15T10:00:00Z",
    "endDate": "2025-04-17T18:00:00Z",
    "totalPrice": 120.00,
    "securityDeposit": 500.00,
    "currency": "USD"
  },
  "terms": {
    "pickupLocation": "123 Main St, San Francisco, CA 94105",
    "returnLocation": "123 Main St, San Francisco, CA 94105",
    "lateReturnFee": 25.00,
    "damagePolicy": "Renter is responsible for any damage beyond normal wear and tear",
    "cancellationPolicy": "Full refund if cancelled 48 hours before rental start"
  },
  "signatures": {
    "owner": {
      "signed": true,
      "timestamp": "2025-03-24T14:30:00Z",
      "ipAddress": "***********"
    },
    "renter": {
      "signed": true,
      "timestamp": "2025-03-24T15:30:00Z",
      "ipAddress": "***********"
    }
  },
  "status": "signed",
  "documentUrl": "https://storage.rentup.com/agreements/agr_123456789.pdf",
  "createdAt": "2025-03-23T12:00:00Z",
  "updatedAt": "2025-03-24T15:30:00Z"
}

POST /agreements
Create a new agreement.

Request Headers:
Authorization: Bearer {token}

Request Body:
{
  "bookingId": "bkg_123456789",
  "title": "Rental Agreement - Professional DSLR Camera",
  "terms": {
    "pickupLocation": "123 Main St, San Francisco, CA 94105",
    "returnLocation": "123 Main St, San Francisco, CA 94105",
    "lateReturnFee": 25.00,
    "damagePolicy": "Renter is responsible for any damage beyond normal wear and tear",
    "cancellationPolicy": "Full refund if cancelled 48 hours before rental start"
  }
}

Response (201 Created):
{
  "id": "agr_123456789",
  "title": "Rental Agreement - Professional DSLR Camera",
  "owner": {
    "id": "usr_987654321",
    "firstName": "Jane",
    "lastName": "Smith"
  },
  "renter": {
    "id": "usr_123456789",
    "firstName": "John",
    "lastName": "Doe"
  },
  "item": {
    "id": "itm_123456789",
    "title": "Professional DSLR Camera"
  },
  "booking": {
    "id": "bkg_123456789",
    "startDate": "2025-04-15T10:00:00Z",
    "endDate": "2025-04-17T18:00:00Z"
  },
  "terms": {
    "pickupLocation": "123 Main St, San Francisco, CA 94105",
    "returnLocation": "123 Main St, San Francisco, CA 94105",
    "lateReturnFee": 25.00,
    "damagePolicy": "Renter is responsible for any damage beyond normal wear and tear",
    "cancellationPolicy": "Full refund if cancelled 48 hours before rental start"
  },
  "status": "draft",
  "createdAt": "2025-03-23T12:00:00Z"
}

POST /agreements/{agreementId}/sign
Sign an agreement.

Request Headers:
Authorization: Bearer {token}

Response (200 OK):
{
  "id": "agr_123456789",
  "status": "signed",
  "signatures": {
    "renter": {
      "signed": true,
      "timestamp": "2025-03-24T15:30:00Z",
      "ipAddress": "***********"
    }
  },
  "documentUrl": "https://storage.rentup.com/agreements/agr_123456789.pdf",
  "updatedAt": "2025-03-24T15:30:00Z"
}

PUT /agreements/{agreementId}/cancel
Cancel an agreement.

Request Headers:
Authorization: Bearer {token}

Request Body:
{
  "reason": "Item no longer available"
}

Response (200 OK):
{
  "id": "agr_123456789",
  "status": "cancelled",
  "cancelReason": "Item no longer available",
  "updatedAt": "2025-03-24T16:00:00Z"
}

### Fraud Prevention

GET /fraud-prevention/risk-score
Get the risk score for a user or transaction.

Request Headers:
Authorization: Bearer {token}

Query Parameters:
- userId (string): User ID to check
- transactionId (string): Transaction ID to check

Response (200 OK):
{
  "riskScore": 15,
  "riskLevel": "low",
  "factors": [
    {
      "type": "user_verification",
      "score": 5,
      "description": "User has verified email and phone"
    },
    {
      "type": "transaction_history",
      "score": 10,
      "description": "User has 5+ successful transactions"
    }
  ],
  "timestamp": "2025-03-24T17:00:00Z"
}

POST /fraud-prevention/reports
Report fraudulent activity.

Request Headers:
Authorization: Bearer {token}

Request Body:
{
  "type": "user",
  "userId": "usr_987654321",
  "description": "User provided fake ID during verification",
  "evidence": {
    "transactionIds": ["txn_123456789"],
    "messageIds": ["msg_123456789"]
  }
}

Response (201 Created):
{
  "id": "frp_123456789",
  "status": "pending",
  "createdAt": "2025-03-24T18:00:00Z"
}

GET /fraud-prevention/reports
Get fraud reports submitted by the current user.

Request Headers:
Authorization: Bearer {token}

Query Parameters:
- status (string): Filter by status (pending, resolved, dismissed)
- page (number): Page number
- limit (number): Items per page

Response (200 OK):
{
  "items": [
    {
      "id": "frp_123456789",
      "type": "user",
      "targetUser": {
        "id": "usr_987654321",
        "firstName": "Jane",
        "lastName": "S."
      },
      "description": "User provided fake ID during verification",
      "status": "pending",
      "createdAt": "2025-03-24T18:00:00Z"
    }
  ],
  "pagination": {
    "total": 1,
    "page": 1,
    "limit": 20,
    "pages": 1
  }
}

### Analytics

GET /analytics/listings
Get analytics for listings.

Request Headers:
Authorization: Bearer {token}

Query Parameters:
- startDate (string): Start date (ISO 8601)
- endDate (string): End date (ISO 8601)
- listingId (string): Filter by listing ID

Response (200 OK):
{
  "views": {
    "total": 1250,
    "byDay": [
      {
        "date": "2025-03-01",
        "count": 42
      },
      {
        "date": "2025-03-02",
        "count": 38
      }
    ]
  },
  "bookings": {
    "total": 15,
    "byDay": [
      {
        "date": "2025-03-01",
        "count": 1
      },
      {
        "date": "2025-03-02",
        "count": 0
      }
    ]
  },
  "revenue": {
    "total": 1200.00,
    "byDay": [
      {
        "date": "2025-03-01",
        "amount": 60.00
      },
      {
        "date": "2025-03-02",
        "amount": 0.00
      }
    ],
    "currency": "USD"
  },
  "conversionRate": 1.2
}

GET /analytics/earnings
Get earnings analytics.

Request Headers:
Authorization: Bearer {token}

Query Parameters:
- startDate (string): Start date (ISO 8601)
- endDate (string): End date (ISO 8601)

Response (200 OK):
{
  "earnings": {
    "total": 1140.00,
    "byMonth": [
      {
        "month": "2025-01",
        "amount": 350.00
      },
      {
        "month": "2025-02",
        "amount": 420.00
      },
      {
        "month": "2025-03",
        "amount": 370.00
      }
    ],
    "currency": "USD"
  },
  "fees": {
    "total": 60.00,
    "byMonth": [
      {
        "month": "2025-01",
        "amount": 17.50
      },
      {
        "month": "2025-02",
        "amount": 21.00
      },
      {
        "month": "2025-03",
        "amount": 18.50
      }
    ],
    "currency": "USD"
  },
  "bookings": {
    "total": 15,
    "byMonth": [
      {
        "month": "2025-01",
        "count": 5
      },
      {
        "month": "2025-02",
        "count": 6
      },
      {
        "month": "2025-03",
        "count": 4
      }
    ]
  }
}


## Appendix

### Error Codes

| Code | Description |
|------|-------------|
| `authentication_error` | Authentication failed |
| `authorization_error` | Insufficient permissions |
| `invalid_request` | Invalid request parameters |
| `resource_not_found` | Requested resource not found |
| `validation_error` | Validation failed |
| `rate_limit_exceeded` | Rate limit exceeded |
| `server_error` | Internal server error |
| `service_unavailable` | Service temporarily unavailable |

### Webhook Events

| Event | Description |
|-------|-------------|
| `user.created` | A new user has been created |
| `listing.created` | A new listing has been created |
| `listing.updated` | A listing has been updated |
| `listing.deleted` | A listing has been deleted |
| `booking.created` | A new booking request has been created |
| `booking.approved` | A booking request has been approved |
| `booking.rejected` | A booking request has been rejected |
| `booking.canceled` | A booking has been canceled |
| `booking.completed` | A booking has been completed |
| `message.created` | A new message has been sent |
| `review.created` | A new review has been created |
| `payment.succeeded` | A payment has succeeded |
| `payment.failed` | A payment has failed |
| `payout.initiated` | A payout has been initiated |
| `payout.completed` | A payout has been completed |
| `payout.failed` | A payout has failed |
| `auction.created` | A new auction has been created |
| `auction.started` | An auction has started |
| `auction.bid_placed` | A bid has been placed on an auction |
| `auction.ended` | An auction has ended |
| `auction.cancelled` | An auction has been cancelled |
| `agreement.created` | A new agreement has been created |
| `agreement.signed` | An agreement has been signed |
| `agreement.cancelled` | An agreement has been cancelled |
| `fraud.report_submitted` | A fraud report has been submitted |
| `fraud.high_risk_detected` | A high-risk transaction or user has been detected |

### Pagination

Most endpoints that return lists of items support pagination. The pagination parameters are:

- `page`: Page number (starts from 1)
- `limit`: Number of items per page (default: 20, max: 100)

The response includes a `pagination` object with the following fields:

- `total`: Total number of items
- `page`: Current page number
- `limit`: Number of items per page
- `pages`: Total number of pages

### Rate Limiting

The API implements rate limiting to prevent abuse. The rate limits are:

- Standard rate limit: 100 requests per minute
- Enhanced rate limit (for partners): 300 requests per minute

Rate limit headers are included in all responses:

- `X-RateLimit-Limit`: Maximum requests per minute
- `X-RateLimit-Remaining`: Remaining requests in the current window
- `X-RateLimit-Reset`: Time when the rate limit resets (Unix timestamp)

If you exceed the rate limit, you will receive a `429 Too Many Requests` response.

### Versioning

The API is versioned using URL path versioning. The current version is `v1`.

When a new version is released, the previous version will be supported for at least 6 months.

### Support

If you have any questions or need help with the API, please contact our support <NAME_EMAIL>.