# RentUp Wireframe to Task Mapping

Maps wireframes to development tasks for easy reference when implementing features.

> **Reference:** Tasks are numbered according to [detailed-task-breakdown.md](./detailed-task-breakdown.md)

## Authentication

| Wireframe | Tasks | Description |
|-----------|-------|-------------|
| [login.md](../wireframes/login.md) | 1.1-1.5 | Login interface |
| [registration.md](../wireframes/registration.md) | 1.1-1.5 | Registration |
| [advanced-login.md](../wireframes/advanced-login.md) | 1.3 | Social login |
| [email-verification.md](../wireframes/email-verification.md) | 1.4 | Email verification |
| [password-recovery.md](../wireframes/password-recovery.md) | 1.4 | Password recovery |
| [two-factor-auth.md](../wireframes/two-factor-auth.md) | 1.1-1.5 | 2FA |
| [mobile-verification.md](../wireframes/mobile-verification.md) | 2.1-2.4 | Mobile verification |

## Listing Management

| Wireframe | Tasks | Description |
|-----------|-------|-------------|
| [item-listing.md](../wireframes/item-listing.md) | 3.1-3.6 | Listing creation/edit |
| [categories-browse.md](../wireframes/categories-browse.md) | 3.3, 5.1-5.5 | Category browsing |
| [search-results.md](../wireframes/search-results.md) | 5.1-5.5 | Search results |
| [item-detail.md](../wireframes/item-detail.md) | 3.3 | Item detail view |
| [ai-listing-creator.md](../wireframes/ai-listing-creator.md) | 18.2 | AI-assisted listing |
| [listing-analytics.md](../wireframes/listing-analytics.md) | 3.1-3.6 | Performance analytics |

## Booking and Payment

| Wireframe | Tasks | Description |
|-----------|-------|-------------|
| [booking-process.md](../wireframes/booking-process.md) | 6.1-6.6 | Booking flow |
| [payment-methods.md](../wireframes/payment-methods.md) | 7.1-7.5 | Payment methods |
| [payment-processing.md](../wireframes/payment-processing.md) | 7.1-7.5 | Payment processing |
| [payment-receipts.md](../wireframes/payment-receipts.md) | 7.4 | Receipts |
| [checkout-delivery-options.md](../wireframes/checkout-delivery-options.md) | 6.1-6.6 | Delivery options |
| [checkout-pickup-options.md](../wireframes/checkout-pickup-options.md) | 6.1-6.6 | Pickup options |

## Communication

| Wireframe | Tasks | Description |
|-----------|-------|-------------|
| [messaging.md](../wireframes/messaging.md) | 8.1-8.5 | Messaging interface |
| [notifications-center.md](../wireframes/notifications-center.md) | 8.4 | Notifications |
| [review-form.md](../wireframes/review-form.md) | 9.1-9.5 | Review submission |
| [reviews-ratings.md](../wireframes/reviews-ratings.md) | 9.1-9.5 | Reviews display |
| [unified-communications.md](../wireframes/unified-communications.md) | 8.1-8.5 | Unified communications |

## Auction & Agreement Systems

| Wireframe | Tasks | Description |
|-----------|-------|-------------|
| [auction-management.md](../wireframes/auction-management.md) | 10.1-10.6 | Auction management |
| [dispute-resolution.md](../wireframes/dispute-resolution.md) | 11.1-11.6 | Dispute resolution |

## User Account

| Wireframe | Tasks | Description |
|-----------|-------|-------------|
| [user-dashboard.md](../wireframes/user-dashboard.md) | Multiple | Dashboard overview |
| [user-profile.md](../wireframes/user-profile.md) | Multiple | Profile management |
| [account-settings.md](../wireframes/account-settings.md) | Multiple | Account settings |
| [transaction-history.md](../wireframes/transaction-history.md) | Multiple | Transaction history |
| [saved-items.md](../wireframes/saved-items.md) | Multiple | Saved items |
| [activity-logs.md](../wireframes/activity-logs.md) | Multiple | Activity logs |

## Business & Mobile

| Wireframe | Tasks | Description |
|-----------|-------|-------------|
| [inventory-management.md](../wireframes/inventory-management.md) | 23.1-23.3 | Inventory management |
| [financial-dashboard.md](../wireframes/financial-dashboard.md) | 23.1-23.3 | Financial dashboard |
| [analytics-dashboard.md](../wireframes/analytics-dashboard.md) | 23.1-23.3 | Analytics dashboard |
| [user-roles-permissions.md](../wireframes/user-roles-permissions.md) | 23.1-23.3 | User roles |
| [mobile-app.md](../wireframes/mobile-app.md) | 24.1-24.5 | Mobile app design |

## Internationalization & Additional Features

| Wireframe | Tasks | Description |
|-----------|-------|-------------|
| [localization-settings.md](../wireframes/localization-settings.md) | 25.1-25.3 | Localization |
| [currency-settings.md](../wireframes/currency-settings.md) | 25.1-25.3 | Currency settings |
| [sustainability-tracking.md](../wireframes/sustainability-tracking.md) | Future | Sustainability |
| [gamification-features.md](../wireframes/gamification-features.md) | Future | Gamification |
| [referral-program.md](../wireframes/referral-program.md) | Future | Referral program |
| [trust-safety.md](../wireframes/trust-safety.md) | 22.1-22.4 | Trust and safety |

## Static Pages

| Wireframe | Tasks | Description |
|-----------|-------|-------------|
| [about-us.md](../wireframes/about-us.md) | ✓ | About us |
| [contact-us.md](../wireframes/contact-us.md) | ✓ | Contact us |
| [help-center.md](../wireframes/help-center.md) | ✓ | Help center |
| [terms-of-service.md](../wireframes/terms-of-service.md) | ✓ | Terms of service |
| [privacy-policy.md](../wireframes/privacy-policy.md) | ✓ | Privacy policy |
| [community-guidelines.md](../wireframes/community-guidelines.md) | ✓ | Community guidelines |

---

Last Updated: 2025-06-15
