# Financial Services API

## Endpoints

### Transactions
```typescript
// Get user transactions
GET /api/v1/financial/transactions
Query params: {
  startDate?: ISO8601
  endDate?: ISO8601
  status?: 'pending' | 'completed' | 'failed'
  type?: 'rental' | 'deposit' | 'fee' | 'refund'
}

// Get transaction details
GET /api/v1/financial/transactions/:id

// Create payment
POST /api/v1/financial/transactions
Body: {
  amount: number
  currency: string
  type: string
  description: string
  paymentMethod: string
}

// Process refund
POST /api/v1/financial/transactions/:id/refund
```

### Documents
```typescript
// Get financial documents
GET /api/v1/financial/documents
Query params: {
  type: 'invoice' | 'receipt' | 'tax'
  year?: number
  month?: number
}

// Generate document
POST /api/v1/financial/documents
Body: {
  type: string
  transactionId: string
  format: 'pdf' | 'csv'
}
```

### Analytics
```typescript
// Get financial metrics
GET /api/v1/financial/metrics
Query params: {
  period: 'day' | 'week' | 'month' | 'year'
  metrics: string[]
}
```