# Frontend Integration Testing Guide

**Updated: May 2025**  
**MSW Version: 2.x**  
**React Version: 19**  
**Testing Framework: Jest + <PERSON>wright**

## Overview

This guide covers the comprehensive integration testing setup for the RentUp frontend, including API mocking with MSW v2.x, full user journey testing, and integration with backend services.

## 🎯 **What Integration Tests Cover**

### ✅ **Implemented Tests**
- **Authentication Flow**: Registration, login, logout, token refresh
- **Search Integration**: Search functionality with API integration
- **Recommendations**: AI recommendation system with visualization
- **Full User Journeys**: Complete workflows from registration to booking
- **Error Handling**: Network errors, API failures, validation errors
- **Loading States**: Async operations and user feedback
- **Accessibility**: WCAG 2.1 AA compliance across flows
- **Responsive Design**: Mobile and desktop integration

### 🔄 **Backend Integration Tests (Requires Backend)**
- **Real API Integration**: Tests with actual backend running
- **Database Integration**: End-to-end data persistence
- **WebSocket Integration**: Real-time features (auctions, notifications)
- **File Upload Integration**: Image and document uploads
- **Payment Integration**: Stripe payment flows

## 🛠 **Setup and Configuration**

### Prerequisites

```bash
# Install dependencies (MSW v2.x included)
npm install

# Verify MSW installation
npm list msw
```

### MSW (Mock Service Worker) Setup

Our setup uses MSW v2.x with the latest API patterns:

```typescript
// Updated for MSW v2.x - using 'http' instead of deprecated 'rest'
import { http, HttpResponse } from 'msw';

export const handlers = [
  http.get('/api/v1/items', ({ request }) => {
    return HttpResponse.json({ items: [] });
  }),
];
```

### Test Structure

```
frontend/src/tests/integration/
├── auth.integration.test.tsx           # Authentication flows
├── search.integration.test.tsx         # Search functionality
├── recommendations.integration.test.tsx # AI recommendations
├── full-system.integration.test.tsx    # Complete user journeys
└── api-contracts.integration.test.tsx  # API contract testing
```

## 🚀 **Running Integration Tests**

### Quick Start

```bash
# Run all integration tests
npm run test:integration:run

# Run only Jest-based integration tests
npm run test:integration:jest

# Run only Playwright E2E tests
npm run test:integration:e2e

# Run specific test suites
npm run test:integration    # Jest integration tests only
npm run test:api           # API contract tests only
npm run test:a11y          # Accessibility tests only
```

### Individual Test Commands

```bash
# Authentication tests
npm test -- auth.integration.test.tsx

# Search tests
npm test -- search.integration.test.tsx

# Full system tests
npm test -- full-system.integration.test.tsx
```

## 📋 **Test Categories**

### 1. Authentication Integration Tests

**File**: `auth.integration.test.tsx`

**Coverage**:
- User registration with validation
- Login with valid/invalid credentials
- Token refresh mechanisms
- Error handling for auth failures
- Social login integration (Google, Apple, Facebook)

**Example**:
```typescript
it('should successfully log in a user with valid credentials', async () => {
  render(<TestWrapper><Login /></TestWrapper>);
  
  await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
  await user.type(screen.getByLabelText(/password/i), 'password123');
  await user.click(screen.getByRole('button', { name: /log in/i }));
  
  await waitFor(() => {
    expect(screen.queryByText(/logging in/i)).not.toBeInTheDocument();
  });
});
```

### 2. Search Integration Tests

**File**: `search.integration.test.tsx`

**Coverage**:
- Search from home page
- Search results display
- Filtering and pagination
- Empty results handling
- Search API error handling

### 3. Recommendations Integration Tests

**File**: `recommendations.integration.test.tsx`

**Coverage**:
- Personalized recommendations loading
- Similar items recommendations
- Preference visualization
- Embedding visualization
- Recommendation interactions and analytics

### 4. Full System Integration Tests

**File**: `full-system.integration.test.tsx`

**Coverage**:
- Complete user journeys (registration → search → booking)
- Cross-page navigation
- Error handling across the system
- Performance and loading states
- Accessibility throughout the app
- Responsive design integration

## 🔧 **API Mocking with MSW v2.x**

### Handler Examples

```typescript
// Authentication
http.post('/api/v1/auth/login', async ({ request }) => {
  const body = await request.json();
  return HttpResponse.json({
    user: mockUser,
    access_token: 'mock-token',
  });
}),

// Search with query parameters
http.get('/api/v1/items', ({ request }) => {
  const url = new URL(request.url);
  const search = url.searchParams.get('search');
  const category = url.searchParams.get('category');
  
  // Filter mock data based on parameters
  return HttpResponse.json({ items: filteredItems });
}),

// Error simulation
http.get('/api/v1/recommendations/personalized', () => {
  return HttpResponse.error(); // Network error
}),
```

### Dynamic Handler Override

```typescript
// In tests, override specific handlers
server.use(
  http.post('/api/v1/auth/login', () => {
    return HttpResponse.json(
      { error: 'Invalid credentials' },
      { status: 401 }
    );
  })
);
```

## 📊 **Test Reports and Results**

### Automated Reporting

The integration test runner generates comprehensive reports:

```bash
# Reports are saved to:
tests/testResults/integration/
├── integration-test-report-[timestamp].json
└── integration-test-report-[timestamp].md
```

### Report Contents

- **Summary**: Pass/fail counts and success rate
- **Detailed Results**: Individual test outcomes
- **Error Details**: Full error messages and stack traces
- **Recommendations**: Next steps based on results

## 🚨 **Before Backend Integration**

**⚠️ IMPORTANT**: These tests currently use mocked APIs. Before running tests that require the backend:

1. **Notify the backend developer**
2. **Ensure backend is running on expected port**
3. **Verify API endpoints are available**
4. **Check database connectivity**

### Backend Integration Checklist

```bash
# 1. Backend health check
curl http://localhost:8000/health

# 2. Database connectivity
curl http://localhost:8000/api/v1/items

# 3. Authentication endpoints
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"test"}'
```

## 🔍 **Debugging Integration Tests**

### Common Issues

1. **MSW Handler Not Matching**
   ```typescript
   // Add logging to handlers
   http.get('/api/v1/items', ({ request }) => {
     console.log('MSW intercepted:', request.url);
     return HttpResponse.json({ items: [] });
   });
   ```

2. **Async Operations Not Waiting**
   ```typescript
   // Use proper waiting
   await waitFor(() => {
     expect(screen.getByText('Expected Text')).toBeInTheDocument();
   }, { timeout: 5000 });
   ```

3. **Component Not Rendering**
   ```typescript
   // Ensure proper test wrapper
   const TestWrapper = ({ children }) => (
     <QueryClientProvider client={queryClient}>
       <BrowserRouter>
         {children}
       </BrowserRouter>
     </QueryClientProvider>
   );
   ```

### Debug Mode

```bash
# Run tests with debug output
DEBUG=true npm run test:integration

# Run specific test with verbose output
npm test -- --verbose auth.integration.test.tsx
```

## 🎯 **Next Steps**

### Phase 1: Frontend-Only Integration (Current)
- ✅ MSW API mocking setup
- ✅ Authentication flow testing
- ✅ Search integration testing
- ✅ Recommendations testing
- ✅ Full user journey testing

### Phase 2: Backend Integration (Requires Backend Developer)
- 🔄 Real API integration testing
- 🔄 Database integration testing
- 🔄 WebSocket integration testing
- 🔄 File upload testing
- 🔄 Payment integration testing

### Phase 3: Production Readiness
- 🔄 Load testing with real backend
- 🔄 Security testing
- 🔄 Performance optimization
- 🔄 Monitoring integration

## 📚 **Additional Resources**

- [MSW v2.x Documentation](https://mswjs.io/docs/)
- [React Testing Library Best Practices](https://testing-library.com/docs/react-testing-library/intro/)
- [Playwright Testing Guide](https://playwright.dev/docs/intro)
- [Jest Testing Framework](https://jestjs.io/docs/getting-started)

---

**Last Updated**: May 2025  
**Status**: ✅ Frontend Integration Tests Ready  
**Next**: 🔄 Backend Integration (Pending Backend Developer)
