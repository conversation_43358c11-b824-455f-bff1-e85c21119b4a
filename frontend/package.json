{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:typecheck": "tsc -b && vite build", "build:analyze": "vite build --mode analyze", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "tsc --noEmit", "preview": "vite preview", "test": "playwright test", "test:ui": "playwright test --ui", "test:smoke": "playwright test tests/smoke.spec.js", "test:home": "playwright test tests/home-simple.spec.js", "test:debug": "playwright test --debug", "test:headed": "playwright test --headed", "test:report": "playwright show-report tests/testResults/html-report", "test:cypress": "cypress run", "test:cypress:open": "cypress open", "test:unit": "jest", "test:unit:watch": "jest --watch", "test:unit:coverage": "jest --coverage", "test:perf": "jest --testMatch='**/*.perf.test.{ts,tsx}'", "test:a11y": "jest --testMatch='**/*.a11y.test.{ts,tsx}'", "test:i18n": "playwright test tests/i18n.spec.js", "test:integration": "jest --testMatch='**/*.integration.test.{ts,tsx}'", "test:api": "jest --testMatch='**/*.api.test.{ts,tsx}'", "test:integration:run": "node scripts/run-integration-tests.js", "test:integration:jest": "node scripts/run-integration-tests.js --jest-only", "test:integration:e2e": "node scripts/run-integration-tests.js --e2e-only", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss}\"", "prepare": "husky install", "test:completion": "node tests/frontend-completion-test.js", "complete": "node complete-frontend.js"}, "dependencies": {"@emotion/react": "^11.15.0", "@emotion/styled": "^11.15.0", "@floating-ui/react-dom": "^2.2.0", "@greatsumini/react-facebook-login": "^3.5.0", "@headlessui/react": "^2.3.0", "@heroicons/react": "^2.3.0", "@hookform/resolvers": "^3.4.0", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@mui/x-date-pickers": "^8.4.0", "@react-oauth/google": "^0.13.0", "@tailwindcss/aspect-ratio": "^0.4.3", "@tailwindcss/container-queries": "^0.2.0", "@tailwindcss/postcss": "^4.2.0", "@tailwindcss/typography": "^0.6.0", "@tailwindcss/vite": "^4.2.0", "@tanstack/react-query": "^5.80.0", "@types/google-one-tap": "^1.3.0", "autoprefixer": "^10.5.0", "axios": "^1.9.0", "class-variance-authority": "^0.8.0", "date-fns": "^4.2.0", "framer-motion": "^13.0.0", "i18next": "^24.0.0", "i18next-browser-languagedetector": "^8.0.0", "i18next-http-backend": "^3.0.0", "postcss": "^8.6.0", "qrcode.react": "^4.3.0", "react": "^19.1.0", "react-apple-signin-auth": "^1.2.0", "react-date-range": "^2.1.0", "react-dom": "^19.1.0", "react-dropzone": "^15.0.0", "react-helmet-async": "^2.1.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.6.0", "react-i18next": "^15.0.0", "react-intersection-observer": "^10.0.0", "react-router-dom": "^8.0.0", "tailwindcss": "^4.2.0", "tailwindcss-fluid-type": "^2.1.0", "web-vitals": "^6.0.0", "zod": "^3.25.0", "zustand": "^5.1.0"}, "devDependencies": {"@axe-core/playwright": "^4.11.0", "@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "@babel/preset-react": "^7.25.0", "@babel/preset-typescript": "^7.25.0", "@eslint/config-array": "^0.21.0", "@eslint/js": "^9.30.0", "@eslint/object-schema": "^2.2.0", "@mswjs/interceptors": "^0.40.0", "@playwright/test": "^1.55.0", "@storybook/addon-essentials": "^8.8.0", "@storybook/addon-interactions": "^8.8.0", "@storybook/addon-links": "^8.8.0", "@storybook/blocks": "^8.8.0", "@storybook/react": "^8.8.0", "@storybook/react-vite": "^8.8.0", "@storybook/test": "^8.8.0", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^15.0.0", "@types/jest": "^29.6.0", "@types/node": "^22.20.0", "@types/prop-types": "^15.8.0", "@types/react": "^19.2.0", "@types/react-dom": "^19.2.0", "@typescript-eslint/eslint-plugin": "^8.40.0", "@typescript-eslint/parser": "^8.40.0", "@vitejs/plugin-react": "^4.4.0", "axe-playwright": "^2.2.0", "babel-jest": "^29.8.0", "chalk": "^5.5.0", "cypress": "^15.0.0", "cypress-file-upload": "^6.0.0", "eslint": "^9.30.0", "eslint-plugin-react": "^7.40.0", "eslint-plugin-react-hooks": "^5.3.0", "eslint-plugin-react-refresh": "^0.5.0", "glob": "^12.0.0", "globals": "^16.2.0", "husky": "^9.1.0", "identity-obj-proxy": "^3.1.0", "jest": "^29.8.0", "jest-axe": "^9.0.0", "jest-environment-jsdom": "^29.8.0", "lint-staged": "^15.3.0", "lru-cache": "^12.0.0", "mochawesome": "^8.0.0", "mochawesome-merge": "^6.0.0", "mochawesome-report-generator": "^7.0.0", "msw": "^2.10.0", "prettier": "^3.6.0", "rimraf": "^7.0.0", "rollup-plugin-visualizer": "^6.0.0", "terser": "^6.0.0", "typescript": "~5.9.0", "typescript-eslint": "^8.40.0", "vite": "^6.4.0", "vite-plugin-compression": "^0.6.0", "vite-plugin-imagemin": "^0.7.0", "vite-plugin-pwa": "^0.21.0"}}