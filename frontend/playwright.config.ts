// playwright.config.ts - Consolidated Configuration (Single Source of Truth)
import { defineConfig, devices } from '@playwright/test';
import path from 'path';

// Define the port your Vite dev server runs on (default is 5173)
const PORT = process.env.PORT || 5173;
const baseURL = `http://localhost:${PORT}`;

export default defineConfig({
  testDir: './tests', // Look for tests in the tests directory
  timeout: 60 * 1000, // Maximum time one test can run for
  expect: {
    timeout: 15000 // Maximum time expect() should wait for the condition to be met
  },
  fullyParallel: true, // Run tests in parallel for speed
  forbidOnly: !!process.env.CI, // Fail the build on CI if you accidentally left test.only in the source code.
  retries: process.env.CI ? 2 : 0, // Retry on CI only
  workers: process.env.CI ? 1 : undefined, // Adjust worker count based on environment
  reporter: [
    ['html', { outputFolder: './tests/testResults/html-report' }],
    ['list'],
    ['./tests/reporters/DetailedReporter.js']
  ],

  use: {
    // Base URL to use in actions like `await page.goto('/')`.
    baseURL: baseURL,

    // Collect trace when retrying the failed test.
    trace: 'on-first-retry',

    // Capture screenshot on failure
    screenshot: 'only-on-failure',

    // Record video on failure/retry
    video: 'retain-on-failure',

    // Increase timeouts for better stability
    navigationTimeout: 60000,
    actionTimeout: 30000,

    // Wait for page to be in a more stable state before considering navigation complete
    waitForNavigation: { waitUntil: 'networkidle' },
  },

  // Folder for test artifacts such as screenshots, videos, traces, etc.
  outputDir: './tests/testResults/test-artifacts/',

  // Configure projects for major browsers.
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
  ],

  // Run your local dev server before starting the tests!
  webServer: {
    command: `npm run dev -- --port ${PORT}`,
    url: baseURL,
    reuseExistingServer: !process.env.CI, // Reuse server if already running locally
    stdout: 'pipe', // Changed from 'ignore' to 'pipe' for better debugging
    stderr: 'pipe',
    timeout: 180 * 1000, // Increased timeout for slower server starts
  },
});
