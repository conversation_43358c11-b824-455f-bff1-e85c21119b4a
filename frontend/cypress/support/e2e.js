// ***********************************************************
// This example support/e2e.js is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands';

// Alternatively you can use CommonJS syntax:
// require('./commands');

// Ignore uncaught exceptions
Cypress.on('uncaught:exception', (err, runnable) => {
  // returning false here prevents <PERSON><PERSON> from failing the test
  return false;
});

// Add more time for page load
Cypress.config('pageLoadTimeout', 60000);
Cypress.config('defaultCommandTimeout', 20000);

// Log more information during tests
Cypress.on('log:added', (attrs, log) => {
  if (attrs.name === 'xhr' || attrs.name === 'request') {
    attrs.consoleProps = () => {
      return {
        Request: attrs.xhr?.request || 'No request data',
        Response: attrs.xhr?.response || 'No response data',
        Status: attrs.xhr?.status || 'No status',
      };
    };
  }
});

// Add custom commands
Cypress.Commands.add('waitForPageLoad', () => {
  cy.window().then(win => {
    return new Cypress.Promise(resolve => {
      if (win.document.readyState === 'complete') {
        resolve();
      } else {
        win.addEventListener('load', resolve);
      }
    });
  });
});

// Add command to check if element exists without failing
Cypress.Commands.add('ifExists', { prevSubject: 'optional' }, (subject, selector, callback) => {
  const log = Cypress.log({
    name: 'ifExists',
    message: selector,
    consoleProps: () => {
      return {
        Selector: selector,
        Subject: subject,
      };
    },
  });

  const checkElement = ($el) => {
    if ($el && $el.length) {
      log.set('message', `Found ${$el.length} element(s)`);
      callback($el);
    } else {
      log.set('message', 'No elements found');
      cy.log(`No elements found for selector: ${selector}`);
    }
    log.end();
  };

  if (subject) {
    cy.wrap(subject).find(selector).then(checkElement);
  } else {
    cy.get('body').find(selector).then(checkElement);
  }
});
