// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... })

// Custom command to check if an element exists
Cypress.Commands.add('elementExists', (selector) => {
  return cy.get('body').then($body => {
    return $body.find(selector).length > 0;
  });
});

// Custom command to try to click an element, but not fail if it doesn't exist
Cypress.Commands.add('tryClick', (selector) => {
  cy.get('body').then($body => {
    if ($body.find(selector).length > 0) {
      cy.get(selector).click();
    } else {
      cy.log(`Element with selector "${selector}" not found, skipping click`);
    }
  });
});

// Custom command to try to type in an element, but not fail if it doesn't exist
Cypress.Commands.add('tryType', (selector, text) => {
  cy.get('body').then($body => {
    if ($body.find(selector).length > 0) {
      cy.get(selector).type(text, { force: true });
    } else {
      cy.log(`Element with selector "${selector}" not found, skipping type`);
    }
  });
});

// Custom command to wait for a specific amount of time (renamed to avoid conflict)
Cypress.Commands.add('waitForTime', (milliseconds) => {
  cy.wait(milliseconds);
});

// Custom command to check if page has loaded
Cypress.Commands.add('isPageLoaded', () => {
  return cy.window().then(win => {
    return win.document.readyState === 'complete';
  });
});

// Custom command to take a screenshot with a timestamp
Cypress.Commands.add('timestampedScreenshot', (name) => {
  const timestamp = new Date().toISOString().replace(/:/g, '-');
  cy.screenshot(`${name}-${timestamp}`);
});

// Custom command to check for console errors
Cypress.Commands.add('noConsoleErrors', () => {
  cy.window().then((win) => {
    const originalConsoleError = win.console.error;
    win.console.error = function (...args) {
      // Log the error to Cypress
      cy.log('Console error:', ...args);
      // Call the original console.error
      originalConsoleError.apply(win.console, args);
    };
  });
});
