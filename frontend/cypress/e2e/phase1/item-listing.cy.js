describe('Item & Listing UI', () => {
  it('should display search results page with filters', () => {
    cy.visit('http://localhost:5173/search');
    
    // Check search input exists
    cy.get('input[type="search"]').should('be.visible');
    
    // Check filters exist
    cy.get('form').should('be.visible');
    
    // Check items are displayed
    cy.get('div').contains('Results').should('be.visible');
  });

  it('should display item details page with all sections', () => {
    cy.visit('http://localhost:5173/items/1');
    
    // Check item title exists
    cy.get('h1').should('be.visible');
    
    // Check item images exist
    cy.get('img').should('be.visible');
    
    // Check price information exists
    cy.contains('per day').should('be.visible');
    
    // Check booking section exists
    cy.contains('Book Now').should('be.visible');
    
    // Check item description exists
    cy.contains('Description').should('be.visible');
    
    // Check owner information exists
    cy.contains('Owner').should('be.visible');
  });

  it('should display categories browse page', () => {
    cy.visit('http://localhost:5173/');
    
    // Find and click on a category
    cy.contains('Shop by Categories').click();
    cy.contains('Electronics').click();
    
    // Should navigate to search page with category parameter
    cy.url().should('include', '/search?category=electronics');
    
    // Check category title is displayed
    cy.contains('Electronics').should('be.visible');
  });

  it('should display item creation form with all fields', () => {
    cy.visit('http://localhost:5173/items/new');
    
    // Check form title exists
    cy.contains('List an Item').should('be.visible');
    
    // Check basic form fields exist
    cy.get('input[name="title"]').should('be.visible');
    cy.get('textarea[name="description"]').should('be.visible');
    cy.get('input[name="price"]').should('be.visible');
    
    // Check category selection exists
    cy.contains('Category').should('be.visible');
    
    // Check image upload section exists
    cy.contains('Images').should('be.visible');
    
    // Check submit button exists
    cy.contains('button', 'Submit').should('be.visible');
  });
});
