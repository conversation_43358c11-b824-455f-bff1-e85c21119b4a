describe('User & Booking UI', () => {
  it('should display user profile page with all sections', () => {
    cy.visit('http://localhost:5173/profile');
    
    // Check profile header exists
    cy.contains('Profile').should('be.visible');
    
    // Check user information section exists
    cy.contains('Account Information').should('be.visible');
    
    // Check listings section exists
    cy.contains('My Listings').should('be.visible');
    
    // Check bookings section exists
    cy.contains('My Bookings').should('be.visible');
    
    // Check reviews section exists
    cy.contains('Reviews').should('be.visible');
  });

  it('should display transaction history', () => {
    cy.visit('http://localhost:5173/profile');
    
    // Navigate to transaction history
    cy.contains('Transaction History').click();
    
    // Check transaction list exists
    cy.contains('Transactions').should('be.visible');
    
    // Check filter options exist
    cy.contains('Filter').should('be.visible');
  });

  it('should display reviews & ratings UI', () => {
    cy.visit('http://localhost:5173/items/1');
    
    // Check reviews section exists
    cy.contains('Reviews').should('be.visible');
    
    // Check rating display exists
    cy.get('[data-testid="rating"]').should('be.visible');
    
    // Check review list exists
    cy.get('[data-testid="reviews-list"]').should('be.visible');
  });

  it('should display booking process form', () => {
    cy.visit('http://localhost:5173/booking/1');
    
    // Check booking form exists
    cy.contains('Booking Details').should('be.visible');
    
    // Check date selection exists
    cy.contains('Select Dates').should('be.visible');
    
    // Check item summary exists
    cy.contains('Item Summary').should('be.visible');
    
    // Check price calculation exists
    cy.contains('Price Details').should('be.visible');
    
    // Check submit button exists
    cy.contains('button', 'Continue to Payment').should('be.visible');
  });

  it('should display messaging interface', () => {
    cy.visit('http://localhost:5173/messages');
    
    // Check messages header exists
    cy.contains('Messages').should('be.visible');
    
    // Check conversation list exists
    cy.get('[data-testid="conversation-list"]').should('be.visible');
    
    // Check message input exists
    cy.get('textarea').should('be.visible');
    
    // Check send button exists
    cy.contains('button', 'Send').should('be.visible');
  });

  it('should display notifications page', () => {
    cy.visit('http://localhost:5173/notifications');
    
    // Check notifications header exists
    cy.contains('Notifications').should('be.visible');
    
    // Check notification list exists
    cy.get('[data-testid="notification-list"]').should('be.visible');
    
    // Check filter options exist
    cy.contains('Filter').should('be.visible');
  });

  it('should display agreement & verification UI', () => {
    cy.visit('http://localhost:5173/verification');
    
    // Check verification header exists
    cy.contains('Verification Center').should('be.visible');
    
    // Check identity verification section exists
    cy.contains('Identity Verification').should('be.visible');
    
    // Check document upload section exists
    cy.contains('Upload Documents').should('be.visible');
    
    // Check verification status section exists
    cy.contains('Verification Status').should('be.visible');
  });
});
