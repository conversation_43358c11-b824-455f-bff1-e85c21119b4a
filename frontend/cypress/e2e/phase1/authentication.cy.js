describe('Authentication Components', () => {
  beforeEach(() => {
    // Add a longer timeout for page load
    cy.visit('http://localhost:5173/login', { timeout: 30000 });

    // Wait for the page to be fully loaded and stable
    cy.get('body').should('be.visible');
    cy.wait(1000); // Add a small delay to ensure all elements are rendered
  });

  it('should display login page with all form elements', () => {
    // Check login header exists with retry-ability
    cy.contains('Log In', { timeout: 10000 }).should('be.visible');

    // Check email/username input exists
    cy.get('input[type="email"], input[name="email"]')
      .should('exist')
      .should('be.visible')
      .should('not.be.disabled');

    // Check password input exists
    cy.get('input[type="password"]')
      .should('exist')
      .should('be.visible')
      .should('not.be.disabled');

    // Check login button exists and is enabled
    cy.contains('button', 'Log In')
      .should('exist')
      .should('be.visible')
      .should('not.be.disabled');

    // Check social login options exist
    cy.contains('Continue with Google', { timeout: 10000 })
      .should('exist')
      .should('be.visible');

    // Check register link exists
    cy.contains('Sign Up')
      .should('exist')
      .should('be.visible');

    // Check forgot password link exists
    cy.contains('Forgot Password')
      .should('exist')
      .should('be.visible');
  });

  it('should display register page with all form elements', () => {
    cy.visit('http://localhost:5173/register');

    // Check register header exists
    cy.contains('Sign Up').should('be.visible');

    // Check name input exists
    cy.get('input[name="name"]').should('be.visible');

    // Check email input exists
    cy.get('input[type="email"], input[name="email"]').should('be.visible');

    // Check password input exists
    cy.get('input[type="password"]').should('be.visible');

    // Check confirm password input exists
    cy.get('input[name="confirmPassword"]').should('be.visible');

    // Check register button exists
    cy.contains('button', 'Sign Up').should('be.visible');

    // Check social login options exist
    cy.contains('Continue with Google').should('be.visible');

    // Check login link exists
    cy.contains('Log In').should('be.visible');
  });

  it('should validate login form inputs', () => {
    cy.visit('http://localhost:5173/login');

    // Try to submit empty form
    cy.contains('button', 'Log In').click();

    // Check validation error messages
    cy.contains('Email is required').should('be.visible');
    cy.contains('Password is required').should('be.visible');

    // Enter invalid email
    cy.get('input[type="email"], input[name="email"]').type('invalid-email');
    cy.contains('button', 'Log In').click();

    // Check validation error message for invalid email
    cy.contains('Invalid email format').should('be.visible');

    // Enter valid email but short password
    cy.get('input[type="email"], input[name="email"]').clear().type('<EMAIL>');
    cy.get('input[type="password"]').type('123');
    cy.contains('button', 'Log In').click();

    // Check validation error message for short password
    cy.contains('Password must be at least').should('be.visible');
  });

  it('should validate register form inputs', () => {
    cy.visit('http://localhost:5173/register');

    // Try to submit empty form
    cy.contains('button', 'Sign Up').click();

    // Check validation error messages
    cy.contains('Name is required').should('be.visible');
    cy.contains('Email is required').should('be.visible');
    cy.contains('Password is required').should('be.visible');

    // Enter invalid email
    cy.get('input[name="name"]').type('Test User');
    cy.get('input[type="email"], input[name="email"]').type('invalid-email');
    cy.contains('button', 'Sign Up').click();

    // Check validation error message for invalid email
    cy.contains('Invalid email format').should('be.visible');

    // Enter valid email but short password
    cy.get('input[type="email"], input[name="email"]').clear().type('<EMAIL>');
    cy.get('input[type="password"]').type('123');
    cy.contains('button', 'Sign Up').click();

    // Check validation error message for short password
    cy.contains('Password must be at least').should('be.visible');

    // Enter valid email and password but different confirm password
    cy.get('input[type="password"]').clear().type('password123');
    cy.get('input[name="confirmPassword"]').type('password456');
    cy.contains('button', 'Sign Up').click();

    // Check validation error message for password mismatch
    cy.contains('Passwords do not match').should('be.visible');
  });
});
