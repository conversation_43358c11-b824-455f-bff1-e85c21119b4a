describe('Core UI Components', () => {
  beforeEach(() => {
    // Visit the home page before each test
    cy.visit('http://localhost:5173/');
  });

  it('should display the header with logo and navigation', () => {
    // Check header exists
    cy.get('header').should('be.visible');
    
    // Check logo exists
    cy.get('header a[href="/"]').should('be.visible');
    
    // Check navigation links exist
    cy.get('header nav').should('be.visible');
  });

  it('should display the footer with links and newsletter form', () => {
    // Check footer exists
    cy.get('footer').should('be.visible');
    
    // Check newsletter form exists
    cy.get('footer form').should('be.visible');
    cy.get('footer form input[type="email"]').should('be.visible');
    cy.get('footer form button[type="submit"]').should('be.visible');
    
    // Check footer links exist
    cy.get('footer a').should('have.length.greaterThan', 5);
  });

  it('should have a working search bar', () => {
    // Type in search bar
    cy.get('input[type="search"]').first().type('test search{enter}');
    
    // Should navigate to search page with query parameter
    cy.url().should('include', '/search?q=test%20search');
  });

  it('should have working navigation links', () => {
    // Click on login link
    cy.get('a[href="/login"]').first().click();
    cy.url().should('include', '/login');
    
    // Go back to home
    cy.visit('http://localhost:5173/');
    
    // Click on search link
    cy.get('a[href="/search"]').first().click();
    cy.url().should('include', '/search');
  });

  it('should have a responsive layout', () => {
    // Test on mobile viewport
    cy.viewport('iphone-x');
    cy.get('header').should('be.visible');
    cy.get('footer').should('be.visible');
    
    // Test on tablet viewport
    cy.viewport('ipad-2');
    cy.get('header').should('be.visible');
    cy.get('footer').should('be.visible');
    
    // Test on desktop viewport
    cy.viewport('macbook-15');
    cy.get('header').should('be.visible');
    cy.get('footer').should('be.visible');
  });
});
