#!/usr/bin/env node

/**
 * Integration Test Runner
 * Runs comprehensive integration tests for the RentUp frontend
 * Updated for May 2025 - includes MSW v2.x and latest testing practices
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

function logStep(step, message) {
  log(`\n${step}. ${message}`, 'blue');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

// Test configuration
const testConfig = {
  // Test suites to run
  suites: [
    {
      name: 'Unit Tests',
      command: 'npm run test:unit',
      description: 'Individual component and service tests',
    },
    {
      name: 'Integration Tests',
      command: 'npm run test:integration',
      description: 'API integration and user flow tests',
    },
    {
      name: 'API Tests',
      command: 'npm run test:api',
      description: 'Frontend-backend API contract tests',
    },
    {
      name: 'Accessibility Tests',
      command: 'npm run test:a11y',
      description: 'WCAG 2.1 AA compliance tests',
    },
    {
      name: 'Performance Tests',
      command: 'npm run test:perf',
      description: 'Performance and Core Web Vitals tests',
    },
  ],
  
  // Playwright E2E tests (separate from Jest)
  e2eTests: [
    {
      name: 'Smoke Tests',
      command: 'npm run test:smoke',
      description: 'Basic functionality verification',
    },
    {
      name: 'Home Page Tests',
      command: 'npm run test:home',
      description: 'Home page functionality tests',
    },
    {
      name: 'Full E2E Tests',
      command: 'npm test',
      description: 'Complete end-to-end user journeys',
    },
  ],
};

// Results tracking
const results = {
  passed: 0,
  failed: 0,
  skipped: 0,
  details: [],
};

function runCommand(command, description) {
  try {
    log(`Running: ${command}`, 'cyan');
    const output = execSync(command, { 
      encoding: 'utf8',
      stdio: 'pipe',
      cwd: process.cwd(),
    });
    
    logSuccess(`${description} - PASSED`);
    results.passed++;
    results.details.push({
      name: description,
      status: 'PASSED',
      command,
      output: output.slice(-500), // Last 500 chars
    });
    
    return true;
  } catch (error) {
    logError(`${description} - FAILED`);
    results.failed++;
    results.details.push({
      name: description,
      status: 'FAILED',
      command,
      error: error.message,
      output: error.stdout || error.stderr || 'No output available',
    });
    
    return false;
  }
}

function checkPrerequisites() {
  logStep(1, 'Checking Prerequisites');
  
  // Check if node_modules exists
  if (!fs.existsSync('node_modules')) {
    logError('node_modules not found. Please run "npm install" first.');
    process.exit(1);
  }
  
  // Check if MSW is installed
  try {
    require.resolve('msw');
    logSuccess('MSW (Mock Service Worker) is installed');
  } catch (error) {
    logError('MSW not found. Installing...');
    try {
      execSync('npm install msw@^2.6.4 --save-dev', { stdio: 'inherit' });
      logSuccess('MSW installed successfully');
    } catch (installError) {
      logError('Failed to install MSW. Please install manually.');
      process.exit(1);
    }
  }
  
  // Check if test files exist
  const testFiles = [
    'src/tests/integration/auth.integration.test.tsx',
    'src/tests/integration/search.integration.test.tsx',
    'src/tests/integration/recommendations.integration.test.tsx',
    'src/tests/integration/full-system.integration.test.tsx',
  ];
  
  const missingFiles = testFiles.filter(file => !fs.existsSync(file));
  if (missingFiles.length > 0) {
    logWarning(`Missing test files: ${missingFiles.join(', ')}`);
  } else {
    logSuccess('All integration test files found');
  }
}

function runJestTests() {
  logStep(2, 'Running Jest-based Tests');
  
  for (const suite of testConfig.suites) {
    log(`\n📋 ${suite.name}: ${suite.description}`, 'magenta');
    runCommand(suite.command, suite.name);
  }
}

function runPlaywrightTests() {
  logStep(3, 'Running Playwright E2E Tests');
  
  // Check if frontend dev server is running
  try {
    execSync('curl -f http://localhost:5173 > /dev/null 2>&1');
    logSuccess('Frontend dev server is running');
  } catch (error) {
    logWarning('Frontend dev server not detected. Starting...');
    log('Please ensure the frontend is running on http://localhost:5173', 'yellow');
    log('Run "npm run dev" in another terminal if not already running', 'yellow');
  }
  
  for (const test of testConfig.e2eTests) {
    log(`\n🎭 ${test.name}: ${test.description}`, 'magenta');
    runCommand(test.command, test.name);
  }
}

function generateReport() {
  logStep(4, 'Generating Test Report');
  
  const reportData = {
    timestamp: new Date().toISOString(),
    summary: {
      total: results.passed + results.failed,
      passed: results.passed,
      failed: results.failed,
      successRate: results.passed + results.failed > 0 
        ? Math.round((results.passed / (results.passed + results.failed)) * 100) 
        : 0,
    },
    details: results.details,
  };
  
  // Create reports directory if it doesn't exist
  const reportsDir = 'tests/testResults/integration';
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }
  
  // Write JSON report
  const reportFile = path.join(reportsDir, `integration-test-report-${Date.now()}.json`);
  fs.writeFileSync(reportFile, JSON.stringify(reportData, null, 2));
  
  // Write markdown report
  const markdownReport = generateMarkdownReport(reportData);
  const markdownFile = path.join(reportsDir, `integration-test-report-${Date.now()}.md`);
  fs.writeFileSync(markdownFile, markdownReport);
  
  logSuccess(`Reports generated:`);
  log(`  JSON: ${reportFile}`, 'cyan');
  log(`  Markdown: ${markdownFile}`, 'cyan');
  
  return reportData;
}

function generateMarkdownReport(data) {
  return `# Integration Test Report

**Generated:** ${data.timestamp}

## Summary

- **Total Tests:** ${data.summary.total}
- **Passed:** ${data.summary.passed} ✅
- **Failed:** ${data.summary.failed} ❌
- **Success Rate:** ${data.summary.successRate}%

## Test Results

${data.details.map(test => `
### ${test.name}

**Status:** ${test.status === 'PASSED' ? '✅ PASSED' : '❌ FAILED'}
**Command:** \`${test.command}\`

${test.status === 'FAILED' ? `
**Error:**
\`\`\`
${test.error}
\`\`\`

**Output:**
\`\`\`
${test.output}
\`\`\`
` : ''}
`).join('\n')}

## Recommendations

${data.summary.failed > 0 ? `
⚠️ **${data.summary.failed} test(s) failed.** Please review the errors above and fix the issues before proceeding.

Common issues:
- Missing dependencies (run \`npm install\`)
- Frontend dev server not running (run \`npm run dev\`)
- Backend API not available (check backend status)
- Environment variables not set
` : `
✅ **All tests passed!** The frontend integration is working correctly.

Next steps:
- Run backend integration tests
- Perform full system testing with backend
- Deploy to staging environment
`}
`;
}

function printSummary(reportData) {
  logHeader('Integration Test Summary');
  
  log(`📊 Test Results:`, 'bright');
  log(`   Total: ${reportData.summary.total}`);
  log(`   Passed: ${reportData.summary.passed}`, 'green');
  log(`   Failed: ${reportData.summary.failed}`, reportData.summary.failed > 0 ? 'red' : 'green');
  log(`   Success Rate: ${reportData.summary.successRate}%`, reportData.summary.successRate >= 80 ? 'green' : 'red');
  
  if (reportData.summary.failed > 0) {
    log('\n🔍 Failed Tests:', 'red');
    reportData.details
      .filter(test => test.status === 'FAILED')
      .forEach(test => {
        log(`   - ${test.name}`, 'red');
      });
  }
  
  log('\n🎯 Next Steps:', 'bright');
  if (reportData.summary.failed === 0) {
    log('   ✅ All frontend integration tests passed!', 'green');
    log('   ✅ Ready for backend integration testing', 'green');
    log('   ✅ Ready for full system testing', 'green');
  } else {
    log('   🔧 Fix failing tests before proceeding', 'yellow');
    log('   📋 Review test output for specific issues', 'yellow');
    log('   🔄 Re-run tests after fixes', 'yellow');
  }
}

// Main execution
async function main() {
  logHeader('RentUp Frontend Integration Test Suite');
  log('Updated for May 2025 - MSW v2.x, React 19, Latest Testing Practices', 'cyan');
  
  try {
    checkPrerequisites();
    runJestTests();
    runPlaywrightTests();
    const reportData = generateReport();
    printSummary(reportData);
    
    // Exit with appropriate code
    process.exit(reportData.summary.failed > 0 ? 1 : 0);
    
  } catch (error) {
    logError(`Test runner failed: ${error.message}`);
    process.exit(1);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  log('RentUp Integration Test Runner', 'bright');
  log('\nUsage: node scripts/run-integration-tests.js [options]');
  log('\nOptions:');
  log('  --help, -h     Show this help message');
  log('  --jest-only    Run only Jest tests (skip Playwright)');
  log('  --e2e-only     Run only Playwright E2E tests (skip Jest)');
  process.exit(0);
}

if (args.includes('--jest-only')) {
  testConfig.e2eTests = [];
}

if (args.includes('--e2e-only')) {
  testConfig.suites = [];
}

// Run the tests
main();
