// @ts-check
import { test, expect } from '@playwright/test';

test.describe('Debug Tests', () => {
  test('should load the home page and capture console logs', async ({ page }) => {
    // Listen for console messages
    const consoleMessages = [];
    page.on('console', msg => {
      consoleMessages.push(`${msg.type()}: ${msg.text()}`);
    });

    // Listen for page errors
    const pageErrors = [];
    page.on('pageerror', error => {
      pageErrors.push(error.message);
    });

    // Navigate to home page
    await page.goto('/');
    
    // Wait a bit for the page to load
    await page.waitForTimeout(5000);
    
    // Take a screenshot for debugging
    await page.screenshot({ path: 'debug-home-page.png', fullPage: true });
    
    // Log what we found
    console.log('Console messages:', consoleMessages);
    console.log('Page errors:', pageErrors);
    console.log('Page title:', await page.title());
    console.log('Page URL:', page.url());
    
    // Get the page content
    const bodyText = await page.locator('body').textContent();
    console.log('Body text length:', bodyText?.length || 0);
    console.log('First 500 chars of body:', bodyText?.substring(0, 500) || 'No body text');
    
    // Check if React has loaded
    const reactLoaded = await page.evaluate(() => {
      return typeof window.React !== 'undefined' || document.querySelector('[data-reactroot]') !== null;
    });
    console.log('React loaded:', reactLoaded);
    
    // Check for any elements
    const elementCount = await page.locator('*').count();
    console.log('Total elements on page:', elementCount);
    
    // This test should always pass - we're just gathering info
    expect(true).toBe(true);
  });

  test('should check if Vite dev server is responding', async ({ page }) => {
    // Try to access the Vite dev server directly
    const response = await page.goto('/');
    
    console.log('Response status:', response?.status());
    console.log('Response headers:', await response?.allHeaders());
    
    // Check if we get a valid HTML response
    const contentType = response?.headers()['content-type'];
    console.log('Content type:', contentType);
    
    expect(response?.status()).toBe(200);
  });
});
