// @ts-check
import { test, expect } from '@playwright/test';

/**
 * Comprehensive accessibility test suite for RentUp
 * Tests WCAG 2.1 AA compliance across key pages
 */
test.describe('Accessibility Tests', () => {
  test('home page should be accessible', async ({ page }) => {
    await page.goto('/');

    // Wait for the page to fully load
    await page.waitForLoadState('networkidle');

    // Wait for critical elements to be visible with longer timeout
    await page.waitForSelector('header', { state: 'visible', timeout: 15000 });
    await page.waitForSelector('main', { state: 'visible', timeout: 15000 });
    await page.waitForSelector('footer', { state: 'visible', timeout: 15000 });

    // Check specific fixes

    // 1. Skip link should target an element with id="main-content"
    const skipLink = await page.locator('a[href="#main-content"]');
    await expect(skipLink).toBeVisible({ visible: 'hidden' }); // Should be visually hidden but present

    const mainContent = await page.locator('#main-content');
    await expect(mainContent).toBeVisible();

    // 2. Navigation links should have sufficient contrast
    const navLinks = await page.locator('nav a');
    for (let i = 0; i < await navLinks.count(); i++) {
      const link = navLinks.nth(i);
      const color = await link.evaluate(el => {
        return window.getComputedStyle(el).color;
      });

      // We don't have a way to directly test contrast ratio in Playwright
      // But we can check that we're not using the problematic color class
      const hasClass = await link.evaluate(el => {
        return el.classList.contains('text-dark-gray');
      });

      expect(hasClass).toBeFalsy();
    }

    // 3. Social media links should have accessible names
    const socialLinks = await page.locator('footer a[href*="facebook"], footer a[href*="instagram"], footer a[href*="tiktok"], footer a[href*="x.com"]');

    for (let i = 0; i < await socialLinks.count(); i++) {
      const link = socialLinks.nth(i);

      // Check for aria-label
      const ariaLabel = await link.getAttribute('aria-label');
      expect(ariaLabel).toBeTruthy();

      // Check for sr-only text
      const hasSrOnlyText = await link.evaluate(el => {
        return el.querySelector('.sr-only') !== null;
      });

      expect(hasSrOnlyText).toBeTruthy();
    }
  });

  test('search page should be accessible', async ({ page }) => {
    await page.goto('/search');

    // Wait for critical elements to be visible
    await page.waitForSelector('header', { state: 'visible' });
    await page.waitForSelector('main', { state: 'visible' });

    // Check navigation links for proper contrast
    const navLinks = await page.locator('nav a');
    for (let i = 0; i < await navLinks.count(); i++) {
      const link = navLinks.nth(i);

      // Check that we're not using the problematic color class
      const hasClass = await link.evaluate(el => {
        return el.classList.contains('text-dark-gray');
      });

      expect(hasClass).toBeFalsy();
    }
  });

  test('should use proper heading structure', async ({ page }) => {
    await page.goto('/');

    // Get all headings
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();

    // Create a map of heading levels
    const headingLevels = [];
    for (const heading of headings) {
      const tagName = await heading.evaluate(el => el.tagName.toLowerCase());
      const level = parseInt(tagName.substring(1));
      headingLevels.push(level);
    }

    // Check that heading levels don't skip (e.g., h1 -> h3)
    for (let i = 0; i < headingLevels.length - 1; i++) {
      const current = headingLevels[i];
      const next = headingLevels[i + 1];

      // Next heading should not be more than one level deeper
      if (next > current) {
        expect(next - current).toBeLessThanOrEqual(1);
      }
    }
  });

  test('should have proper document language', async ({ page }) => {
    await page.goto('/');

    // Check that html has lang attribute
    const htmlLang = await page.locator('html').getAttribute('lang');
    expect(htmlLang).toBeTruthy();
    expect(htmlLang).toEqual('en');
  });
});
