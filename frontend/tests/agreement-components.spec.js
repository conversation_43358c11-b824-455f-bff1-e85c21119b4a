// @ts-check
import { test, expect } from '@playwright/test';

test.describe('Agreement Components', () => {
  // Helper function to mock authentication
  async function mockAuthentication(page) {
    // Mock localStorage to simulate being logged in
    await page.addInitScript(() => {
      localStorage.setItem('auth_token', 'mock_token');
      localStorage.setItem('user', JSON.stringify({
        id: 'user-2',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'user'
      }));
    });
  }

  test('should render the signature capture component', async ({ page }) => {
    // Mock authentication first
    await mockAuthentication(page);

    // Navigate to the agreement sign page
    await page.goto('/agreements/agreement-1/sign');

    // Wait for the loading to complete and page to load
    await page.waitForLoadState('networkidle');
    await page.waitForSelector('h2:has-text("Sign Agreement")', { timeout: 15000 });

    // Check if signature canvas is rendered
    const signatureCanvas = await page.locator('canvas').first();
    await expect(signatureCanvas).toBeVisible();

    // Check if pen color options are rendered
    const colorButtons = await page.locator('button[title]').filter({ hasText: '' });
    expect(await colorButtons.count()).toBeGreaterThan(0);

    // Check if pen thickness options are rendered
    const thicknessButtons = await page.locator('button').filter({ hasText: /^[1-9]$/ });
    expect(await thicknessButtons.count()).toBeGreaterThan(0);

    // Check if clear and confirm buttons are rendered
    await expect(page.locator('button:has-text("Clear")')).toBeVisible();
    await expect(page.locator('button:has-text("Confirm Signature")')).toBeVisible();
  });

  test('should be able to draw on the signature canvas', async ({ page }) => {
    // Mock authentication first
    await mockAuthentication(page);

    // Navigate to the agreement sign page
    await page.goto('/agreements/agreement-1/sign');

    // Wait for the loading to complete and page to load
    await page.waitForLoadState('networkidle');
    await page.waitForSelector('h2:has-text("Sign Agreement")', { timeout: 15000 });

    // Get the signature canvas
    const signatureCanvas = await page.locator('canvas').first();

    // Get the bounding box of the canvas
    const boundingBox = await signatureCanvas.boundingBox();
    if (!boundingBox) {
      throw new Error('Canvas bounding box not found');
    }

    // Draw a signature on the canvas
    await page.mouse.move(
      boundingBox.x + boundingBox.width / 4,
      boundingBox.y + boundingBox.height / 2
    );
    await page.mouse.down();
    await page.mouse.move(
      boundingBox.x + boundingBox.width / 2,
      boundingBox.y + boundingBox.height / 4,
      { steps: 5 }
    );
    await page.mouse.move(
      boundingBox.x + boundingBox.width * 3 / 4,
      boundingBox.y + boundingBox.height / 2,
      { steps: 5 }
    );
    await page.mouse.up();

    // Check if the confirm button is enabled after drawing
    const confirmButton = await page.locator('button:has-text("Confirm Signature")');
    await expect(confirmButton).not.toBeDisabled();
  });

  test('should be able to change pen color and thickness', async ({ page }) => {
    // Mock authentication first
    await mockAuthentication(page);

    // Navigate to the agreement sign page
    await page.goto('/agreements/agreement-1/sign');

    // Wait for the loading to complete and page to load
    await page.waitForLoadState('networkidle');
    await page.waitForSelector('h2:has-text("Sign Agreement")', { timeout: 15000 });

    // Get the blue color button and click it
    const blueColorButton = await page.locator('button[title="Blue"]');
    await blueColorButton.click();

    // Get the thickness button for 5px and click it
    const thicknessButton = await page.locator('button:has-text("5")');
    await thicknessButton.click();

    // Check if the buttons have the selected state
    await expect(blueColorButton).toHaveClass(/ring-2/);
    await expect(thicknessButton).toHaveClass(/bg-primary/);
  });

  test('should be able to clear the signature', async ({ page }) => {
    // Mock authentication first
    await mockAuthentication(page);

    // Navigate to the agreement sign page
    await page.goto('/agreements/agreement-1/sign');

    // Wait for the loading to complete and page to load
    await page.waitForLoadState('networkidle');
    await page.waitForSelector('h2:has-text("Sign Agreement")', { timeout: 15000 });

    // Get the signature canvas
    const signatureCanvas = await page.locator('canvas').first();

    // Get the bounding box of the canvas
    const boundingBox = await signatureCanvas.boundingBox();
    if (!boundingBox) {
      throw new Error('Canvas bounding box not found');
    }

    // Draw a signature on the canvas
    await page.mouse.move(
      boundingBox.x + boundingBox.width / 4,
      boundingBox.y + boundingBox.height / 2
    );
    await page.mouse.down();
    await page.mouse.move(
      boundingBox.x + boundingBox.width * 3 / 4,
      boundingBox.y + boundingBox.height / 2,
      { steps: 5 }
    );
    await page.mouse.up();

    // Check if the confirm button is enabled after drawing
    const confirmButton = await page.locator('button:has-text("Confirm Signature")');
    await expect(confirmButton).not.toBeDisabled();

    // Click the clear button
    await page.locator('button:has-text("Clear")').click();

    // Check if the confirm button is disabled after clearing
    await expect(confirmButton).toBeDisabled();
  });

  test('should be able to submit the signature', async ({ page }) => {
    // Mock authentication first
    await mockAuthentication(page);

    // Navigate to the agreement sign page
    await page.goto('/agreements/agreement-1/sign');

    // Wait for the loading to complete and page to load
    await page.waitForLoadState('networkidle');
    await page.waitForSelector('h2:has-text("Sign Agreement")', { timeout: 15000 });

    // Get the signature canvas
    const signatureCanvas = await page.locator('canvas').first();

    // Get the bounding box of the canvas
    const boundingBox = await signatureCanvas.boundingBox();
    if (!boundingBox) {
      throw new Error('Canvas bounding box not found');
    }

    // Draw a signature on the canvas
    await page.mouse.move(
      boundingBox.x + boundingBox.width / 4,
      boundingBox.y + boundingBox.height / 2
    );
    await page.mouse.down();
    await page.mouse.move(
      boundingBox.x + boundingBox.width * 3 / 4,
      boundingBox.y + boundingBox.height / 2,
      { steps: 5 }
    );
    await page.mouse.up();

    // Check the agreement checkbox
    await page.locator('input[type="checkbox"]').check();

    // Click the confirm signature button
    await page.locator('button:has-text("Confirm Signature")').click();

    // Click the submit button
    await page.locator('button:has-text("Sign Agreement")').click();

    // Check if the success message is shown
    await page.waitForSelector('text=Agreement signed successfully');
  });
});
