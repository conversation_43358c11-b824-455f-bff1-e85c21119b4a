# Test Results: Business Accounts (2025-05-26_23-03)

## Summary
- **Total**: 18
- **Passed**: 0
- **Failed**: 18
- **Skipped**: 0
- **Duration**: 522789ms

## Test Details

### should navigate to business dashboard

**Status**: ❌ FAILED
**Duration**: 32878ms

**Error**:
```
TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

```

**Stack Trace**:
```
TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/e2e/business-accounts.spec.ts:13:16
```


**Steps**:
- Before Hooks: ⏭️ (undefinedms)
- beforeEach hook: ⏭️ (undefinedms)
- fixture: browser: ⏭️ (undefinedms)
- browserType.launch: ⏭️ (undefinedms)
- page.goto(/login): ✅ (556ms)
- page.fill(input[name="email"]): ⏭️ (undefinedms)

**Console Logs**:
```

```

### should create a new business account

**Status**: ❌ FAILED
**Duration**: 32658ms

**Error**:
```
TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

```

**Stack Trace**:
```
TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/e2e/business-accounts.spec.ts:13:16
```


**Steps**:
- Before Hooks: ❌ (32044ms)
  Error: TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

- beforeEach hook: ❌ (32043ms)
  Error: TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

- fixture: browser: ✅ (580ms)
- browserType.launch: ✅ (576ms)
- fixture: context: ✅ (75ms)
- browser.newContext: ✅ (72ms)
- fixture: page: ✅ (31401ms)
- browserContext.newPage: ⏭️ (undefinedms)
- page.fill(input[name="email"]): ❌ (30003ms)
  Error: TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

- After Hooks: ✅ (66ms)
- page.screenshot: ✅ (21ms)
- fixture: page: ⏭️ (undefinedms)
- fixture: context: ✅ (0ms)
- video.saveAs: ✅ (1ms)
- Worker Cleanup: ✅ (546ms)
- fixture: browser: ✅ (544ms)

**Console Logs**:
```

```

### should navigate to business select page

**Status**: ❌ FAILED
**Duration**: 31198ms

**Error**:
```
TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

```

**Stack Trace**:
```
TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/e2e/business-accounts.spec.ts:13:16
```


**Steps**:
- Before Hooks: ❌ (30612ms)
  Error: TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

- beforeEach hook: ❌ (30610ms)
  Error: TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

- fixture: context: ✅ (10ms)
- browser.newContext: ✅ (8ms)
- fixture: page: ✅ (30634ms)
- browserContext.newPage: ⏭️ (undefinedms)
- After Hooks: ✅ (77ms)
- page.screenshot: ✅ (30ms)
- fixture: page: ⏭️ (undefinedms)
- fixture: context: ✅ (0ms)
- video.saveAs: ✅ (1ms)
- Worker Cleanup: ✅ (505ms)
- fixture: browser: ✅ (504ms)

**Console Logs**:
```

```

### should invite a new member

**Status**: ❌ FAILED
**Duration**: 31114ms

**Error**:
```
TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

```

**Stack Trace**:
```
TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/e2e/business-accounts.spec.ts:13:16
```


**Steps**:
- Before Hooks: ⏭️ (undefinedms)
- beforeEach hook: ⏭️ (undefinedms)
- fixture: context: ✅ (7ms)
- browser.newContext: ✅ (4ms)
- fixture: page: ✅ (262ms)
- browserContext.newPage: ✅ (261ms)
- page.goto(/login): ✅ (279ms)
- page.fill(input[name="email"]): ⏭️ (undefinedms)

**Console Logs**:
```

```

### should navigate to business members page

**Status**: ❌ FAILED
**Duration**: 32805ms

**Error**:
```
TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

```

**Stack Trace**:
```
TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/e2e/business-accounts.spec.ts:13:16
```


**Steps**:
- Before Hooks: ❌ (32039ms)
  Error: TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

- beforeEach hook: ❌ (32036ms)
  Error: TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

- fixture: browser: ✅ (592ms)
- browserType.launch: ✅ (587ms)
- fixture: context: ✅ (98ms)
- browser.newContext: ✅ (94ms)
- fixture: page: ✅ (766ms)
- browserContext.newPage: ✅ (765ms)
- page.goto(/login): ✅ (548ms)
- page.fill(input[name="email"]): ❌ (30010ms)
  Error: TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

- After Hooks: ✅ (89ms)
- page.screenshot: ✅ (29ms)
- fixture: page: ✅ (1ms)
- fixture: context: ✅ (0ms)
- video.saveAs: ✅ (2ms)
- Worker Cleanup: ✅ (672ms)
- fixture: browser: ✅ (671ms)

**Console Logs**:
```

```

### should navigate to business settings page

**Status**: ❌ FAILED
**Duration**: 32328ms

**Error**:
```
TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

```

**Stack Trace**:
```
TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/e2e/business-accounts.spec.ts:13:16
```


**Steps**:
- Before Hooks: ❌ (31746ms)
  Error: TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

- beforeEach hook: ❌ (31744ms)
  Error: TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

- fixture: browser: ✅ (475ms)
- browserType.launch: ✅ (472ms)
- fixture: context: ✅ (76ms)
- browser.newContext: ✅ (73ms)
- fixture: page: ✅ (31211ms)
- browserContext.newPage: ⏭️ (undefinedms)
- After Hooks: ✅ (72ms)
- page.screenshot: ✅ (25ms)
- fixture: page: ⏭️ (undefinedms)
- fixture: context: ✅ (0ms)
- video.saveAs: ✅ (2ms)
- Worker Cleanup: ✅ (507ms)
- fixture: browser: ✅ (507ms)

**Console Logs**:
```

```

### should update business settings

**Status**: ❌ FAILED
**Duration**: 32579ms

**Error**:
```
TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

```

**Stack Trace**:
```
TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/e2e/business-accounts.spec.ts:13:16
```


**Steps**:
- Before Hooks: ❌ (31921ms)
  Error: TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

- beforeEach hook: ❌ (31917ms)
  Error: TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

- fixture: browser: ✅ (557ms)
- browserType.launch: ✅ (552ms)
- fixture: context: ✅ (68ms)
- browser.newContext: ✅ (65ms)
- fixture: page: ✅ (724ms)
- browserContext.newPage: ✅ (723ms)
- page.goto(/login): ✅ (541ms)
- page.fill(input[name="email"]): ❌ (30011ms)
  Error: TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

- After Hooks: ✅ (80ms)
- page.screenshot: ✅ (24ms)
- fixture: page: ✅ (0ms)
- fixture: context: ✅ (0ms)
- video.saveAs: ✅ (2ms)
- Worker Cleanup: ✅ (574ms)
- fixture: browser: ✅ (573ms)

**Console Logs**:
```

```

### should switch between business accounts

**Status**: ❌ FAILED
**Duration**: 32503ms

**Error**:
```
TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

```

**Stack Trace**:
```
TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/e2e/business-accounts.spec.ts:13:16
```


**Steps**:
- Before Hooks: ❌ (31806ms)
  Error: TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

- beforeEach hook: ❌ (31803ms)
  Error: TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

- fixture: browser: ✅ (527ms)
- browserType.launch: ✅ (525ms)
- fixture: context: ✅ (65ms)
- browser.newContext: ✅ (62ms)
- fixture: page: ✅ (693ms)
- browserContext.newPage: ✅ (691ms)
- page.goto(/login): ✅ (496ms)
- page.fill(input[name="email"]): ❌ (30007ms)
  Error: TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

- After Hooks: ✅ (79ms)
- page.screenshot: ✅ (26ms)
- fixture: page: ✅ (0ms)
- fixture: context: ✅ (0ms)
- video.saveAs: ✅ (2ms)
- Worker Cleanup: ✅ (615ms)
- fixture: browser: ✅ (613ms)

**Console Logs**:
```

```

### should switch to personal account

**Status**: ❌ FAILED
**Duration**: 32463ms

**Error**:
```
TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

```

**Stack Trace**:
```
TimeoutError: page.fill: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('input[name="email"]')[22m

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/e2e/business-accounts.spec.ts:13:16
```


**Steps**:
- Before Hooks: ⏭️ (undefinedms)
- beforeEach hook: ⏭️ (undefinedms)
- fixture: browser: ✅ (476ms)
- browserType.launch: ✅ (474ms)
- fixture: context: ✅ (61ms)
- browser.newContext: ✅ (58ms)
- fixture: page: ✅ (593ms)
- browserContext.newPage: ✅ (592ms)
- page.goto(/login): ✅ (502ms)
- page.fill(input[name="email"]): ⏭️ (undefinedms)

**Console Logs**:
```

```


## Root Cause Analysis


### Potential Issues

1. **Implementation Status**: Some features might not be fully implemented yet.
2. **Selector Issues**: The selectors used in the tests might not match the actual DOM structure.
3. **Timing Issues**: There might be animations or loading states causing timing problems.
4. **Navigation Issues**: The expected navigation behavior might not be implemented yet.

### Recommendations

1. Check if the features being tested are fully implemented.
2. Update the selectors if the DOM structure has changed.
3. Add proper waiting mechanisms for asynchronous operations.
4. Update the expected navigation behavior if it has changed.


## Next Steps

1. Fix the failing tests.
2. Add more tests to increase coverage.
3. Ensure that the tests are resilient to changes in the implementation.
