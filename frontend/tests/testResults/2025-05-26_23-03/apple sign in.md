# Test Results: Apple Sign In (2025-05-26_23-03)

## Summary
- **Total**: 6
- **Passed**: 0
- **Failed**: 6
- **Skipped**: 0
- **Duration**: 522788ms

## Test Details

### should display Apple Sign In button on login page

**Status**: ❌ FAILED
**Duration**: 32771ms

**Error**:
```
TimeoutError: page.waitForSelector: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('h1:has-text("Log In")') to be visible[22m

```

**Stack Trace**:
```
TimeoutError: page.waitForSelector: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('h1:has-text("Log In")') to be visible[22m

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/apple-signin.spec.js:13:16
```


**Steps**:
- Before Hooks: ✅ (1426ms)
- fixture: browser: ✅ (617ms)
- browserType.launch: ✅ (614ms)
- fixture: context: ✅ (83ms)
- browser.newContext: ✅ (80ms)
- fixture: page: ✅ (715ms)
- browserContext.newPage: ✅ (714ms)
- page.goto(/login): ✅ (513ms)
- page.waitForSelector(h1:has-text("Log In")): ⏭️ (undefinedms)

**Console Logs**:
```

```

### should handle Apple Sign In error gracefully

**Status**: ❌ FAILED
**Duration**: 32493ms

**Error**:
```
TimeoutError: page.waitForSelector: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('h1:has-text("Log In")') to be visible[22m

```

**Stack Trace**:
```
TimeoutError: page.waitForSelector: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('h1:has-text("Log In")') to be visible[22m

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/apple-signin.spec.js:29:16
```


**Steps**:
- Before Hooks: ⏭️ (undefinedms)
- fixture: browser: ✅ (526ms)
- browserType.launch: ✅ (524ms)
- fixture: context: ✅ (79ms)
- browser.newContext: ✅ (77ms)
- fixture: page: ✅ (31315ms)
- browserContext.newPage: ⏭️ (undefinedms)
- After Hooks: ✅ (82ms)
- page.screenshot: ✅ (30ms)
- fixture: page: ⏭️ (undefinedms)
- fixture: context: ✅ (0ms)
- video.saveAs: ✅ (1ms)
- Worker Cleanup: ✅ (515ms)
- fixture: browser: ✅ (515ms)

**Console Logs**:
```

```

### should redirect to the correct callback URL

**Status**: ❌ FAILED
**Duration**: 32322ms

**Error**:
```
TimeoutError: page.waitForSelector: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('h1:has-text("Log In")') to be visible[22m

```

**Stack Trace**:
```
TimeoutError: page.waitForSelector: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('h1:has-text("Log In")') to be visible[22m

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/apple-signin.spec.js:58:16
```


**Steps**:
- Before Hooks: ✅ (1218ms)
- fixture: browser: ✅ (532ms)
- browserType.launch: ✅ (530ms)
- fixture: context: ✅ (56ms)
- browser.newContext: ✅ (54ms)
- fixture: page: ✅ (620ms)
- browserContext.newPage: ✅ (620ms)
- page.goto(/login): ✅ (505ms)
- page.waitForSelector(h1:has-text("Log In")): ❌ (30005ms)
  Error: TimeoutError: page.waitForSelector: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('h1:has-text("Log In")') to be visible[22m

- After Hooks: ✅ (69ms)
- page.screenshot: ✅ (24ms)
- fixture: page: ✅ (0ms)
- fixture: context: ✅ (0ms)
- video.saveAs: ✅ (1ms)
- Worker Cleanup: ✅ (516ms)
- fixture: browser: ✅ (515ms)

**Console Logs**:
```

```


## Root Cause Analysis


### Potential Issues

1. **Implementation Status**: Some features might not be fully implemented yet.
2. **Selector Issues**: The selectors used in the tests might not match the actual DOM structure.
3. **Timing Issues**: There might be animations or loading states causing timing problems.
4. **Navigation Issues**: The expected navigation behavior might not be implemented yet.

### Recommendations

1. Check if the features being tested are fully implemented.
2. Update the selectors if the DOM structure has changed.
3. Add proper waiting mechanisms for asynchronous operations.
4. Update the expected navigation behavior if it has changed.


## Next Steps

1. Fix the failing tests.
2. Add more tests to increase coverage.
3. Ensure that the tests are resilient to changes in the implementation.
