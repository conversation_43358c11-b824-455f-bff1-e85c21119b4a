# Test info

- Name: Agreement Components >> should render the signature capture component
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/agreement-components.spec.js:19:3

# Error details

```
Error: expect(received).toBe<PERSON><PERSON>r<PERSON><PERSON>(expected)

Expected: > 0
Received:   0
    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/agreement-components.spec.js:40:44
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - link "List an Item":
    - /url: /items/new
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - link "Back to Agreement":
    - /url: /agreements/agreement-1
    - img
    - text: Back to Agreement
  - heading "Sign Agreement" [level=2]
  - paragraph: As the Renter, please review and sign the agreement below.
  - heading "Your Signature" [level=3]
  - paragraph: Please sign in the box below using your mouse or touch screen.
  - text: "Pen:"
  - button "Color":
    - text: Color
    - img
  - button "Size":
    - text: Size
    - img
  - button "Clear":
    - img
    - text: Clear
  - paragraph: Sign here
  - button "Confirm Signature" [disabled]:
    - img
    - text: Confirm Signature
  - paragraph: Please sign above using your mouse or touch screen
  - checkbox "I have read and agree to the terms and conditions outlined in this agreement. I understand that by signing this document, I am entering into a legally binding contract."
  - text: I have read and agree to the terms and conditions outlined in this agreement. I understand that by signing this document, I am entering into a legally binding contract.
  - button "Sign Agreement" [disabled]
  - heading "Legal Information" [level=3]
  - paragraph: Your electronic signature is legally binding in accordance with the Electronic Signatures in Global and National Commerce Act (E-SIGN Act) and the Uniform Electronic Transactions Act (UETA).
  - paragraph: A cryptographic hash of this agreement and your signature will be stored securely to ensure the integrity and authenticity of this document.
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
   1 | // @ts-check
   2 | import { test, expect } from '@playwright/test';
   3 |
   4 | test.describe('Agreement Components', () => {
   5 |   // Helper function to mock authentication
   6 |   async function mockAuthentication(page) {
   7 |     // Mock localStorage to simulate being logged in
   8 |     await page.addInitScript(() => {
   9 |       localStorage.setItem('auth_token', 'mock_token');
   10 |       localStorage.setItem('user', JSON.stringify({
   11 |         id: 'user-2',
   12 |         email: '<EMAIL>',
   13 |         name: 'Test User',
   14 |         role: 'user'
   15 |       }));
   16 |     });
   17 |   }
   18 |
   19 |   test('should render the signature capture component', async ({ page }) => {
   20 |     // Mock authentication first
   21 |     await mockAuthentication(page);
   22 |
   23 |     // Navigate to the agreement sign page
   24 |     await page.goto('/agreements/agreement-1/sign');
   25 |
   26 |     // Wait for the loading to complete and page to load
   27 |     await page.waitForLoadState('networkidle');
   28 |     await page.waitForSelector('h2:has-text("Sign Agreement")', { timeout: 15000 });
   29 |
   30 |     // Check if signature canvas is rendered
   31 |     const signatureCanvas = await page.locator('canvas').first();
   32 |     await expect(signatureCanvas).toBeVisible();
   33 |
   34 |     // Check if pen color options are rendered
   35 |     const colorButtons = await page.locator('button[title]').filter({ hasText: '' });
   36 |     expect(await colorButtons.count()).toBeGreaterThan(0);
   37 |
   38 |     // Check if pen thickness options are rendered
   39 |     const thicknessButtons = await page.locator('button').filter({ hasText: /^[1-9]$/ });
>  40 |     expect(await thicknessButtons.count()).toBeGreaterThan(0);
      |                                            ^ Error: expect(received).toBeGreaterThan(expected)
   41 |
   42 |     // Check if clear and confirm buttons are rendered
   43 |     await expect(page.locator('button:has-text("Clear")')).toBeVisible();
   44 |     await expect(page.locator('button:has-text("Confirm Signature")')).toBeVisible();
   45 |   });
   46 |
   47 |   test('should be able to draw on the signature canvas', async ({ page }) => {
   48 |     // Mock authentication first
   49 |     await mockAuthentication(page);
   50 |
   51 |     // Navigate to the agreement sign page
   52 |     await page.goto('/agreements/agreement-1/sign');
   53 |
   54 |     // Wait for the loading to complete and page to load
   55 |     await page.waitForLoadState('networkidle');
   56 |     await page.waitForSelector('h2:has-text("Sign Agreement")', { timeout: 15000 });
   57 |
   58 |     // Get the signature canvas
   59 |     const signatureCanvas = await page.locator('canvas').first();
   60 |
   61 |     // Get the bounding box of the canvas
   62 |     const boundingBox = await signatureCanvas.boundingBox();
   63 |     if (!boundingBox) {
   64 |       throw new Error('Canvas bounding box not found');
   65 |     }
   66 |
   67 |     // Draw a signature on the canvas
   68 |     await page.mouse.move(
   69 |       boundingBox.x + boundingBox.width / 4,
   70 |       boundingBox.y + boundingBox.height / 2
   71 |     );
   72 |     await page.mouse.down();
   73 |     await page.mouse.move(
   74 |       boundingBox.x + boundingBox.width / 2,
   75 |       boundingBox.y + boundingBox.height / 4,
   76 |       { steps: 5 }
   77 |     );
   78 |     await page.mouse.move(
   79 |       boundingBox.x + boundingBox.width * 3 / 4,
   80 |       boundingBox.y + boundingBox.height / 2,
   81 |       { steps: 5 }
   82 |     );
   83 |     await page.mouse.up();
   84 |
   85 |     // Check if the confirm button is enabled after drawing
   86 |     const confirmButton = await page.locator('button:has-text("Confirm Signature")');
   87 |     await expect(confirmButton).not.toBeDisabled();
   88 |   });
   89 |
   90 |   test('should be able to change pen color and thickness', async ({ page }) => {
   91 |     // Mock authentication first
   92 |     await mockAuthentication(page);
   93 |
   94 |     // Navigate to the agreement sign page
   95 |     await page.goto('/agreements/agreement-1/sign');
   96 |
   97 |     // Wait for the loading to complete and page to load
   98 |     await page.waitForLoadState('networkidle');
   99 |     await page.waitForSelector('h2:has-text("Sign Agreement")', { timeout: 15000 });
  100 |
  101 |     // Get the blue color button and click it
  102 |     const blueColorButton = await page.locator('button[title="Blue"]');
  103 |     await blueColorButton.click();
  104 |
  105 |     // Get the thickness button for 5px and click it
  106 |     const thicknessButton = await page.locator('button:has-text("5")');
  107 |     await thicknessButton.click();
  108 |
  109 |     // Check if the buttons have the selected state
  110 |     await expect(blueColorButton).toHaveClass(/ring-2/);
  111 |     await expect(thicknessButton).toHaveClass(/bg-primary/);
  112 |   });
  113 |
  114 |   test('should be able to clear the signature', async ({ page }) => {
  115 |     // Mock authentication first
  116 |     await mockAuthentication(page);
  117 |
  118 |     // Navigate to the agreement sign page
  119 |     await page.goto('/agreements/agreement-1/sign');
  120 |
  121 |     // Wait for the loading to complete and page to load
  122 |     await page.waitForLoadState('networkidle');
  123 |     await page.waitForSelector('h2:has-text("Sign Agreement")', { timeout: 15000 });
  124 |
  125 |     // Get the signature canvas
  126 |     const signatureCanvas = await page.locator('canvas').first();
  127 |
  128 |     // Get the bounding box of the canvas
  129 |     const boundingBox = await signatureCanvas.boundingBox();
  130 |     if (!boundingBox) {
  131 |       throw new Error('Canvas bounding box not found');
  132 |     }
  133 |
  134 |     // Draw a signature on the canvas
  135 |     await page.mouse.move(
  136 |       boundingBox.x + boundingBox.width / 4,
  137 |       boundingBox.y + boundingBox.height / 2
  138 |     );
  139 |     await page.mouse.down();
  140 |     await page.mouse.move(
```