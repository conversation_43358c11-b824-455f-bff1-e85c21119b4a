# Test info

- Name: Agreement Components >> should be able to submit the signature
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/agreement-components.spec.js:158:3

# Error details

```
Error: page.waitForSelector: Test ended.
Call log:
  - waiting for locator('h2:has-text("Sign Agreement")') to be visible

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/agreement-components.spec.js:167:16
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - link "List an Item":
    - /url: /items/new
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
- text: "[plugin:@tailwindcss/vite:generate:serve] Cannot apply unknown utility class: p-12 /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/src/pages/Login/styles/login.css at onInvalidCandidate (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:18:410) at de (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:13:29693) at file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:18:373 at D (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:3:1655) at Oe (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:18:172) at Qr (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:35:828) at async pn (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:35:1123) at async xa (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:35:1898) at async Sa (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/@tailwindcss/node/dist/index.mjs:10:3433) at async C.generate (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/@tailwindcss/vite/dist/index.mjs:1:3581) at async TransformPluginContext.transform (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/@tailwindcss/vite/dist/index.mjs:1:1959) at async EnvironmentPluginContainer.transform (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:42294:18) at async loadAndTransform (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:35735:27 Click outside, press Esc key, or fix the code to dismiss. You can also disable this overlay by setting"
- code: server.hmr.overlay
- text: to
- code: "false"
- text: in
- code: vite.config.ts
- text: .
```

# Test source

```ts
   67 |     // Draw a signature on the canvas
   68 |     await page.mouse.move(
   69 |       boundingBox.x + boundingBox.width / 4,
   70 |       boundingBox.y + boundingBox.height / 2
   71 |     );
   72 |     await page.mouse.down();
   73 |     await page.mouse.move(
   74 |       boundingBox.x + boundingBox.width / 2,
   75 |       boundingBox.y + boundingBox.height / 4,
   76 |       { steps: 5 }
   77 |     );
   78 |     await page.mouse.move(
   79 |       boundingBox.x + boundingBox.width * 3 / 4,
   80 |       boundingBox.y + boundingBox.height / 2,
   81 |       { steps: 5 }
   82 |     );
   83 |     await page.mouse.up();
   84 |
   85 |     // Check if the confirm button is enabled after drawing
   86 |     const confirmButton = await page.locator('button:has-text("Confirm Signature")');
   87 |     await expect(confirmButton).not.toBeDisabled();
   88 |   });
   89 |
   90 |   test('should be able to change pen color and thickness', async ({ page }) => {
   91 |     // Mock authentication first
   92 |     await mockAuthentication(page);
   93 |
   94 |     // Navigate to the agreement sign page
   95 |     await page.goto('/agreements/agreement-1/sign');
   96 |
   97 |     // Wait for the loading to complete and page to load
   98 |     await page.waitForLoadState('networkidle');
   99 |     await page.waitForSelector('h2:has-text("Sign Agreement")', { timeout: 15000 });
  100 |
  101 |     // Get the blue color button and click it
  102 |     const blueColorButton = await page.locator('button[title="Blue"]');
  103 |     await blueColorButton.click();
  104 |
  105 |     // Get the thickness button for 5px and click it
  106 |     const thicknessButton = await page.locator('button:has-text("5")');
  107 |     await thicknessButton.click();
  108 |
  109 |     // Check if the buttons have the selected state
  110 |     await expect(blueColorButton).toHaveClass(/ring-2/);
  111 |     await expect(thicknessButton).toHaveClass(/bg-primary/);
  112 |   });
  113 |
  114 |   test('should be able to clear the signature', async ({ page }) => {
  115 |     // Mock authentication first
  116 |     await mockAuthentication(page);
  117 |
  118 |     // Navigate to the agreement sign page
  119 |     await page.goto('/agreements/agreement-1/sign');
  120 |
  121 |     // Wait for the loading to complete and page to load
  122 |     await page.waitForLoadState('networkidle');
  123 |     await page.waitForSelector('h2:has-text("Sign Agreement")', { timeout: 15000 });
  124 |
  125 |     // Get the signature canvas
  126 |     const signatureCanvas = await page.locator('canvas').first();
  127 |
  128 |     // Get the bounding box of the canvas
  129 |     const boundingBox = await signatureCanvas.boundingBox();
  130 |     if (!boundingBox) {
  131 |       throw new Error('Canvas bounding box not found');
  132 |     }
  133 |
  134 |     // Draw a signature on the canvas
  135 |     await page.mouse.move(
  136 |       boundingBox.x + boundingBox.width / 4,
  137 |       boundingBox.y + boundingBox.height / 2
  138 |     );
  139 |     await page.mouse.down();
  140 |     await page.mouse.move(
  141 |       boundingBox.x + boundingBox.width * 3 / 4,
  142 |       boundingBox.y + boundingBox.height / 2,
  143 |       { steps: 5 }
  144 |     );
  145 |     await page.mouse.up();
  146 |
  147 |     // Check if the confirm button is enabled after drawing
  148 |     const confirmButton = await page.locator('button:has-text("Confirm Signature")');
  149 |     await expect(confirmButton).not.toBeDisabled();
  150 |
  151 |     // Click the clear button
  152 |     await page.locator('button:has-text("Clear")').click();
  153 |
  154 |     // Check if the confirm button is disabled after clearing
  155 |     await expect(confirmButton).toBeDisabled();
  156 |   });
  157 |
  158 |   test('should be able to submit the signature', async ({ page }) => {
  159 |     // Mock authentication first
  160 |     await mockAuthentication(page);
  161 |
  162 |     // Navigate to the agreement sign page
  163 |     await page.goto('/agreements/agreement-1/sign');
  164 |
  165 |     // Wait for the loading to complete and page to load
  166 |     await page.waitForLoadState('networkidle');
> 167 |     await page.waitForSelector('h2:has-text("Sign Agreement")', { timeout: 15000 });
      |                ^ Error: page.waitForSelector: Test ended.
  168 |
  169 |     // Get the signature canvas
  170 |     const signatureCanvas = await page.locator('canvas').first();
  171 |
  172 |     // Get the bounding box of the canvas
  173 |     const boundingBox = await signatureCanvas.boundingBox();
  174 |     if (!boundingBox) {
  175 |       throw new Error('Canvas bounding box not found');
  176 |     }
  177 |
  178 |     // Draw a signature on the canvas
  179 |     await page.mouse.move(
  180 |       boundingBox.x + boundingBox.width / 4,
  181 |       boundingBox.y + boundingBox.height / 2
  182 |     );
  183 |     await page.mouse.down();
  184 |     await page.mouse.move(
  185 |       boundingBox.x + boundingBox.width * 3 / 4,
  186 |       boundingBox.y + boundingBox.height / 2,
  187 |       { steps: 5 }
  188 |     );
  189 |     await page.mouse.up();
  190 |
  191 |     // Check the agreement checkbox
  192 |     await page.locator('input[type="checkbox"]').check();
  193 |
  194 |     // Click the confirm signature button
  195 |     await page.locator('button:has-text("Confirm Signature")').click();
  196 |
  197 |     // Click the submit button
  198 |     await page.locator('button:has-text("Sign Agreement")').click();
  199 |
  200 |     // Check if the success message is shown
  201 |     await page.waitForSelector('text=Agreement signed successfully');
  202 |   });
  203 | });
  204 |
```