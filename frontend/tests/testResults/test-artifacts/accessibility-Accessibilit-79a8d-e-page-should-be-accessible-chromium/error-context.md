# Test info

- Name: Accessibility Tests >> home page should be accessible
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/accessibility.spec.js:9:3

# Error details

```
Error: locator.evaluate: Test ended.
Call log:
  - waiting for locator('nav a').nth(1)
    - locator resolved to visible <a href="/contact" title="navigation.contactUs" class="text-white font-medium">navigation.contactUs</a>

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/accessibility.spec.js:39:35
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - heading "The Community Marketplace for Endless Shared Possibilities" [level=1]
  - paragraph: Rent anything you need, share what you own. Join thousands of people saving money and reducing waste in your community.
  - textbox "What would you like to rent today?"
  - button:
    - img
  - text: CLAIM THIS OFFER NOW
  - heading "New Bluetooth Calling Smart Watch, 550" [level=2]
  - paragraph: Activity Tracker, Calorie Tracker
  - link "RENT NOW":
    - /url: /search?category=smart-watches
  - button "Go to slide 1"
  - button "Go to slide 2"
  - button "Go to slide 3"
  - img "New Bluetooth Calling Smart Watch, 550"
  - text: 15% CASH BACK
  - heading "Apple iPhone 14 Pro Max" [level=3]
  - paragraph: Retina XDR Display
  - link "RENT NOW":
    - /url: /search?category=smartphones
  - img "iPhone 14 Pro Max"
  - text: BEST DISCOUNTS
  - heading "Meta Oculus Quest 128GB" [level=3]
  - paragraph: High Resolution
  - link "RENT NOW":
    - /url: /search?category=vr-headsets
  - img "Meta Oculus Quest"
  - img
  - paragraph: Android TV
  - img
  - paragraph: Speakers
  - img
  - paragraph: Cameras
  - img
  - paragraph: Mobile
  - img
  - paragraph: New Laptop
  - img
  - paragraph: Smart Watches
  - region "Browse by Category":
    - text: Explore Our Categories
    - heading "Browse by Category" [level=2]
    - paragraph: Discover thousands of items available for rent across our diverse categories
    - text: Failed to load categories. Using fallback data.
    - link "Medical & Mobility Equipment category with 8 subcategories":
      - /url: /search?category=medical-equipment
      - heading "Medical & Mobility Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Medical devices, mobility aids, and healthcare equipment
      - img
    - link "Vehicles & Transportation category with 8 subcategories":
      - /url: /search?category=vehicles
      - heading "Vehicles & Transportation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Cars, motorcycles, boats, bicycles, and other modes of transportation
      - img
    - link "Real Estate & Spaces category with 8 subcategories":
      - /url: /search?category=real-estate
      - heading "Real Estate & Spaces" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Residential properties, commercial spaces, event venues, and more
      - img
    - link "Electronics & Technology category with 8 subcategories":
      - /url: /search?category=electronics
      - heading "Electronics & Technology" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Computers, smartphones, audio equipment, and other electronic devices
      - img
    - link "Home & Furniture category with 8 subcategories":
      - /url: /search?category=home-furniture
      - heading "Home & Furniture" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Furniture, appliances, home decor, and household items
      - img
    - link "Tools & Equipment category with 8 subcategories":
      - /url: /search?category=tools-equipment
      - heading "Tools & Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Power tools, hand tools, construction equipment, and specialized tools
      - img
    - link "Sports & Recreation category with 8 subcategories":
      - /url: /search?category=sports-recreation
      - heading "Sports & Recreation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Sports equipment, outdoor gear, fitness equipment, and recreational items
      - img
    - link "Fashion & Accessories category with 8 subcategories":
      - /url: /search?category=fashion
      - heading "Fashion & Accessories" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Clothing, accessories, jewelry, and fashion items
      - img
    - link "View all categories":
      - /url: /search
      - text: View All Categories
      - img
  - text: Featured Rentals
  - heading "Featured Items" [level=2]
  - paragraph: Discover our most popular and highly-rated rental items
  - img "Power Drill"
  - link "Quick view of this item":
    - /url: /items/1
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/1
    - img
  - text: Rent-to-Buy 92 • Excellent Tools & Equipment
  - heading "Power Drill" [level=3]:
    - link "Power Drill":
      - /url: /items/1
  - img
  - text: By John D. •
  - img
  - text: 4.5 (12) $25.00 / day RTB
  - img
  - text: "Downtown • 3 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/1
    - text: Rent Now
    - img
  - img "Mountain Bike"
  - link "Quick view of this item":
    - /url: /items/2
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/2
    - img
  - text: 86 • Very Good Tools & Equipment
  - heading "Mountain Bike" [level=3]:
    - link "Mountain Bike":
      - /url: /items/2
  - img
  - text: By John D. •
  - img
  - text: 5.0 (8) $15.00 / day
  - img
  - text: "Westside • 5 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/2
    - text: Rent Now
    - img
  - img "Projector"
  - link "Quick view of this item":
    - /url: /items/3
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/3
    - img
  - text: 87 • Very Good Tools & Equipment
  - heading "Projector" [level=3]:
    - link "Projector":
      - /url: /items/3
  - img
  - text: By John D. •
  - img
  - text: 3.5 (3) $40.00 / day
  - img
  - text: "Eastside • 5 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/3
    - text: Rent Now
    - img
  - img "Camping Tent"
  - link "Quick view of this item":
    - /url: /items/4
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/4
    - img
  - text: 86 • Very Good Tools & Equipment
  - heading "Camping Tent" [level=3]:
    - link "Camping Tent":
      - /url: /items/4
  - img
  - text: By John D. •
  - img
  - text: 4.5 (15) $20.00 / day
  - img
  - text: "Northside • 3 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/4
    - text: Rent Now
    - img
  - link "View all items":
    - /url: /search
    - text: View All Items
    - img
  - heading "Rent with Confidence" [level=2]
  - paragraph: Our Item Reliability & History System ensures you always know the condition and history of what you're renting
  - img
  - heading "Reliability Scoring" [level=3]
  - paragraph: Every item receives a reliability score based on its history, maintenance records, and testing frequency.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Maintenance Tracking" [level=3]
  - paragraph: View detailed maintenance records and testing history for every item before you rent.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Verified Reviews" [level=3]
  - paragraph: All reviews come from verified renters who have actually used the item, ensuring honest feedback.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - heading "What Our Community Says" [level=3]
  - heading "Sarah K." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"The reliability score gave me confidence to rent an expensive camera for my daughter's wedding. It worked perfectly and saved us thousands of dollars!\""
  - heading "Michael T." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"As someone who rents medical equipment for my mother, the maintenance records and testing history give me peace of mind that everything will work properly.\""
  - heading "Start Saving Today!" [level=2]
  - paragraph: Join thousands of smart renters and lenders in your community. List your first item in minutes.
  - link "Get Started":
    - /url: /register
  - link "How It Works":
    - /url: /how-it-works
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
- text: "[plugin:@tailwindcss/vite:generate:serve] Cannot apply unknown utility class: p-12 /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/src/pages/Login/styles/login.css at onInvalidCandidate (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:18:410) at de (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:13:29693) at file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:18:373 at D (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:3:1655) at Oe (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:18:172) at Qr (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:35:828) at async pn (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:35:1123) at async xa (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:35:1898) at async Sa (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/@tailwindcss/node/dist/index.mjs:10:3433) at async C.generate (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/@tailwindcss/vite/dist/index.mjs:1:3581) at async TransformPluginContext.transform (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/@tailwindcss/vite/dist/index.mjs:1:1959) at async EnvironmentPluginContainer.transform (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:42294:18) at async loadAndTransform (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:35735:27 Click outside, press Esc key, or fix the code to dismiss. You can also disable this overlay by setting"
- code: server.hmr.overlay
- text: to
- code: "false"
- text: in
- code: vite.config.ts
- text: .
```

# Test source

```ts
   1 | // @ts-check
   2 | import { test, expect } from '@playwright/test';
   3 |
   4 | /**
   5 |  * Comprehensive accessibility test suite for RentUp
   6 |  * Tests WCAG 2.1 AA compliance across key pages
   7 |  */
   8 | test.describe('Accessibility Tests', () => {
   9 |   test('home page should be accessible', async ({ page }) => {
   10 |     await page.goto('/');
   11 |
   12 |     // Wait for the page to fully load
   13 |     await page.waitForLoadState('networkidle');
   14 |
   15 |     // Wait for critical elements to be visible with longer timeout
   16 |     await page.waitForSelector('header', { state: 'visible', timeout: 15000 });
   17 |     await page.waitForSelector('main', { state: 'visible', timeout: 15000 });
   18 |     await page.waitForSelector('footer', { state: 'visible', timeout: 15000 });
   19 |
   20 |     // Check specific fixes
   21 |
   22 |     // 1. Skip link should target an element with id="main-content"
   23 |     const skipLink = await page.locator('a[href="#main-content"]');
   24 |     await expect(skipLink).toBeVisible({ visible: 'hidden' }); // Should be visually hidden but present
   25 |
   26 |     const mainContent = await page.locator('#main-content');
   27 |     await expect(mainContent).toBeVisible();
   28 |
   29 |     // 2. Navigation links should have sufficient contrast
   30 |     const navLinks = await page.locator('nav a');
   31 |     for (let i = 0; i < await navLinks.count(); i++) {
   32 |       const link = navLinks.nth(i);
   33 |       const color = await link.evaluate(el => {
   34 |         return window.getComputedStyle(el).color;
   35 |       });
   36 |
   37 |       // We don't have a way to directly test contrast ratio in Playwright
   38 |       // But we can check that we're not using the problematic color class
>  39 |       const hasClass = await link.evaluate(el => {
      |                                   ^ Error: locator.evaluate: Test ended.
   40 |         return el.classList.contains('text-dark-gray');
   41 |       });
   42 |
   43 |       expect(hasClass).toBeFalsy();
   44 |     }
   45 |
   46 |     // 3. Social media links should have accessible names
   47 |     const socialLinks = await page.locator('footer a[href*="facebook"], footer a[href*="instagram"], footer a[href*="tiktok"], footer a[href*="x.com"]');
   48 |
   49 |     for (let i = 0; i < await socialLinks.count(); i++) {
   50 |       const link = socialLinks.nth(i);
   51 |
   52 |       // Check for aria-label
   53 |       const ariaLabel = await link.getAttribute('aria-label');
   54 |       expect(ariaLabel).toBeTruthy();
   55 |
   56 |       // Check for sr-only text
   57 |       const hasSrOnlyText = await link.evaluate(el => {
   58 |         return el.querySelector('.sr-only') !== null;
   59 |       });
   60 |
   61 |       expect(hasSrOnlyText).toBeTruthy();
   62 |     }
   63 |   });
   64 |
   65 |   test('search page should be accessible', async ({ page }) => {
   66 |     await page.goto('/search');
   67 |
   68 |     // Wait for critical elements to be visible
   69 |     await page.waitForSelector('header', { state: 'visible' });
   70 |     await page.waitForSelector('main', { state: 'visible' });
   71 |
   72 |     // Check navigation links for proper contrast
   73 |     const navLinks = await page.locator('nav a');
   74 |     for (let i = 0; i < await navLinks.count(); i++) {
   75 |       const link = navLinks.nth(i);
   76 |
   77 |       // Check that we're not using the problematic color class
   78 |       const hasClass = await link.evaluate(el => {
   79 |         return el.classList.contains('text-dark-gray');
   80 |       });
   81 |
   82 |       expect(hasClass).toBeFalsy();
   83 |     }
   84 |   });
   85 |
   86 |   test('should use proper heading structure', async ({ page }) => {
   87 |     await page.goto('/');
   88 |
   89 |     // Get all headings
   90 |     const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
   91 |
   92 |     // Create a map of heading levels
   93 |     const headingLevels = [];
   94 |     for (const heading of headings) {
   95 |       const tagName = await heading.evaluate(el => el.tagName.toLowerCase());
   96 |       const level = parseInt(tagName.substring(1));
   97 |       headingLevels.push(level);
   98 |     }
   99 |
  100 |     // Check that heading levels don't skip (e.g., h1 -> h3)
  101 |     for (let i = 0; i < headingLevels.length - 1; i++) {
  102 |       const current = headingLevels[i];
  103 |       const next = headingLevels[i + 1];
  104 |
  105 |       // Next heading should not be more than one level deeper
  106 |       if (next > current) {
  107 |         expect(next - current).toBeLessThanOrEqual(1);
  108 |       }
  109 |     }
  110 |   });
  111 |
  112 |   test('should have proper document language', async ({ page }) => {
  113 |     await page.goto('/');
  114 |
  115 |     // Check that html has lang attribute
  116 |     const htmlLang = await page.locator('html').getAttribute('lang');
  117 |     expect(htmlLang).toBeTruthy();
  118 |     expect(htmlLang).toEqual('en');
  119 |   });
  120 | });
  121 |
```