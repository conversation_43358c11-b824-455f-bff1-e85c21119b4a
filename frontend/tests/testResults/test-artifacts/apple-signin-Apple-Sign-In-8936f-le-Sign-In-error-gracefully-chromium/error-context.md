# Test info

- Name: Apple Sign In >> should handle Apple Sign In error gracefully
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/apple-signin.spec.js:24:3

# Error details

```
Error: page.waitForSelector: Test ended.
Call log:
  - waiting for locator('h1:has-text("Log In")') to be visible

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/apple-signin.spec.js:29:16
```

# Page snapshot

```yaml
- img
- heading "Something went wrong" [level=2]
- paragraph: We're sorry, but an error occurred while rendering this page. Our team has been notified.
- button "Reload Page"
- link "Go to Home":
  - /url: /
- text: "[plugin:@tailwindcss/vite:generate:serve] Cannot apply unknown utility class: p-12 /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/src/pages/Login/styles/login.css at onInvalidCandidate (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:18:410) at de (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:13:29693) at file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:18:373 at D (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:3:1655) at Oe (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:18:172) at Qr (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:35:828) at async pn (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:35:1123) at async xa (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/tailwindcss/dist/chunk-CCUDLJWE.mjs:35:1898) at async Sa (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/@tailwindcss/node/dist/index.mjs:10:3433) at async C.generate (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/@tailwindcss/vite/dist/index.mjs:1:3581) at async TransformPluginContext.transform (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/@tailwindcss/vite/dist/index.mjs:1:1959) at async EnvironmentPluginContainer.transform (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:42294:18) at async loadAndTransform (file:///home/<USER>/Documents/agentLabsNetwork/rentup/frontend/node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:35735:27 Click outside, press Esc key, or fix the code to dismiss. You can also disable this overlay by setting"
- code: server.hmr.overlay
- text: to
- code: "false"
- text: in
- code: vite.config.ts
- text: .
```

# Test source

```ts
   1 | // @ts-check
   2 | import { test, expect } from '@playwright/test';
   3 |
   4 | /**
   5 |  * Test suite for Apple Sign In functionality
   6 |  */
   7 | test.describe('Apple Sign In', () => {
   8 |   test('should display Apple Sign In button on login page', async ({ page }) => {
   9 |     // Navigate to the login page
  10 |     await page.goto('/login');
  11 |
  12 |     // Wait for the page to load
  13 |     await page.waitForSelector('h1:has-text("Log In")');
  14 |
  15 |     // Check if the Apple Sign In button is visible
  16 |     const appleButton = page.locator('button:has-text("Continue with Apple")');
  17 |     await expect(appleButton).toBeVisible();
  18 |
  19 |     // Check if the Apple logo is visible in the button
  20 |     const appleLogo = appleButton.locator('svg');
  21 |     await expect(appleLogo).toBeVisible();
  22 |   });
  23 |
  24 |   test('should handle Apple Sign In error gracefully', async ({ page }) => {
  25 |     // Navigate to the login page
  26 |     await page.goto('/login');
  27 |
  28 |     // Wait for the page to load
> 29 |     await page.waitForSelector('h1:has-text("Log In")');
     |                ^ Error: page.waitForSelector: Test ended.
  30 |
  31 |     // Mock the Apple Sign In error
  32 |     await page.evaluate(() => {
  33 |       // Create a custom event that will be triggered when the Apple Sign In button is clicked
  34 |       window.addEventListener('click', (event) => {
  35 |         // Check if the clicked element is the Apple Sign In button
  36 |         if (event.target.closest('button') && event.target.closest('button').textContent.includes('Continue with Apple')) {
  37 |           // Simulate an error by dispatching a custom event
  38 |           const errorEvent = new CustomEvent('AppleIDSignInOnFailure', {
  39 |             detail: { error: 'Apple Sign In failed' }
  40 |           });
  41 |           window.dispatchEvent(errorEvent);
  42 |         }
  43 |       }, { capture: true });
  44 |     });
  45 |
  46 |     // Click the Apple Sign In button
  47 |     await page.click('button:has-text("Continue with Apple")');
  48 |
  49 |     // Check if the error message is displayed
  50 |     await page.waitForSelector('div.bg-red-50:has-text("Apple login failed")');
  51 |   });
  52 |
  53 |   test('should redirect to the correct callback URL', async ({ page }) => {
  54 |     // Navigate to the login page
  55 |     await page.goto('/login');
  56 |
  57 |     // Wait for the page to load
  58 |     await page.waitForSelector('h1:has-text("Log In")');
  59 |
  60 |     // Get the Apple Sign In button
  61 |     const appleButton = page.locator('button:has-text("Continue with Apple")');
  62 |
  63 |     // Check if the button has the correct redirect URI
  64 |     const redirectUri = await page.evaluate(() => {
  65 |       // This is a simplified check - in a real test, you would need to inspect the actual request
  66 |       const authConfig = window.authConfig || {};
  67 |       return authConfig.apple?.redirectUri;
  68 |     });
  69 |
  70 |     // The test will pass if redirectUri is undefined since we can't access window.authConfig in this test
  71 |     // In a real test, you would verify that the redirect URI is correct
  72 |     console.log('Redirect URI:', redirectUri);
  73 |   });
  74 | });
  75 |
```