/**
 * Detailed Test Reporter
 *
 * This reporter generates detailed reports with expected vs. actual results.
 * It helps pinpoint root causes of test failures by comparing expected behavior with actual behavior.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Define paths for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const testResultsDir = path.join(__dirname, '..', 'testResults');

// Create directories if they don't exist
if (!fs.existsSync(testResultsDir)) {
  fs.mkdirSync(testResultsDir, { recursive: true });
}

// Helper function to format date for filenames
function formatDate() {
  const now = new Date();
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}-${String(now.getMinutes()).padStart(2, '0')}`;
}

/**
 * Detailed Reporter class
 */
class DetailedReporter {
  constructor() {
    this.results = {};
    this.startTime = new Date();
    this.dateStr = formatDate();
    this.testCases = {};
    this.currentTest = null;
    this.logs = {};
  }

  /**
   * Called when the test run begins
   */
  onBegin(config, suite) {
    console.log(`Starting the run with ${suite.allTests().length} tests`);

    // Create test results directory for this run
    this.runDir = path.join(testResultsDir, this.dateStr);
    if (!fs.existsSync(this.runDir)) {
      fs.mkdirSync(this.runDir, { recursive: true });
    }
  }

  /**
   * Called when a test begins
   */
  onTestBegin(test) {
    console.log(`Starting test: ${test.title}`);
    this.currentTest = test;
    this.logs[test.id] = [];

    // Initialize test case
    if (!this.testCases[test.parent.title]) {
      this.testCases[test.parent.title] = {};
    }

    this.testCases[test.parent.title][test.title] = {
      status: 'running',
      startTime: new Date(),
      endTime: null,
      duration: 0,
      error: null,
      logs: [],
      steps: []
    };
  }

  /**
   * Called when a test step begins
   */
  onStepBegin(test, result, step) {
    if (this.currentTest && this.testCases[test.parent.title] && this.testCases[test.parent.title][test.title]) {
      this.testCases[test.parent.title][test.title].steps.push({
        title: step.title,
        status: 'running',
        startTime: new Date()
      });
    }
  }

  /**
   * Called when a test step ends
   */
  onStepEnd(test, result, step) {
    if (this.currentTest && this.testCases[test.parent.title] && this.testCases[test.parent.title][test.title]) {
      const steps = this.testCases[test.parent.title][test.title].steps;
      const stepIndex = steps.findIndex(s => s.title === step.title && s.status === 'running');

      if (stepIndex !== -1) {
        steps[stepIndex].status = step.error ? 'failed' : 'passed';
        steps[stepIndex].endTime = new Date();
        steps[stepIndex].duration = steps[stepIndex].endTime - steps[stepIndex].startTime;
        steps[stepIndex].error = step.error;
      }
    }
  }

  /**
   * Called when a test ends
   */
  onTestEnd(test, result) {
    console.log(`Finished test: ${test.title} (${result.status})`);

    if (this.testCases[test.parent.title] && this.testCases[test.parent.title][test.title]) {
      this.testCases[test.parent.title][test.title].status = result.status;
      this.testCases[test.parent.title][test.title].endTime = new Date();
      this.testCases[test.parent.title][test.title].duration =
        this.testCases[test.parent.title][test.title].endTime -
        this.testCases[test.parent.title][test.title].startTime;
      this.testCases[test.parent.title][test.title].error = result.error;
      this.testCases[test.parent.title][test.title].logs = this.logs[test.id] || [];
    }

    // Store result
    if (!this.results[test.parent.title]) {
      this.results[test.parent.title] = {
        passed: 0,
        failed: 0,
        skipped: 0,
        total: 0
      };
    }

    this.results[test.parent.title].total++;

    if (result.status === 'passed') {
      this.results[test.parent.title].passed++;
    } else if (result.status === 'failed') {
      this.results[test.parent.title].failed++;
    } else if (result.status === 'skipped') {
      this.results[test.parent.title].skipped++;
    }

    this.currentTest = null;
  }

  /**
   * Called when the test run ends
   */
  onEnd(result) {
    console.log(`Finished the run: ${result.status}`);

    // Generate summary report
    this.generateSummaryReport();

    // Generate detailed reports for each test suite
    for (const suiteName in this.testCases) {
      this.generateDetailedReport(suiteName);
    }
  }

  /**
   * Called when a console message is logged
   */
  onStdOut(chunk, test) {
    if (test && this.logs[test.id]) {
      this.logs[test.id].push(chunk);
    }
  }

  /**
   * Called when an error is logged
   */
  onStdErr(chunk, test) {
    if (test && this.logs[test.id]) {
      this.logs[test.id].push(chunk);
    }
  }

  /**
   * Generate a summary report
   */
  generateSummaryReport() {
    const summaryFile = path.join(this.runDir, 'summary.md');

    let totalPassed = 0;
    let totalFailed = 0;
    let totalSkipped = 0;
    let totalTests = 0;

    for (const suiteName in this.results) {
      totalPassed += this.results[suiteName].passed;
      totalFailed += this.results[suiteName].failed;
      totalSkipped += this.results[suiteName].skipped;
      totalTests += this.results[suiteName].total;
    }

    const summaryContent = `# Test Results Summary (${this.dateStr})

## Overview
${totalTests} tests were run.
${totalPassed} tests passed.
${totalFailed} tests failed.
${totalSkipped} tests skipped.

## Test Results by Suite
${Object.entries(this.results).map(([suiteName, result]) =>
  `### ${suiteName}
- Total: ${result.total}
- Passed: ${result.passed}
- Failed: ${result.failed}
- Skipped: ${result.skipped}
`).join('\n')}

## Details

${Object.keys(this.results).map(suiteName =>
  `### ${suiteName}
See [${suiteName.replace(/\\s+/g, '-').toLowerCase()}.md](${suiteName.replace(/\\s+/g, '-').toLowerCase()}.md) for details.
`).join('\n')}

## Root Cause Analysis

### Test Script Issues
- Selectors may need to be updated if the DOM structure has changed
- Text content expectations may need to be updated if the content has changed
- Navigation expectations may need to be updated if the URL structure has changed
- Timing issues may need to be addressed if there are animations or loading states

### Application Issues
- Features may not be fully implemented yet
- UI components may be missing or incomplete
- Navigation may not be working as expected
- Data may not be loading or displaying correctly

## Next Steps

1. Fix test script issues first:
   - Update selectors to match current implementation
   - Adjust timing for animations and transitions
   - Improve test resilience with multiple selector strategies

2. Address application issues:
   - Implement missing UI elements
   - Fix incorrect text content
   - Ensure proper accessibility attributes

3. Re-run tests to verify fixes

## HTML Report
An interactive HTML report is available in the Playwright HTML report directory.
`;

    fs.writeFileSync(summaryFile, summaryContent);
    console.log(`Summary report saved to ${summaryFile}`);
  }

  /**
   * Generate a detailed report for a test suite
   * @param {string} suiteName - Name of the test suite
   */
  generateDetailedReport(suiteName) {
    const reportFile = path.join(this.runDir, `${suiteName.replace(/\\s+/g, '-').toLowerCase()}.md`);

    let testCaseResults = '';

    for (const testName in this.testCases[suiteName]) {
      const testCase = this.testCases[suiteName][testName];

      testCaseResults += `
### ${testName}

**Status**: ${testCase.status === 'passed' ? '✅ PASSED' : testCase.status === 'failed' ? '❌ FAILED' : '⏭️ SKIPPED'}
**Duration**: ${testCase.duration}ms

${testCase.status === 'failed' ? `**Error**:
\`\`\`
${testCase.error ? testCase.error.message : 'No error message available'}
\`\`\`

**Stack Trace**:
\`\`\`
${testCase.error ? testCase.error.stack : 'No stack trace available'}
\`\`\`
` : ''}

**Steps**:
${testCase.steps.map(step =>
  `- ${step.title}: ${step.status === 'passed' ? '✅' : step.status === 'failed' ? '❌' : '⏭️'} (${step.duration}ms)${step.error ? `\n  Error: ${step.error.message}` : ''}`
).join('\n')}

**Console Logs**:
\`\`\`
${testCase.logs.join('')}
\`\`\`
`;
    }

    const reportContent = `# Test Results: ${suiteName} (${this.dateStr})

## Summary
- **Total**: ${this.results[suiteName].total}
- **Passed**: ${this.results[suiteName].passed}
- **Failed**: ${this.results[suiteName].failed}
- **Skipped**: ${this.results[suiteName].skipped}
- **Duration**: ${new Date() - this.startTime}ms

## Test Details
${testCaseResults}

## Root Cause Analysis

${this.results[suiteName].failed > 0 ? `
### Potential Issues

1. **Implementation Status**: Some features might not be fully implemented yet.
2. **Selector Issues**: The selectors used in the tests might not match the actual DOM structure.
3. **Timing Issues**: There might be animations or loading states causing timing problems.
4. **Navigation Issues**: The expected navigation behavior might not be implemented yet.

### Recommendations

1. Check if the features being tested are fully implemented.
2. Update the selectors if the DOM structure has changed.
3. Add proper waiting mechanisms for asynchronous operations.
4. Update the expected navigation behavior if it has changed.
` : ''}

## Next Steps

1. ${this.results[suiteName].failed > 0 ? 'Fix the failing tests.' : 'Continue to maintain the passing tests.'}
2. Add more tests to increase coverage.
3. Ensure that the tests are resilient to changes in the implementation.
`;

    fs.writeFileSync(reportFile, reportContent);
    console.log(`Detailed report for ${suiteName} saved to ${reportFile}`);
  }
}

export default DetailedReporter;
