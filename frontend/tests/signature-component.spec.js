// @ts-check
import { test, expect } from '@playwright/test';

test.describe('Signature Component Tests', () => {
  test('should render signature component on design system page', async ({ page }) => {
    // Navigate to the design system page which doesn't require authentication
    await page.goto('/design-system');

    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Look for any canvas element (signature components use canvas)
    const canvasElements = await page.locator('canvas').count();
    
    // If there are canvas elements, test one of them
    if (canvasElements > 0) {
      const canvas = page.locator('canvas').first();
      await expect(canvas).toBeVisible();
      
      // Test if we can interact with the canvas
      const boundingBox = await canvas.boundingBox();
      if (boundingBox) {
        // Try to draw on the canvas
        await canvas.click({
          position: { x: boundingBox.width / 2, y: boundingBox.height / 2 }
        });
      }
    }
  });

  test('should render basic page elements', async ({ page }) => {
    // Test a simple page that should always work
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Check for basic page structure
    await expect(page.locator('body')).toBeVisible();
    
    // Check if we can find any interactive elements
    const buttons = await page.locator('button').count();
    const links = await page.locator('a').count();
    
    // We should have some interactive elements on the page
    expect(buttons + links).toBeGreaterThan(0);
  });

  test('should navigate to design system page', async ({ page }) => {
    // Navigate to design system page
    await page.goto('/design-system');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Check if the page loaded successfully
    await expect(page).toHaveURL(/.*design-system.*/);
    
    // Look for design system content
    const headings = await page.locator('h1, h2, h3').count();
    expect(headings).toBeGreaterThan(0);
  });

  test('should handle navigation without authentication', async ({ page }) => {
    // Test navigation to public pages
    const publicPages = ['/', '/how-it-works', '/contact', '/help'];
    
    for (const pagePath of publicPages) {
      await page.goto(pagePath);
      await page.waitForLoadState('networkidle');
      
      // Check that the page loaded (not redirected to login)
      expect(page.url()).toContain(pagePath);
      
      // Check for basic page structure
      await expect(page.locator('body')).toBeVisible();
    }
  });

  test('should display header and footer', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Wait for header and footer with longer timeout
    await page.waitForSelector('header', { timeout: 10000 });
    await page.waitForSelector('footer', { timeout: 10000 });
    
    // Verify they are visible
    await expect(page.locator('header')).toBeVisible();
    await expect(page.locator('footer')).toBeVisible();
  });
});
