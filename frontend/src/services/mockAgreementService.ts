// Mock service for agreement data during testing
export interface MockAgreement {
  id: string;
  title: string;
  status: 'draft' | 'pending' | 'active' | 'completed' | 'disputed';
  ownerSigned: boolean;
  renterSigned: boolean;
  contentHash: string;
  createdAt: string;
  updatedAt: string;
  rentalId: string;
  ownerId: string;
  renterId: string;
  agreementHtml?: string;
}

// Mock agreement data
const mockAgreements: MockAgreement[] = [
  {
    id: 'agreement-1',
    title: 'Rental Agreement for Power Drill',
    status: 'pending',
    ownerSigned: true,
    renterSigned: false,
    contentHash: 'abc123def456',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    rentalId: 'rental-1',
    ownerId: 'user-1',
    renterId: 'user-2',
    agreementHtml: `
      <div class="agreement-content">
        <h2>Rental Agreement</h2>
        <p>This agreement is between the owner and renter for the rental of a Power Drill.</p>
        <h3>Terms and Conditions</h3>
        <ul>
          <li>Rental period: 3 days</li>
          <li>Daily rate: $25</li>
          <li>Security deposit: $50</li>
          <li>Renter is responsible for any damage</li>
        </ul>
        <p>By signing this agreement, both parties agree to the terms outlined above.</p>
      </div>
    `
  },
  {
    id: 'agreement-2',
    title: 'Rental Agreement for Mountain Bike',
    status: 'active',
    ownerSigned: true,
    renterSigned: true,
    contentHash: 'def456ghi789',
    createdAt: '2024-01-10T14:30:00Z',
    updatedAt: '2024-01-12T09:15:00Z',
    rentalId: 'rental-2',
    ownerId: 'user-3',
    renterId: 'user-2',
    agreementHtml: `
      <div class="agreement-content">
        <h2>Rental Agreement</h2>
        <p>This agreement is between the owner and renter for the rental of a Mountain Bike.</p>
        <h3>Terms and Conditions</h3>
        <ul>
          <li>Rental period: 7 days</li>
          <li>Daily rate: $15</li>
          <li>Security deposit: $100</li>
          <li>Helmet included</li>
          <li>Renter must return bike in same condition</li>
        </ul>
        <p>By signing this agreement, both parties agree to the terms outlined above.</p>
      </div>
    `
  }
];

// Mock API functions
export const getMockAgreement = async (agreementId: string): Promise<MockAgreement | null> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const agreement = mockAgreements.find(a => a.id === agreementId);
  return agreement || null;
};

export const signMockAgreement = async (
  agreementId: string, 
  signatureData: string, 
  userId: string
): Promise<MockAgreement> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  const agreement = mockAgreements.find(a => a.id === agreementId);
  if (!agreement) {
    throw new Error('Agreement not found');
  }

  // Update signature status based on user role
  if (agreement.ownerId === userId) {
    agreement.ownerSigned = true;
  } else if (agreement.renterId === userId) {
    agreement.renterSigned = true;
  }

  // Update status if both parties have signed
  if (agreement.ownerSigned && agreement.renterSigned) {
    agreement.status = 'active';
  }

  agreement.updatedAt = new Date().toISOString();
  
  return agreement;
};

export const getMockAgreements = async (userId: string): Promise<MockAgreement[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));
  
  return mockAgreements.filter(a => a.ownerId === userId || a.renterId === userId);
};

// Initialize mock data in window for testing
if (typeof window !== 'undefined') {
  (window as any).mockAgreementService = {
    getMockAgreement,
    signMockAgreement,
    getMockAgreements,
    mockAgreements
  };
}
