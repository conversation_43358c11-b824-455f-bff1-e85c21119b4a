import { API_URL } from '../config/constants';

// Types
export interface Auction {
  id: string;
  item_id: string;
  owner_id: string;
  title: string;
  description: string;
  start_time: string;
  end_time: string;
  reserve_price: number;
  min_increment: number;
  current_highest_bid: number;
  current_highest_bidder_id: string | null;
  status: 'scheduled' | 'active' | 'ended' | 'canceled';
  rental_start_date: string;
  rental_end_date: string;
  created_at: string;
  updated_at: string;
  item: any;
  owner: any;
  current_highest_bidder: any;
  bids: Bid[];
  // Enhanced auction properties
  location?: string;
  category?: string;
  subcategory?: string;
  imageUrl?: string;
  images?: string[];
  bidCount?: number;
  sellerName?: string;
  sellerRating?: number;
  auctionType?: 'standard' | 'reserve' | 'dutch' | 'sealed' | 'proxy';
  bidIncrementStrategy?: 'fixed' | 'percentage' | 'tiered';
  bidIncrement?: number;
  bidIncrementPercentage?: number;
  enableAntiSniping?: boolean;
  antiSnipingTime?: number;
}

export interface Bid {
  id: string;
  auction_id: string;
  bidder_id: string;
  amount: number;
  placed_at: string;
  status: 'active' | 'outbid' | 'won' | 'invalid';
  created_at: string;
  updated_at: string;
  bidder?: any;
}

export interface AuctionCreateData {
  item_id: string;
  title: string;
  description: string;
  start_time: string;
  end_time: string;
  reserve_price: number;
  min_increment: number;
  rental_start_date: string;
  rental_end_date: string;
  // Enhanced auction creation properties
  location?: string;
  category?: string;
  subcategory?: string;
  images?: File[];
  auctionType?: string;
  bidIncrementStrategy?: string;
  bidIncrement?: number;
  bidIncrementPercentage?: number;
  enableAntiSniping?: boolean;
  antiSnipingTime?: number;
}

export interface AuctionUpdateData {
  title?: string;
  description?: string;
  start_time?: string;
  end_time?: string;
  reserve_price?: number;
  min_increment?: number;
  status?: string;
  rental_start_date?: string;
  rental_end_date?: string;
  // Enhanced auction update properties
  location?: string;
  category?: string;
  subcategory?: string;
  images?: File[];
  auctionType?: string;
  bidIncrementStrategy?: string;
  bidIncrement?: number;
  bidIncrementPercentage?: number;
  enableAntiSniping?: boolean;
  antiSnipingTime?: number;
}

export interface AuctionFilter {
  status?: string;
  category?: string;
  subcategory?: string;
  location?: string;
  auctionType?: string;
  minPrice?: number;
  maxPrice?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// WebSocket message types
export interface WebSocketMessage {
  type: string;
  data: any;
}

// Mock data for development
const mockAuctions: Auction[] = [
  {
    id: '1',
    item_id: 'item1',
    owner_id: 'user1',
    title: 'Vintage Camera Collection',
    description: 'A collection of vintage cameras in excellent condition.',
    start_time: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
    end_time: new Date(Date.now() + 86400000).toISOString(), // 1 day from now
    reserve_price: 100,
    min_increment: 10,
    current_highest_bid: 120,
    current_highest_bidder_id: 'user2',
    status: 'active',
    rental_start_date: new Date(Date.now() + 172800000).toISOString(), // 2 days from now
    rental_end_date: new Date(Date.now() + 604800000).toISOString(), // 7 days from now
    created_at: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
    updated_at: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
    item: null,
    owner: null,
    current_highest_bidder: null,
    bids: [],
    // Enhanced properties
    location: 'New York, NY',
    category: 'Electronics',
    subcategory: 'Cameras',
    imageUrl: '/placeholder-item.jpg',
    images: ['/placeholder-item.jpg', '/placeholder-item-2.jpg'],
    bidCount: 7,
    sellerName: 'John Doe',
    sellerRating: 4.8,
    auctionType: 'standard',
    bidIncrementStrategy: 'fixed',
    bidIncrement: 10,
    enableAntiSniping: true,
    antiSnipingTime: 5
  },
  {
    id: '2',
    item_id: 'item2',
    owner_id: 'user2',
    title: 'Modern Dining Table',
    description: 'Sleek modern dining table with 6 chairs.',
    start_time: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
    end_time: new Date(Date.now() + 172800000).toISOString(), // 2 days from now
    reserve_price: 200,
    min_increment: 20,
    current_highest_bid: 250,
    current_highest_bidder_id: 'user3',
    status: 'active',
    rental_start_date: new Date(Date.now() + 259200000).toISOString(), // 3 days from now
    rental_end_date: new Date(Date.now() + 691200000).toISOString(), // 8 days from now
    created_at: new Date(Date.now() - 259200000).toISOString(), // 3 days ago
    updated_at: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
    item: null,
    owner: null,
    current_highest_bidder: null,
    bids: [],
    // Enhanced properties
    location: 'Los Angeles, CA',
    category: 'Furniture',
    subcategory: 'Dining',
    imageUrl: '/placeholder-item.jpg',
    images: ['/placeholder-item.jpg', '/placeholder-item-2.jpg'],
    bidCount: 12,
    sellerName: 'Jane Smith',
    sellerRating: 4.5,
    auctionType: 'reserve',
    bidIncrementStrategy: 'percentage',
    bidIncrementPercentage: 5,
    enableAntiSniping: true,
    antiSnipingTime: 10
  }
];

class AuctionService {
  private token: string | null = null;
  private websocketConnections: Map<string, WebSocket> = new Map();
  private messageHandlers: Map<string, ((message: WebSocketMessage) => void)[]> = new Map();

  constructor() {
    // Initialize token from localStorage
    this.token = localStorage.getItem('token');
  }

  // Set token (called after login)
  setToken(token: string) {
    this.token = token;
    localStorage.setItem('token', token);
  }

  // API calls
  async getAuctions(filters: AuctionFilter = {}): Promise<Auction[]> {
    try {
      const queryParams = new URLSearchParams();

      if (filters.status) queryParams.append('status', filters.status);
      if (filters.category) queryParams.append('category', filters.category);
      if (filters.subcategory) queryParams.append('subcategory', filters.subcategory);
      if (filters.location) queryParams.append('location', filters.location);
      if (filters.auctionType) queryParams.append('auctionType', filters.auctionType);
      if (filters.minPrice) queryParams.append('minPrice', filters.minPrice.toString());
      if (filters.maxPrice) queryParams.append('maxPrice', filters.maxPrice.toString());
      if (filters.sortBy) queryParams.append('sortBy', filters.sortBy);
      if (filters.sortOrder) queryParams.append('sortOrder', filters.sortOrder);

      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';

      // Try to fetch from API
      try {
        const response = await fetch(`${API_URL}/api/v1/auctions${queryString}`, {
          headers: {
            'Authorization': `Bearer ${this.token}`
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch auctions: ${response.statusText}`);
        }

        return response.json();
      } catch (error) {
        console.warn('API call failed, using mock data:', error);
        // Fall back to mock data for development
        return this.filterMockAuctions(filters);
      }
    } catch (error) {
      console.error('Error in getAuctions:', error);
      throw error;
    }
  }

  // Get user's auctions
  async getUserAuctions(userId: string, limit = 10, offset = 0, status?: string): Promise<Auction[]> {
    try {
      const queryParams = new URLSearchParams();
      queryParams.append('limit', limit.toString());
      queryParams.append('offset', offset.toString());
      if (status) queryParams.append('status', status);

      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';

      // Try to fetch from API
      try {
        const response = await fetch(`${API_URL}/api/v1/users/${userId}/auctions${queryString}`, {
          headers: {
            'Authorization': `Bearer ${this.token}`
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch user auctions: ${response.statusText}`);
        }

        return response.json();
      } catch (error) {
        console.warn('API call failed, using mock data:', error);
        // Fall back to mock data for development
        return mockAuctions.filter(auction =>
          auction.owner_id === userId &&
          (!status || auction.status === status)
        ).slice(offset, offset + limit);
      }
    } catch (error) {
      console.error('Error in getUserAuctions:', error);
      throw error;
    }
  }

  // Get auction by ID
  async getAuctionById(id: string): Promise<Auction> {
    try {
      // Try to fetch from API
      try {
        const response = await fetch(`${API_URL}/api/v1/auctions/${id}`, {
          headers: {
            'Authorization': `Bearer ${this.token}`
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch auction: ${response.statusText}`);
        }

        return response.json();
      } catch (error) {
        console.warn('API call failed, using mock data:', error);
        // Fall back to mock data for development
        const auction = mockAuctions.find(a => a.id === id);
        if (!auction) {
          throw new Error('Auction not found');
        }
        return auction;
      }
    } catch (error) {
      console.error(`Error in getAuctionById for ${id}:`, error);
      throw error;
    }
  }

  // Create a new auction
  async createAuction(data: AuctionCreateData): Promise<Auction> {
    try {
      // Try to create via API
      try {
        // Handle file uploads if present
        let requestBody: any;
        let headers: HeadersInit = {
          'Authorization': `Bearer ${this.token}`
        };

        if (data.images && data.images.length > 0) {
          // Use FormData for file uploads
          const formData = new FormData();
          Object.entries(data).forEach(([key, value]) => {
            if (key === 'images') {
              Array.from(value).forEach((file, index) => {
                formData.append(`images[${index}]`, file);
              });
            } else {
              formData.append(key, String(value));
            }
          });
          requestBody = formData;
        } else {
          // Use JSON for regular data
          requestBody = JSON.stringify(data);
          headers['Content-Type'] = 'application/json';
        }

        const response = await fetch(`${API_URL}/api/v1/auctions`, {
          method: 'POST',
          headers,
          body: requestBody
        });

        if (!response.ok) {
          throw new Error(`Failed to create auction: ${response.statusText}`);
        }

        return response.json();
      } catch (error) {
        console.warn('API call failed, using mock data:', error);
        // Fall back to mock data for development
        return {
          id: Math.random().toString(36).substring(2, 15),
          item_id: data.item_id,
          owner_id: 'current-user-id',
          title: data.title,
          description: data.description,
          start_time: data.start_time,
          end_time: data.end_time,
          reserve_price: data.reserve_price,
          min_increment: data.min_increment,
          current_highest_bid: 0,
          current_highest_bidder_id: null,
          status: 'scheduled',
          rental_start_date: data.rental_start_date,
          rental_end_date: data.rental_end_date,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          item: null,
          owner: null,
          current_highest_bidder: null,
          bids: [],
          // Enhanced properties
          location: data.location,
          category: data.category,
          subcategory: data.subcategory,
          imageUrl: '/placeholder-item.jpg',
          images: ['/placeholder-item.jpg'],
          bidCount: 0,
          sellerName: 'Current User',
          sellerRating: 5.0,
          auctionType: data.auctionType as any,
          bidIncrementStrategy: data.bidIncrementStrategy as any,
          bidIncrement: data.bidIncrement,
          bidIncrementPercentage: data.bidIncrementPercentage,
          enableAntiSniping: data.enableAntiSniping,
          antiSnipingTime: data.antiSnipingTime
        };
      }
    } catch (error) {
      console.error('Error in createAuction:', error);
      throw error;
    }
  }

  // Helper method to filter mock auctions based on filters
  private filterMockAuctions(filters: AuctionFilter): Auction[] {
    let filteredAuctions = [...mockAuctions];

    // Apply filters
    if (filters.status) {
      filteredAuctions = filteredAuctions.filter(auction => auction.status === filters.status);
    }

    if (filters.category) {
      filteredAuctions = filteredAuctions.filter(auction => auction.category === filters.category);
    }

    if (filters.subcategory) {
      filteredAuctions = filteredAuctions.filter(auction => auction.subcategory === filters.subcategory);
    }

    if (filters.location) {
      filteredAuctions = filteredAuctions.filter(auction =>
        auction.location && auction.location.includes(filters.location!)
      );
    }

    if (filters.auctionType) {
      filteredAuctions = filteredAuctions.filter(auction => auction.auctionType === filters.auctionType);
    }

    if (filters.minPrice !== undefined) {
      filteredAuctions = filteredAuctions.filter(auction => auction.current_highest_bid >= filters.minPrice!);
    }

    if (filters.maxPrice !== undefined) {
      filteredAuctions = filteredAuctions.filter(auction => auction.current_highest_bid <= filters.maxPrice!);
    }

    // Apply sorting
    if (filters.sortBy) {
      filteredAuctions.sort((a, b) => {
        let valueA: any;
        let valueB: any;

        switch (filters.sortBy) {
          case 'price':
            valueA = a.current_highest_bid;
            valueB = b.current_highest_bid;
            break;
          case 'endTime':
            valueA = new Date(a.end_time).getTime();
            valueB = new Date(b.end_time).getTime();
            break;
          case 'bids':
            valueA = a.bidCount || 0;
            valueB = b.bidCount || 0;
            break;
          default:
            valueA = a.created_at;
            valueB = b.created_at;
        }

        if (filters.sortOrder === 'desc') {
          return valueB - valueA;
        }
        return valueA - valueB;
      });
    }

    return filteredAuctions;
  }
}

export default new AuctionService();
