import { renderHook } from '@testing-library/react';
import axios from 'axios';
import { useErrorHandler } from '../useErrorHandler';

// Mock the config/constants module
jest.mock('../../config/constants', () => ({
  API_URL: 'http://localhost:8000'
}));

import { ErrorType } from '../../utils/errorHandling';

// Mock the useToast hook
jest.mock('../useToast', () => ({
  useToast: () => ({
    showToast: jest.fn(),
  }),
}));

// Mock the parseApiError function
jest.mock('../../utils/errorHandling', () => {
  const original = jest.requireActual('../../utils/errorHandling');
  return {
    ...original,
    parseApiError: jest.fn((error) => ({
      type: original.ErrorType.UNKNOWN,
      message: 'An error occurred',
      details: 'Error details',
      originalError: error,
    })),
  };
});

describe('useErrorHandler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    console.error = jest.fn();
  });

  describe('handleError', () => {
    it('logs the error to the console', () => {
      const { result } = renderHook(() => useErrorHandler());
      const error = new Error('Test error');

      result.current.handleError(error);

      expect(console.error).toHaveBeenCalled();
    });

    it('sets the error message when setError is provided', () => {
      const { result } = renderHook(() => useErrorHandler());
      const error = new Error('Test error');
      const setError = jest.fn();

      result.current.handleError(error, { setError });

      expect(setError).toHaveBeenCalledWith('An error occurred');
    });

    it('uses the friendlyMessage when provided', () => {
      const { result } = renderHook(() => useErrorHandler());
      const error = new Error('Test error');
      const setError = jest.fn();

      result.current.handleError(error, {
        friendlyMessage: 'Friendly error message',
        setError,
      });

      expect(setError).toHaveBeenCalledWith('Friendly error message');
    });

    it('calls the onError callback when provided', () => {
      const { result } = renderHook(() => useErrorHandler());
      const error = new Error('Test error');
      const onError = jest.fn();

      result.current.handleError(error, { onError });

      expect(onError).toHaveBeenCalled();
    });
  });

  describe('tryCatch', () => {
    it('returns the result of the function when it succeeds', async () => {
      const { result } = renderHook(() => useErrorHandler());
      const fn = jest.fn().mockResolvedValue('success');

      const res = await result.current.tryCatch(fn);

      expect(res).toBe('success');
      expect(fn).toHaveBeenCalled();
    });

    it('handles errors when the function fails', async () => {
      const { result } = renderHook(() => useErrorHandler());
      const error = new Error('Test error');
      const fn = jest.fn().mockRejectedValue(error);

      const res = await result.current.tryCatch(fn);

      expect(res).toBeNull();
      expect(fn).toHaveBeenCalled();
    });

    it('uses the friendlyMessage when provided', async () => {
      const { result } = renderHook(() => useErrorHandler());
      const error = new Error('Test error');
      const fn = jest.fn().mockRejectedValue(error);
      const options = {
        friendlyMessage: 'Friendly error message',
      };

      await result.current.tryCatch(fn, options);

      // The error handling is tested in the handleError tests
    });
  });

  describe('handleAxiosError', () => {
    it('handles Axios errors specifically', () => {
      const { result } = renderHook(() => useErrorHandler());
      const error = new axios.AxiosError(
        'Request failed',
        '500',
        {},
        {},
        {
          status: 500,
          statusText: 'Internal Server Error',
          headers: {},
          config: {} as any,
          request: {},
          data: {},
        }
      );

      result.current.handleAxiosError(error);

      expect(console.error).toHaveBeenCalled();
    });

    it('handles non-Axios errors using the standard handler', () => {
      const { result } = renderHook(() => useErrorHandler());
      const error = new Error('Test error');

      result.current.handleAxiosError(error);

      expect(console.error).toHaveBeenCalled();
    });
  });

  describe('isErrorType', () => {
    it('returns true when the error is of the specified type', () => {
      const { result } = renderHook(() => useErrorHandler());
      const error = {
        type: ErrorType.NETWORK,
        message: 'Network error',
        details: 'Error details',
      };

      const isNetwork = result.current.isErrorType(error, ErrorType.NETWORK);

      expect(isNetwork).toBe(true);
    });

    it('returns false when the error is not of the specified type', () => {
      const { result } = renderHook(() => useErrorHandler());
      const error = {
        type: ErrorType.NETWORK,
        message: 'Network error',
        details: 'Error details',
      };

      const isAuth = result.current.isErrorType(error, ErrorType.AUTHENTICATION);

      expect(isAuth).toBe(false);
    });
  });

  describe('isNetworkError', () => {
    it('returns true for Axios network errors', () => {
      const { result } = renderHook(() => useErrorHandler());
      const error = new axios.AxiosError(
        'Request failed',
        'ECONNABORTED',
        {},
        {},
        undefined
      );

      const isNetwork = result.current.isNetworkError(error);

      expect(isNetwork).toBe(true);
    });

    it('returns false for non-network errors', () => {
      const { result } = renderHook(() => useErrorHandler());
      const error = new Error('Test error');

      const isNetwork = result.current.isNetworkError(error);

      expect(isNetwork).toBe(false);
    });
  });
});
