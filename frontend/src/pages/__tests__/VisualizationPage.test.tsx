import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';

// Mock the constants module
jest.mock('../../config/constants', () => ({
  API_URL: 'http://localhost:8000',
  APP_NAME: 'RentUp',
  DEFAULT_LANGUAGE: 'en',
  SUPPORTED_LANGUAGES: ['en', 'es', 'fr'],
  DEFAULT_CURRENCY: 'USD',
  SUPPORTED_CURRENCIES: ['USD', 'EUR', 'GBP'],
  DEFAULT_THEME: 'light',
  SUPPORTED_THEMES: ['light', 'dark'],
}));

import VisualizationPage from '../VisualizationPage';

// Mock the useAuth hook
jest.mock('../../hooks/useAuth', () => ({
  useAuth: () => ({
    isAuthenticated: false,
    user: null,
  }),
}));

// Mock the useMediaQuery hook
jest.mock('../../hooks/useMediaQuery', () => ({
  __esModule: true,
  useMediaQuery: jest.fn().mockReturnValue(false),
  useIsMobile: jest.fn().mockReturnValue(false),
  useIsTablet: jest.fn().mockReturnValue(false),
  useIsDesktop: jest.fn().mockReturnValue(true),
  default: jest.fn().mockReturnValue(false),
}));

// Mock the VisualizationDemo component
jest.mock('../../components/Visualization/VisualizationDemo', () => {
  return {
    __esModule: true,
    default: ({ className, testId }: { className?: string; testId?: string }) => (
      <div data-testid={testId || 'visualization-demo'} className={className}>
        Visualization Demo Component
      </div>
    ),
  };
});

// Mock the VisualizationDashboard component
jest.mock('../../components/Visualization/VisualizationDashboard', () => {
  return {
    __esModule: true,
    default: ({ userId, compact }: { userId?: string; compact?: boolean }) => (
      <div data-testid="visualization-dashboard">
        Visualization Dashboard for user {userId}
        {compact && <span>Compact mode</span>}
      </div>
    ),
  };
});

// Mock the EnhancedPreferenceVisualization component
jest.mock('../../components/Visualization/EnhancedPreferenceVisualization', () => {
  return {
    __esModule: true,
    default: ({ userId, testId, compact, interactive }: { userId?: string; testId?: string; compact?: boolean; interactive?: boolean }) => (
      <div data-testid={testId || 'enhanced-preference-visualization'}>
        Enhanced Preference Visualization for user {userId}
        {compact && <span>Compact mode</span>}
        {interactive && <span>Interactive mode</span>}
      </div>
    ),
  };
});

// Mock the InteractiveEmbeddingVisualization component
jest.mock('../../components/Visualization/InteractiveEmbeddingVisualization', () => {
  return {
    __esModule: true,
    default: ({ testId, compact, limit, interactive }: { testId?: string; compact?: boolean; limit?: number; interactive?: boolean }) => (
      <div data-testid={testId || 'interactive-embedding-visualization'}>
        Interactive Embedding Visualization with limit {limit}
        {compact && <span>Compact mode</span>}
        {interactive && <span>Interactive mode</span>}
      </div>
    ),
  };
});

// Mock the RecommendationSection component
jest.mock('../../components/Recommendations/RecommendationSection', () => {
  return {
    __esModule: true,
    default: ({ title, type, limit, showExplanations, enhanced, testId }: { title: string; type: string; limit?: number; showExplanations?: boolean; enhanced?: boolean; testId?: string }) => (
      <div data-testid={testId || 'recommendation-section'}>
        <h3>{title}</h3>
        <p>Type: {type}</p>
        <p>Limit: {limit}</p>
        {showExplanations && <p>Shows explanations</p>}
        {enhanced && <p>Enhanced mode</p>}
      </div>
    ),
  };
});

describe('VisualizationPage Component', () => {
  const renderWithProviders = (ui: React.ReactElement) => {
    return render(
      <BrowserRouter>
        <HelmetProvider>
          {ui}
        </HelmetProvider>
      </BrowserRouter>
    );
  };

  it('renders the page title and description', () => {
    renderWithProviders(<VisualizationPage />);

    expect(screen.getByText('AI Recommendation Insights')).toBeInTheDocument();
    expect(screen.getByText('Explore how our AI recommendation system works to provide you with personalized suggestions.')).toBeInTheDocument();
  });

  it('shows sign in message when user is not authenticated', () => {
    renderWithProviders(<VisualizationPage />);

    expect(screen.getByText('Sign In to See Your Personalized Insights')).toBeInTheDocument();
    expect(screen.getByText(/To view your personalized recommendation insights/)).toBeInTheDocument();
    expect(screen.getByText('Sign In')).toBeInTheDocument();
    expect(screen.queryByTestId('visualization-page-demo')).not.toBeInTheDocument();
  });

  it('renders information sections about recommendations', () => {
    renderWithProviders(<VisualizationPage />);

    expect(screen.getByText('How Our Recommendations Work')).toBeInTheDocument();
    expect(screen.getByText('Privacy and Your Data')).toBeInTheDocument();
    expect(screen.getByText('Frequently Asked Questions')).toBeInTheDocument();
  });

  it('renders the visualization demo when user is authenticated', () => {
    // Override the mock to return authenticated user
    jest.spyOn(require('../../hooks/useAuth'), 'useAuth').mockImplementation(() => ({
      isAuthenticated: true,
      user: { id: 'user-123', name: 'Test User' },
    }));

    renderWithProviders(<VisualizationPage />);

    expect(screen.queryByText('Sign In to See Your Personalized Insights')).not.toBeInTheDocument();
    expect(screen.getByTestId('visualization-page-demo')).toBeInTheDocument();
  });

  describe('Authenticated User Tab Navigation', () => {
    beforeEach(() => {
      // Override the mock to return authenticated user
      jest.spyOn(require('../../hooks/useAuth'), 'useAuth').mockImplementation(() => ({
        isAuthenticated: true,
        user: { id: 'user-123', name: 'Test User' },
      }));

      // Mock the media query hook
      jest.spyOn(require('../../hooks/useMediaQuery'), 'useIsMobile').mockReturnValue(false);
      jest.spyOn(require('../../hooks/useMediaQuery'), 'useMediaQuery').mockReturnValue(false);
    });

    it('shows the overview tab by default', () => {
      renderWithProviders(<VisualizationPage />);

      // Check that the tabs are rendered using test IDs
      expect(screen.getByTestId('visualization-tab-overview')).toBeInTheDocument();
      expect(screen.getByTestId('visualization-tab-preferences')).toBeInTheDocument();
      expect(screen.getByTestId('visualization-tab-relationships')).toBeInTheDocument();
      expect(screen.getByTestId('visualization-tab-recommendations')).toBeInTheDocument();
      expect(screen.getByTestId('visualization-tab-dashboard')).toBeInTheDocument();

      // Check that the overview tab content is rendered
      expect(screen.getByTestId('visualization-page-demo')).toBeInTheDocument();

      // Check that the overview tab has the correct ARIA attributes
      const overviewTab = screen.getByTestId('visualization-tab-overview');
      expect(overviewTab).toHaveAttribute('aria-selected', 'true');
      expect(overviewTab).toHaveAttribute('role', 'tab');
      expect(overviewTab).toHaveAttribute('tabindex', '0');

      // Check that the overview panel has the correct ARIA attributes
      const overviewPanel = screen.getByTestId('visualization-content-overview');
      expect(overviewPanel).toHaveAttribute('role', 'tabpanel');
      expect(overviewPanel).toHaveAttribute('aria-labelledby', 'tab-overview');
    });

    it('switches to the preferences tab when clicked', async () => {
      renderWithProviders(<VisualizationPage />);

      // Click on the preferences tab
      fireEvent.click(screen.getByTestId('visualization-tab-preferences'));

      // Check that the preferences tab content is rendered
      await waitFor(() => {
        expect(screen.getByTestId('visualization-page-preference-viz')).toBeInTheDocument();
      });

      // Check that the preferences tab has the correct ARIA attributes
      const preferencesTab = screen.getByTestId('visualization-tab-preferences');
      expect(preferencesTab).toHaveAttribute('aria-selected', 'true');
      expect(preferencesTab).toHaveAttribute('role', 'tab');
      expect(preferencesTab).toHaveAttribute('tabindex', '0');

      // Check that the preferences panel has the correct ARIA attributes
      const preferencesPanel = screen.getByTestId('visualization-content-preferences');
      expect(preferencesPanel).toHaveAttribute('role', 'tabpanel');
      expect(preferencesPanel).toHaveAttribute('aria-labelledby', 'tab-preferences');

      // Check that the other tabs are not selected
      expect(screen.getByTestId('visualization-tab-overview')).toHaveAttribute('aria-selected', 'false');
      expect(screen.getByTestId('visualization-tab-relationships')).toHaveAttribute('aria-selected', 'false');
      expect(screen.getByTestId('visualization-tab-recommendations')).toHaveAttribute('aria-selected', 'false');
      expect(screen.getByTestId('visualization-tab-dashboard')).toHaveAttribute('aria-selected', 'false');
    });

    it('switches to the relationships tab when clicked', async () => {
      renderWithProviders(<VisualizationPage />);

      // Click on the relationships tab
      fireEvent.click(screen.getByTestId('visualization-tab-relationships'));

      // Check that the relationships tab content is rendered
      await waitFor(() => {
        expect(screen.getByTestId('visualization-page-embedding-viz')).toBeInTheDocument();
      });

      // Check that the relationships tab has the correct ARIA attributes
      const relationshipsTab = screen.getByTestId('visualization-tab-relationships');
      expect(relationshipsTab).toHaveAttribute('aria-selected', 'true');
      expect(relationshipsTab).toHaveAttribute('role', 'tab');
      expect(relationshipsTab).toHaveAttribute('tabindex', '0');

      // Check that the relationships panel has the correct ARIA attributes
      const relationshipsPanel = screen.getByTestId('visualization-content-relationships');
      expect(relationshipsPanel).toHaveAttribute('role', 'tabpanel');
      expect(relationshipsPanel).toHaveAttribute('aria-labelledby', 'tab-relationships');
    });

    it('switches to the recommendations tab when clicked', async () => {
      renderWithProviders(<VisualizationPage />);

      // Click on the recommendations tab
      fireEvent.click(screen.getByTestId('visualization-tab-recommendations'));

      // Check that the recommendations tab content is rendered
      await waitFor(() => {
        expect(screen.getByTestId('visualization-recommendations')).toBeInTheDocument();
      });

      // Check that the recommendations tab has the correct ARIA attributes
      const recommendationsTab = screen.getByTestId('visualization-tab-recommendations');
      expect(recommendationsTab).toHaveAttribute('aria-selected', 'true');
      expect(recommendationsTab).toHaveAttribute('role', 'tab');
      expect(recommendationsTab).toHaveAttribute('tabindex', '0');

      // Check that the recommendations panel has the correct ARIA attributes
      const recommendationsPanel = screen.getByTestId('visualization-content-recommendations');
      expect(recommendationsPanel).toHaveAttribute('role', 'tabpanel');
      expect(recommendationsPanel).toHaveAttribute('aria-labelledby', 'tab-recommendations');
    });

    it('switches to the dashboard tab when clicked', async () => {
      renderWithProviders(<VisualizationPage />);

      // Click on the dashboard tab
      fireEvent.click(screen.getByTestId('visualization-tab-dashboard'));

      // Check that the dashboard tab content is rendered
      await waitFor(() => {
        expect(screen.getByTestId('visualization-dashboard')).toBeInTheDocument();
      });

      // Check that the dashboard tab has the correct ARIA attributes
      const dashboardTab = screen.getByTestId('visualization-tab-dashboard');
      expect(dashboardTab).toHaveAttribute('aria-selected', 'true');
      expect(dashboardTab).toHaveAttribute('role', 'tab');
      expect(dashboardTab).toHaveAttribute('tabindex', '0');

      // Check that the dashboard panel has the correct ARIA attributes
      const dashboardPanel = screen.getByTestId('visualization-content-dashboard');
      expect(dashboardPanel).toHaveAttribute('role', 'tabpanel');
      expect(dashboardPanel).toHaveAttribute('aria-labelledby', 'tab-dashboard');
    });

    it('supports keyboard navigation between tabs using arrow keys', async () => {
      renderWithProviders(<VisualizationPage />);

      // Get tab elements
      const overviewTab = screen.getByTestId('visualization-tab-overview');
      const preferencesTab = screen.getByTestId('visualization-tab-preferences');
      const relationshipsTab = screen.getByTestId('visualization-tab-relationships');

      // First, ensure we start from the overview tab by clicking it
      fireEvent.click(overviewTab);

      // Wait for the overview tab to be active
      await waitFor(() => {
        expect(overviewTab).toHaveAttribute('aria-selected', 'true');
      });

      // Focus on the overview tab for keyboard navigation
      overviewTab.focus();

      // Press right arrow to navigate to preferences tab
      fireEvent.keyDown(overviewTab, { key: 'ArrowRight' });

      // Wait for the tab change to complete
      await waitFor(() => {
        expect(preferencesTab).toHaveAttribute('aria-selected', 'true');
      });

      // Check that the preferences tab content is rendered
      await waitFor(() => {
        expect(screen.getByTestId('visualization-content-preferences')).toBeInTheDocument();
      });

      // Press right arrow again to navigate to relationships tab
      fireEvent.keyDown(preferencesTab, { key: 'ArrowRight' });

      // Wait for the tab change to complete
      await waitFor(() => {
        expect(relationshipsTab).toHaveAttribute('aria-selected', 'true');
      });

      // Check that the relationships tab content is rendered
      await waitFor(() => {
        expect(screen.getByTestId('visualization-content-relationships')).toBeInTheDocument();
      });

      // Press left arrow to navigate back to preferences tab
      fireEvent.keyDown(relationshipsTab, { key: 'ArrowLeft' });

      // Wait for the tab change to complete
      await waitFor(() => {
        expect(preferencesTab).toHaveAttribute('aria-selected', 'true');
      });

      // Check that the preferences tab content is rendered
      await waitFor(() => {
        expect(screen.getByTestId('visualization-content-preferences')).toBeInTheDocument();
      });
    });
  });
});
