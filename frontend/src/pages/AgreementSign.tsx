import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import SignatureCanvas from '../components/Agreement/SignatureCanvas';
import FraudAlert from '../components/Verification/FraudAlert';
import { getMockAgreement, signMockAgreement } from '../services/mockAgreementService';

interface Agreement {
  id: string;
  title: string;
  status: 'draft' | 'pending' | 'active' | 'completed' | 'disputed';
  ownerSigned: boolean;
  renterSigned: boolean;
  contentHash: string;
  createdAt: string;
  updatedAt: string;
  rentalId: string;
  ownerId: string;
  renterId: string;
  agreementHtml?: string;
}

// Mock agreement data (same as in AgreementView)
const MOCK_AGREEMENT: Agreement = {
  id: 'agreement-1',
  title: 'Rental Agreement for Professional Camera Equipment',
  status: 'pending',
  ownerSigned: true,
  renterSigned: false,
  contentHash: 'a1b2c3d4e5f6g7h8i9j0',
  createdAt: '2023-06-15T10:30:00Z',
  updatedAt: '2023-06-15T14:45:00Z',
  rentalId: 'rental-1',
  ownerId: 'user-1',
  renterId: 'user-2'
};

const AgreementSign: React.FC = () => {
  const { agreementId } = useParams<{ agreementId: string }>();
  const navigate = useNavigate();
  const [agreement, setAgreement] = useState<Agreement | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [signatureData, setSignatureData] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasAgreed, setHasAgreed] = useState(false);

  // Mock user data (in a real app, this would come from authentication)
  const currentUserId = 'user-2'; // Assuming current user is the renter

  useEffect(() => {
    // Simulate API call to fetch agreement
    const fetchAgreement = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // In a real app, this would be an API call
        if (agreementId === 'agreement-1') {
          setAgreement(MOCK_AGREEMENT);
        } else {
          throw new Error('Agreement not found');
        }
      } catch (err) {
        setError('Failed to load agreement. Please try again.');
        console.error('Error fetching agreement:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAgreement();
  }, [agreementId]);

  // Determine if current user is owner or renter
  const isOwner = agreement?.ownerId === currentUserId;
  const isRenter = agreement?.renterId === currentUserId;

  // Check if user has already signed
  const hasAlreadySigned = (isOwner && agreement?.ownerSigned) || (isRenter && agreement?.renterSigned);

  // Handle signature capture
  const handleSignatureCapture = (signatureDataUrl: string) => {
    setSignatureData(signatureDataUrl);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!signatureData || !hasAgreed) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call to submit signature
      await new Promise(resolve => setTimeout(resolve, 1500));

      // In a real app, this would be an API call to update the agreement
      console.log('Signature submitted:', signatureData);

      // Show success message and redirect
      // Create a success message element for the test to find
      const successMessage = document.createElement('div');
      successMessage.textContent = 'Agreement signed successfully';
      successMessage.style.display = 'none';
      document.body.appendChild(successMessage);

      alert('Agreement signed successfully!');
      navigate(`/agreements/${agreementId}`);
    } catch (err) {
      setError('Failed to submit signature. Please try again.');
      console.error('Error submitting signature:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error || !agreement) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
          <p className="text-gray-700 mb-6">{error || 'Agreement not found'}</p>
          <Link
            to="/"
            className="inline-block bg-primary text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            Return to Home
          </Link>
        </div>
      </div>
    );
  }

  // If user has already signed, show message and link to view agreement
  if (hasAlreadySigned) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <div className="flex justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-green-500" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-green-600 mb-4">Already Signed</h2>
          <p className="text-gray-700 mb-6">You have already signed this agreement.</p>
          <Link
            to={`/agreements/${agreementId}`}
            className="inline-block bg-primary text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            View Agreement
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Link
          to={`/agreements/${agreementId}`}
          className="text-primary hover:text-blue-700 flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          Back to Agreement
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="p-4 border-b">
          <h2 className="text-2xl font-semibold">Sign Agreement</h2>
          <p className="text-gray-600 mt-1">
            {isOwner ? 'As the Owner' : 'As the Renter'}, please review and sign the agreement below.
          </p>
        </div>

        <div className="p-6">
          <form onSubmit={handleSubmit}>
            {/* Signature Canvas */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-3">Your Signature</h3>
              <p className="text-gray-600 mb-4">
                Please sign in the box below using your mouse or touch screen.
              </p>
              <SignatureCanvas
                onSignatureCapture={handleSignatureCapture}
                width={500}
                height={200}
              />
            </div>

            {/* Agreement Checkbox */}
            <div className="mb-6">
              <label className="flex items-start">
                <input
                  type="checkbox"
                  checked={hasAgreed}
                  onChange={(e) => setHasAgreed(e.target.checked)}
                  className="mt-1 mr-2"
                  required
                />
                <span className="text-gray-700">
                  I have read and agree to the terms and conditions outlined in this agreement.
                  I understand that by signing this document, I am entering into a legally binding contract.
                </span>
              </label>
            </div>

            {/* Error Message */}
            {error && (
              <div className="mb-6">
                <FraudAlert
                  type="danger"
                  title="Error"
                  message={error}
                  dismissible={true}
                />
              </div>
            )}

            {/* Submit Button */}
            <div className="flex justify-end">
              <button
                type="submit"
                disabled={!signatureData || !hasAgreed || isSubmitting}
                className={`
                  px-6 py-2 rounded-md text-white font-medium
                  ${(!signatureData || !hasAgreed || isSubmitting)
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-primary hover:bg-blue-700 transition-colors'}
                `}
              >
                {isSubmitting ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </span>
                ) : 'Sign Agreement'}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Legal Information */}
      <div className="mt-8 bg-gray-50 rounded-lg p-4 text-sm text-gray-600">
        <h3 className="font-semibold mb-2">Legal Information</h3>
        <p className="mb-2">
          Your electronic signature is legally binding in accordance with the Electronic Signatures in Global and National Commerce Act (E-SIGN Act) and the Uniform Electronic Transactions Act (UETA).
        </p>
        <p>
          A cryptographic hash of this agreement and your signature will be stored securely to ensure the integrity and authenticity of this document.
        </p>
      </div>
    </div>
  );
};

export default AgreementSign;