import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { register as registerService } from '../services/authService';

const schema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string().min(8, 'Password must be at least 8 characters'),
  termsAccepted: z.literal(true, {
    errorMap: () => ({ message: 'You must accept the terms and conditions' }),
  }),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type FormData = z.infer<typeof schema>;

const Register = () => {
  const { register, handleSubmit, formState: { errors }, setError } = useForm<FormData>();
  const [isLoading, setIsLoading] = useState(false);
  const [registerError, setRegisterError] = useState<string | null>(null);
  const navigate = useNavigate();

  const onSubmit = async (data: FormData) => {
    try {
      setIsLoading(true);
      setRegisterError(null);
      schema.parse(data);

      // Convert FormData to RegisterData format
      const registerData = {
        email: data.email,
        password: data.password,
        confirmPassword: data.confirmPassword,
        firstName: data.name.split(' ')[0], // Extract first name
        lastName: data.name.split(' ').slice(1).join(' ') || '', // Extract last name
        agreeToTerms: data.termsAccepted
      };

      const response = await registerService(registerData);

      if (response.success && response.user) {
        // Show success message
        console.log('Registration successful:', response);

        // Redirect to login page with success message
        navigate('/login', {
          state: {
            message: 'Registration successful! Please check your email for verification instructions.',
            type: 'success'
          }
        });
      } else {
        throw new Error('Registration failed');
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        error.errors.forEach((e) => {
          // Type assertion is safe here because we know the path is a string from the schema
          setError(e.path[0] as keyof FormData, { type: "manual", message: e.message });
        });
      } else {
        console.error('Registration error:', error);
        setRegisterError(error instanceof Error ? error.message : "Registration failed. Please try again.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-light-gray flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 border border-gray-200">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Create Account</h1>
          <p className="text-gray-700 mt-2 font-medium">Join RentUp and start renting or listing items</p>
        </div>

        {registerError && (
          <div className="bg-red-100 border border-red-300 text-red-800 px-4 py-3 rounded-md mb-6 shadow-sm font-medium">
            {registerError}
          </div>
        )}

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div>
            <label htmlFor="name" className="block text-sm font-semibold text-gray-800 mb-2">
              Full Name
            </label>
            <input
              id="name"
              type="text"
              autoComplete="name"
              className={`w-full px-3 py-2.5 border ${errors.name ? 'border-error-600 border-2' : 'border-gray-400'} rounded-md focus:outline-none focus:ring-2 focus:ring-primary shadow-sm text-gray-800`}
              {...register("name")}
            />
            {errors.name && (
              <p className="mt-2 text-sm text-red-700 font-medium">{errors.name.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-semibold text-gray-800 mb-2">
              Email Address
            </label>
            <input
              id="email"
              type="email"
              autoComplete="email"
              className={`w-full px-3 py-2.5 border ${errors.email ? 'border-error-600 border-2' : 'border-gray-400'} rounded-md focus:outline-none focus:ring-2 focus:ring-primary shadow-sm text-gray-800`}
              {...register("email")}
            />
            {errors.email && (
              <p className="mt-2 text-sm text-red-700 font-medium">{errors.email.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-semibold text-gray-800 mb-2">
              Password
            </label>
            <input
              id="password"
              type="password"
              autoComplete="new-password"
              className={`w-full px-3 py-2.5 border ${errors.password ? 'border-error-600 border-2' : 'border-gray-400'} rounded-md focus:outline-none focus:ring-2 focus:ring-primary shadow-sm text-gray-800`}
              {...register("password")}
            />
            {errors.password && (
              <p className="mt-2 text-sm text-red-700 font-medium">{errors.password.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="confirmPassword" className="block text-sm font-semibold text-gray-800 mb-2">
              Confirm Password
            </label>
            <input
              id="confirmPassword"
              type="password"
              autoComplete="new-password"
              className={`w-full px-3 py-2.5 border ${errors.confirmPassword ? 'border-error-600 border-2' : 'border-gray-400'} rounded-md focus:outline-none focus:ring-2 focus:ring-primary shadow-sm text-gray-800`}
              {...register("confirmPassword")}
            />
            {errors.confirmPassword && (
              <p className="mt-2 text-sm text-red-700 font-medium">{errors.confirmPassword.message}</p>
            )}
          </div>

          <div className="flex items-start">
            <input
              id="terms"
              type="checkbox"
              className="h-5 w-5 text-primary border-gray-400 rounded focus:ring-2 focus:ring-primary mt-0.5"
              {...register("termsAccepted")}
            />
            <label htmlFor="terms" className="ml-2 block text-sm text-gray-700 font-medium">
              I agree to the{' '}
              <Link to="/terms" className="text-primary-700 hover:text-primary-800 underline">
                Terms of Service
              </Link>{' '}
              and{' '}
              <Link to="/privacy" className="text-primary-700 hover:text-primary-800 underline">
                Privacy Policy
              </Link>
            </label>
            {errors.termsAccepted && (
              <p className="text-sm text-red-700 font-medium absolute ml-7 mt-6">{errors.termsAccepted.message}</p>
            )}
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="w-full flex justify-center py-2.5 px-4 border border-primary-700 rounded-md shadow-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors font-semibold"
            >
              {isLoading ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Creating account...
                </span>
              ) : (
                'Create Account'
              )}
            </button>
          </div>
        </form>

        <div className="mt-8 text-center">
          <p className="text-gray-700 font-medium">
            Already have an account?{' '}
            <Link to="/login" className="text-primary-700 hover:text-primary-800 font-semibold underline">
              Sign in
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Register;
