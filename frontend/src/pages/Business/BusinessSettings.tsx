import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useToast } from '../../hooks/useToast';
import { getBusinessAccount, updateBusinessAccount } from '../../services/businessAccountService';
import { BusinessIndustry, BusinessSize, BusinessAccountUpdateRequest } from '../../types/BusinessAccount';
import { BusinessRole } from '../../types/BusinessAccount';
import { useTranslation } from 'react-i18next';

/**
 * BusinessSettings Component
 *
 * Page for managing business account settings.
 */
const BusinessSettings: React.FC = () => {
  const { t } = useTranslation();
  const { user, currentBusiness, isBusinessAccount, hasBusinessRole } = useAuth();
  const { showToast } = useToast();
  const navigate = useNavigate();

  const [formData, setFormData] = useState<BusinessAccountUpdateRequest>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [activeTab, setActiveTab] = useState('profile');

  // Redirect if not a business account
  useEffect(() => {
    if (!isBusinessAccount) {
      navigate('/business/select');
      showToast(t('business.errors.notBusinessAccount'), 'error');
    }
  }, [isBusinessAccount, navigate, showToast, t]);

  // Check if user has permission to manage settings
  useEffect(() => {
    if (!hasBusinessRole([BusinessRole.OWNER, BusinessRole.ADMIN])) {
      navigate('/business/dashboard');
      showToast(t('business.errors.noPermission'), 'error');
    }
  }, [hasBusinessRole, navigate, showToast, t]);

  // Load business account data
  useEffect(() => {
    if (!currentBusiness?.id) return;

    // Only update form data if it's different to prevent infinite loops
    const newFormData = {
      name: currentBusiness.name || '',
      description: currentBusiness.description || '',
      industry: currentBusiness.industry || '',
      size: currentBusiness.size || '',
      website: currentBusiness.website || '',
      email: currentBusiness.email || '',
      phone_number: currentBusiness.phone_number || ''
    };

    // Check if form data has actually changed
    setFormData(prevFormData => {
      const hasChanged = Object.keys(newFormData).some(key =>
        prevFormData[key as keyof typeof prevFormData] !== newFormData[key as keyof typeof newFormData]
      );

      return hasChanged ? newFormData : prevFormData;
    });
  }, [currentBusiness?.id]); // Only depend on the ID to prevent infinite loops

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear error for this field
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name?.trim()) {
      newErrors.name = t('business.settings.errors.nameRequired');
    } else if (formData.name.length < 3) {
      newErrors.name = t('business.settings.errors.nameLength');
    }

    if (!formData.industry) {
      newErrors.industry = t('business.settings.errors.industryRequired');
    }

    if (!formData.size) {
      newErrors.size = t('business.settings.errors.sizeRequired');
    }

    if (formData.website && !/^(https?:\/\/)?(www\.)?[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(\/.*)?$/.test(formData.website)) {
      newErrors.website = t('business.settings.errors.invalidWebsite');
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = t('business.settings.errors.invalidEmail');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm() || !currentBusiness) {
      return;
    }

    try {
      setIsSubmitting(true);

      // Update business account
      await updateBusinessAccount(currentBusiness.id, formData);

      showToast(t('business.settings.success'), 'success');
    } catch (error) {
      console.error('Error updating business account:', error);
      showToast(t('business.settings.error'), 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle tab change
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  if (!currentBusiness) {
    return null;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">{t('business.settings.title')}</h1>
          <p className="text-gray-600">{t('business.settings.subtitle')}</p>
        </div>
        <div className="mt-4 md:mt-0">
          <button
            onClick={() => navigate('/business/dashboard')}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <svg className="-ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            {t('business.settings.backToDashboard')}
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'profile'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => handleTabChange('profile')}
          >
            {t('business.settings.tabs.profile')}
          </button>
          <button
            className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'branding'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => handleTabChange('branding')}
          >
            {t('business.settings.tabs.branding')}
          </button>
          <button
            className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'notifications'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => handleTabChange('notifications')}
          >
            {t('business.settings.tabs.notifications')}
          </button>
          <button
            className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'security'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => handleTabChange('security')}
          >
            {t('business.settings.tabs.security')}
          </button>
        </nav>
      </div>

      {/* Profile Tab */}
      {activeTab === 'profile' && (
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <form onSubmit={handleSubmit}>
            <div className="space-y-6">
              {/* Business Name */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  {t('business.settings.businessName')} <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name || ''}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border ${
                    errors.name ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-primary-500'
                  } rounded-md shadow-sm focus:outline-none focus:ring-2`}
                  placeholder={t('business.settings.businessNamePlaceholder')}
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                )}
              </div>

              {/* Business Description */}
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                  {t('business.settings.description')}
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description || ''}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder={t('business.settings.descriptionPlaceholder')}
                />
              </div>

              {/* Industry and Size */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="industry" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('business.settings.industry')} <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="industry"
                    name="industry"
                    value={formData.industry || ''}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 border ${
                      errors.industry ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-primary-500'
                    } rounded-md shadow-sm focus:outline-none focus:ring-2`}
                  >
                    <option value="">{t('business.settings.selectIndustry')}</option>
                    {Object.entries(BusinessIndustry).map(([key, value]) => (
                      <option key={key} value={value}>
                        {t(`business.industries.${value}`)}
                      </option>
                    ))}
                  </select>
                  {errors.industry && (
                    <p className="mt-1 text-sm text-red-600">{errors.industry}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="size" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('business.settings.size')} <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="size"
                    name="size"
                    value={formData.size || ''}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 border ${
                      errors.size ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-primary-500'
                    } rounded-md shadow-sm focus:outline-none focus:ring-2`}
                  >
                    <option value="">{t('business.settings.selectSize')}</option>
                    {Object.entries(BusinessSize).map(([key, value]) => (
                      <option key={key} value={value}>
                        {t(`business.sizes.${value}`)}
                      </option>
                    ))}
                  </select>
                  {errors.size && (
                    <p className="mt-1 text-sm text-red-600">{errors.size}</p>
                  )}
                </div>
              </div>

              {/* Website and Email */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('business.settings.website')}
                  </label>
                  <input
                    type="text"
                    id="website"
                    name="website"
                    value={formData.website || ''}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 border ${
                      errors.website ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-primary-500'
                    } rounded-md shadow-sm focus:outline-none focus:ring-2`}
                    placeholder="https://example.com"
                  />
                  {errors.website && (
                    <p className="mt-1 text-sm text-red-600">{errors.website}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('business.settings.email')}
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email || ''}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 border ${
                      errors.email ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-primary-500'
                    } rounded-md shadow-sm focus:outline-none focus:ring-2`}
                    placeholder="<EMAIL>"
                  />
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-600">{errors.email}</p>
                  )}
                </div>
              </div>

              {/* Phone Number */}
              <div>
                <label htmlFor="phone_number" className="block text-sm font-medium text-gray-700 mb-1">
                  {t('business.settings.phoneNumber')}
                </label>
                <input
                  type="tel"
                  id="phone_number"
                  name="phone_number"
                  value={formData.phone_number || ''}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="+****************"
                />
              </div>

              {/* Submit Button */}
              <div className="flex justify-end">
                <button
                  type="submit"
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 flex items-center"
                  disabled={isSubmitting}
                >
                  {isSubmitting && (
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  )}
                  {t('business.settings.saveChanges')}
                </button>
              </div>
            </div>
          </form>
        </div>
      )}

      {/* Other tabs would be implemented here */}
      {activeTab !== 'profile' && (
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200 text-center">
          <p className="text-gray-600">{t('business.settings.comingSoon')}</p>
        </div>
      )}
    </div>
  );
};

export default BusinessSettings;
