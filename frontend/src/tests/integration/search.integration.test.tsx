/**
 * Search Integration Tests
 * Tests the complete search flow with API integration
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { http, HttpResponse } from 'msw';
import { server } from '../../testing/mocks/server';

// Import components to test
import SearchResults from '../../pages/SearchResults';
import Home from '../../pages/Home';

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
      mutations: {
        retry: false,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Search Integration Tests', () => {
  const user = userEvent.setup();

  describe('Search from Home Page', () => {
    it('should perform search from home page and display results', async () => {
      render(
        <TestWrapper>
          <Home />
        </TestWrapper>
      );

      // Find search input and button
      const searchInput = screen.getByPlaceholderText(/search for items/i);
      const searchButton = screen.getByRole('button', { name: /search/i });

      // Perform search
      await user.type(searchInput, 'electronics');
      await user.click(searchButton);

      // Wait for navigation and results
      await waitFor(() => {
        // This depends on your routing implementation
        // You might check for URL change or results display
        expect(window.location.pathname).toContain('/search');
      });
    });

    it('should handle empty search gracefully', async () => {
      render(
        <TestWrapper>
          <Home />
        </TestWrapper>
      );

      const searchButton = screen.getByRole('button', { name: /search/i });

      // Try to search with empty input
      await user.click(searchButton);

      // Should show validation message or handle gracefully
      await waitFor(() => {
        expect(
          screen.getByText(/please enter a search term/i) ||
          screen.getByText(/search term is required/i)
        ).toBeInTheDocument();
      });
    });
  });

  describe('Search Results Page', () => {
    it('should display search results from API', async () => {
      // Mock search results
      server.use(
        http.get('/api/v1/items', ({ request }) => {
          const url = new URL(request.url);
          const search = url.searchParams.get('search');
          
          if (search === 'electronics') {
            return HttpResponse.json({
              items: [
                {
                  id: '1',
                  title: 'Laptop',
                  description: 'Gaming laptop for rent',
                  price: 50.00,
                  category: 'electronics',
                  location: 'Test City',
                },
                {
                  id: '2',
                  title: 'Camera',
                  description: 'Professional camera',
                  price: 30.00,
                  category: 'electronics',
                  location: 'Test City',
                },
              ],
              total: 2,
              page: 1,
              per_page: 10,
            });
          }
          
          return HttpResponse.json({
            items: [],
            total: 0,
            page: 1,
            per_page: 10,
          });
        })
      );

      // Render search results page with search query
      const mockLocation = {
        pathname: '/search',
        search: '?q=electronics',
        hash: '',
        state: null,
        key: 'test',
      };

      render(
        <TestWrapper>
          <SearchResults />
        </TestWrapper>
      );

      // Wait for results to load
      await waitFor(() => {
        expect(screen.getByText('Laptop')).toBeInTheDocument();
        expect(screen.getByText('Camera')).toBeInTheDocument();
      });

      // Check that results display correctly
      expect(screen.getByText(/gaming laptop for rent/i)).toBeInTheDocument();
      expect(screen.getByText(/professional camera/i)).toBeInTheDocument();
    });

    it('should handle no results found', async () => {
      // Mock empty search results
      server.use(
        http.get('/api/v1/items', () => {
          return HttpResponse.json({
            items: [],
            total: 0,
            page: 1,
            per_page: 10,
          });
        })
      );

      render(
        <TestWrapper>
          <SearchResults />
        </TestWrapper>
      );

      // Wait for no results message
      await waitFor(() => {
        expect(
          screen.getByText(/no items found/i) ||
          screen.getByText(/no results/i)
        ).toBeInTheDocument();
      });
    });

    it('should handle search API errors', async () => {
      // Mock API error
      server.use(
        http.get('/api/v1/items', () => {
          return HttpResponse.error();
        })
      );

      render(
        <TestWrapper>
          <SearchResults />
        </TestWrapper>
      );

      // Wait for error message
      await waitFor(() => {
        expect(
          screen.getByText(/error loading results/i) ||
          screen.getByText(/something went wrong/i)
        ).toBeInTheDocument();
      });
    });

    it('should filter results by category', async () => {
      // Mock filtered search results
      server.use(
        http.get('/api/v1/items', ({ request }) => {
          const url = new URL(request.url);
          const category = url.searchParams.get('category');
          
          if (category === 'electronics') {
            return HttpResponse.json({
              items: [
                {
                  id: '1',
                  title: 'Laptop',
                  description: 'Gaming laptop for rent',
                  price: 50.00,
                  category: 'electronics',
                  location: 'Test City',
                },
              ],
              total: 1,
              page: 1,
              per_page: 10,
            });
          }
          
          return HttpResponse.json({
            items: [],
            total: 0,
            page: 1,
            per_page: 10,
          });
        })
      );

      render(
        <TestWrapper>
          <SearchResults />
        </TestWrapper>
      );

      // Find and click category filter
      const categoryFilter = screen.getByText(/electronics/i);
      await user.click(categoryFilter);

      // Wait for filtered results
      await waitFor(() => {
        expect(screen.getByText('Laptop')).toBeInTheDocument();
      });

      // Verify only electronics items are shown
      expect(screen.queryByText(/furniture/i)).not.toBeInTheDocument();
    });

    it('should handle pagination', async () => {
      // Mock paginated results
      server.use(
        http.get('/api/v1/items', ({ request }) => {
          const url = new URL(request.url);
          const page = parseInt(url.searchParams.get('page') || '1');
          
          if (page === 1) {
            return HttpResponse.json({
              items: [
                { id: '1', title: 'Item 1', description: 'First item', price: 10.00, category: 'test', location: 'Test City' },
                { id: '2', title: 'Item 2', description: 'Second item', price: 20.00, category: 'test', location: 'Test City' },
              ],
              total: 15,
              page: 1,
              per_page: 2,
            });
          } else if (page === 2) {
            return HttpResponse.json({
              items: [
                { id: '3', title: 'Item 3', description: 'Third item', price: 30.00, category: 'test', location: 'Test City' },
                { id: '4', title: 'Item 4', description: 'Fourth item', price: 40.00, category: 'test', location: 'Test City' },
              ],
              total: 15,
              page: 2,
              per_page: 2,
            });
          }
          
          return HttpResponse.json({ items: [], total: 0, page: 1, per_page: 2 });
        })
      );

      render(
        <TestWrapper>
          <SearchResults />
        </TestWrapper>
      );

      // Wait for first page results
      await waitFor(() => {
        expect(screen.getByText('Item 1')).toBeInTheDocument();
        expect(screen.getByText('Item 2')).toBeInTheDocument();
      });

      // Click next page
      const nextButton = screen.getByRole('button', { name: /next/i });
      await user.click(nextButton);

      // Wait for second page results
      await waitFor(() => {
        expect(screen.getByText('Item 3')).toBeInTheDocument();
        expect(screen.getByText('Item 4')).toBeInTheDocument();
      });

      // Verify first page items are no longer visible
      expect(screen.queryByText('Item 1')).not.toBeInTheDocument();
    });
  });
});
