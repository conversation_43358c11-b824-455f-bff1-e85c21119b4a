/**
 * Basic Integration Test Setup Verification
 * Tests that our integration testing infrastructure is working
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';

// Simple test component
const TestComponent: React.FC = () => {
  return (
    <div>
      <h1>Integration Test Setup</h1>
      <p>This is a basic test to verify our setup is working.</p>
    </div>
  );
};

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <BrowserRouter>
      {children}
    </BrowserRouter>
  );
};

describe('Basic Integration Test Setup', () => {
  it('should render a simple component', () => {
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    expect(screen.getByText('Integration Test Setup')).toBeInTheDocument();
    expect(screen.getByText('This is a basic test to verify our setup is working.')).toBeInTheDocument();
  });

  it('should have access to testing utilities', () => {
    // Test that our testing environment is properly configured
    expect(typeof screen).toBe('object');
    expect(typeof render).toBe('function');
  });

  it('should have access to environment variables', () => {
    // Test that our mock environment variables are available
    expect(global.import?.meta?.env?.MODE).toBe('test');
    expect(global.import?.meta?.env?.VITE_API_URL).toBe('http://localhost:8000');
  });
});
