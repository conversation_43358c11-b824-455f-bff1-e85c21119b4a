/**
 * Full System Integration Tests
 * Tests complete user journeys across the entire application
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { http, HttpResponse } from 'msw';
import { server } from '../../testing/mocks/server';

// Import main app component
import App from '../../App';

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
      mutations: {
        retry: false,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Full System Integration Tests', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    // Clear storage before each test
    localStorage.clear();
    sessionStorage.clear();
    
    // Reset MSW handlers
    server.resetHandlers();
  });

  describe('Complete User Journey: Registration to Booking', () => {
    it('should allow a user to register, login, search, and view items', async () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );

      // Step 1: Start on home page
      await waitFor(() => {
        expect(screen.getByText(/rentup|community marketplace/i)).toBeInTheDocument();
      });

      // Step 2: Navigate to registration
      const registerButton = screen.getByRole('link', { name: /register|sign up/i });
      await user.click(registerButton);

      await waitFor(() => {
        expect(screen.getByText(/create account|register/i)).toBeInTheDocument();
      });

      // Step 3: Fill out registration form
      const nameInput = screen.getByLabelText(/name/i);
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /register|sign up/i });

      await user.type(nameInput, 'Test User');
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);

      // Step 4: Verify successful registration and automatic login
      await waitFor(() => {
        expect(
          screen.getByText(/welcome|registration successful/i) ||
          screen.getByText(/dashboard|profile/i)
        ).toBeInTheDocument();
      });

      // Step 5: Navigate to search
      const searchInput = screen.getByPlaceholderText(/search for items/i);
      const searchButton = screen.getByRole('button', { name: /search/i });

      await user.type(searchInput, 'camera');
      await user.click(searchButton);

      // Step 6: Verify search results
      await waitFor(() => {
        expect(screen.getByText(/search results|items found/i)).toBeInTheDocument();
      });

      // Step 7: Click on an item
      const itemLink = screen.getByText(/camera|test item/i);
      await user.click(itemLink);

      // Step 8: Verify item details page
      await waitFor(() => {
        expect(
          screen.getByText(/item details|description/i) ||
          screen.getByRole('button', { name: /rent|book/i })
        ).toBeInTheDocument();
      });
    });

    it('should handle the complete booking flow', async () => {
      // Mock authenticated user
      localStorage.setItem('access_token', 'mock-token');
      localStorage.setItem('user_id', 'test-user-123');

      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );

      // Navigate to an item (simulate direct link)
      // This would depend on your routing setup
      window.history.pushState({}, '', '/items/1');

      await waitFor(() => {
        expect(screen.getByText(/item details|rent this item/i)).toBeInTheDocument();
      });

      // Click rent/book button
      const rentButton = screen.getByRole('button', { name: /rent|book now/i });
      await user.click(rentButton);

      // Fill out booking form
      await waitFor(() => {
        expect(screen.getByText(/booking|rental dates/i)).toBeInTheDocument();
      });

      // Select dates (this depends on your date picker implementation)
      const startDateInput = screen.getByLabelText(/start date|from/i);
      const endDateInput = screen.getByLabelText(/end date|to/i);

      await user.type(startDateInput, '2025-06-01');
      await user.type(endDateInput, '2025-06-03');

      // Submit booking
      const confirmButton = screen.getByRole('button', { name: /confirm|book/i });
      await user.click(confirmButton);

      // Verify booking confirmation
      await waitFor(() => {
        expect(
          screen.getByText(/booking confirmed|reservation successful/i)
        ).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling Across the System', () => {
    it('should handle network errors gracefully throughout the app', async () => {
      // Mock network errors for all endpoints
      server.use(
        http.all('*', () => {
          return HttpResponse.error();
        })
      );

      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );

      // Try to perform actions that require API calls
      const searchInput = screen.getByPlaceholderText(/search for items/i);
      const searchButton = screen.getByRole('button', { name: /search/i });

      await user.type(searchInput, 'test');
      await user.click(searchButton);

      // Verify error handling
      await waitFor(() => {
        expect(
          screen.getByText(/network error|something went wrong|unable to connect/i)
        ).toBeInTheDocument();
      });
    });

    it('should handle authentication errors and redirect to login', async () => {
      // Mock 401 responses
      server.use(
        http.get('/api/v1/users/me', () => {
          return HttpResponse.json(
            { error: 'Unauthorized' },
            { status: 401 }
          );
        })
      );

      // Set invalid token
      localStorage.setItem('access_token', 'invalid-token');

      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );

      // Try to access protected content
      const profileLink = screen.getByRole('link', { name: /profile|account/i });
      await user.click(profileLink);

      // Verify redirect to login
      await waitFor(() => {
        expect(
          screen.getByText(/please log in|login required/i) ||
          screen.getByLabelText(/email|username/i)
        ).toBeInTheDocument();
      });
    });
  });

  describe('Performance and Loading States', () => {
    it('should show loading states during API calls', async () => {
      // Mock slow API response
      server.use(
        http.get('/api/v1/items', async () => {
          // Delay response
          await new Promise(resolve => setTimeout(resolve, 1000));
          return HttpResponse.json({
            items: [],
            total: 0,
            page: 1,
            per_page: 10,
          });
        })
      );

      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );

      // Trigger search
      const searchInput = screen.getByPlaceholderText(/search for items/i);
      const searchButton = screen.getByRole('button', { name: /search/i });

      await user.type(searchInput, 'test');
      await user.click(searchButton);

      // Verify loading state appears
      expect(
        screen.getByText(/loading|searching/i) ||
        screen.getByRole('progressbar') ||
        screen.getByTestId('loading-spinner')
      ).toBeInTheDocument();

      // Wait for loading to complete
      await waitFor(() => {
        expect(
          screen.queryByText(/loading|searching/i) &&
          screen.queryByRole('progressbar') &&
          screen.queryByTestId('loading-spinner')
        ).not.toBeInTheDocument();
      }, { timeout: 2000 });
    });
  });

  describe('Accessibility Throughout the App', () => {
    it('should maintain accessibility standards across all pages', async () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );

      // Test keyboard navigation
      await user.tab();
      expect(document.activeElement).toHaveAttribute('role', 'link');

      // Test screen reader content
      expect(screen.getByRole('main')).toBeInTheDocument();
      expect(screen.getByRole('navigation')).toBeInTheDocument();

      // Navigate to different pages and verify accessibility
      const searchInput = screen.getByPlaceholderText(/search for items/i);
      await user.type(searchInput, 'test');
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument();
      });

      // Verify search results are accessible
      const searchResults = screen.getByRole('main');
      expect(searchResults).toHaveAttribute('aria-live', 'polite');
    });
  });

  describe('Responsive Design Integration', () => {
    it('should work correctly on mobile viewport', async () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      Object.defineProperty(window, 'innerHeight', {
        writable: true,
        configurable: true,
        value: 667,
      });

      // Trigger resize event
      window.dispatchEvent(new Event('resize'));

      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );

      // Verify mobile-specific elements
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /menu|hamburger/i })).toBeInTheDocument();
      });

      // Test mobile navigation
      const menuButton = screen.getByRole('button', { name: /menu|hamburger/i });
      await user.click(menuButton);

      await waitFor(() => {
        expect(screen.getByRole('navigation')).toBeVisible();
      });
    });
  });
});
