/**
 * Recommendations Integration Tests
 * Tests the AI recommendation system with API integration
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { http, HttpResponse } from 'msw';
import { server } from '../../testing/mocks/server';

// Import components to test
import RecommendationsPage from '../../pages/RecommendationsPage';
import VisualizationPage from '../../pages/VisualizationPage';

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
      mutations: {
        retry: false,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Recommendations Integration Tests', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    // Mock user authentication
    localStorage.setItem('user_id', 'test-user-123');
  });

  describe('Personalized Recommendations', () => {
    it('should load and display personalized recommendations', async () => {
      // Mock recommendations API
      server.use(
        http.get('/api/v1/recommendations/personalized', () => {
          return HttpResponse.json([
            {
              id: '1',
              item_id: '1',
              score: 0.95,
              reason: 'Based on your previous rentals',
              item: {
                id: '1',
                title: 'Professional Camera',
                description: 'High-quality DSLR camera',
                price: 45.00,
                category: 'electronics',
                location: 'Test City',
              },
            },
            {
              id: '2',
              item_id: '2',
              score: 0.87,
              reason: 'Popular in your area',
              item: {
                id: '2',
                title: 'Mountain Bike',
                description: 'Perfect for outdoor adventures',
                price: 35.00,
                category: 'sports',
                location: 'Test City',
              },
            },
          ]);
        })
      );

      render(
        <TestWrapper>
          <RecommendationsPage />
        </TestWrapper>
      );

      // Wait for recommendations to load
      await waitFor(() => {
        expect(screen.getByText('Professional Camera')).toBeInTheDocument();
        expect(screen.getByText('Mountain Bike')).toBeInTheDocument();
      });

      // Check recommendation reasons
      expect(screen.getByText(/based on your previous rentals/i)).toBeInTheDocument();
      expect(screen.getByText(/popular in your area/i)).toBeInTheDocument();

      // Check recommendation scores are displayed (if your UI shows them)
      // expect(screen.getByText(/95%|0.95/)).toBeInTheDocument();
    });

    it('should handle empty recommendations', async () => {
      // Mock empty recommendations
      server.use(
        http.get('/api/v1/recommendations/personalized', () => {
          return HttpResponse.json([]);
        })
      );

      render(
        <TestWrapper>
          <RecommendationsPage />
        </TestWrapper>
      );

      // Wait for empty state message
      await waitFor(() => {
        expect(
          screen.getByText(/no recommendations available/i) ||
          screen.getByText(/we'll have recommendations for you soon/i)
        ).toBeInTheDocument();
      });
    });

    it('should handle recommendations API errors', async () => {
      // Mock API error
      server.use(
        http.get('/api/v1/recommendations/personalized', () => {
          return HttpResponse.error();
        })
      );

      render(
        <TestWrapper>
          <RecommendationsPage />
        </TestWrapper>
      );

      // Wait for error message
      await waitFor(() => {
        expect(
          screen.getByText(/error loading recommendations/i) ||
          screen.getByText(/something went wrong/i)
        ).toBeInTheDocument();
      });
    });
  });

  describe('Similar Items Recommendations', () => {
    it('should load similar items for a specific item', async () => {
      const itemId = 'test-item-123';

      // Mock similar items API
      server.use(
        http.get(`/api/v1/recommendations/similar/${itemId}`, () => {
          return HttpResponse.json([
            {
              id: '1',
              item_id: '1',
              score: 0.92,
              reason: 'Similar category and features',
              item: {
                id: '1',
                title: 'Similar Camera',
                description: 'Another great camera option',
                price: 40.00,
                category: 'electronics',
                location: 'Test City',
              },
            },
          ]);
        })
      );

      // This would be rendered on an item details page
      // For this test, we'll assume a component that shows similar items
      render(
        <TestWrapper>
          <div data-testid="similar-items">
            {/* Your similar items component would go here */}
          </div>
        </TestWrapper>
      );

      // Test would verify similar items are loaded and displayed
      // Implementation depends on your component structure
    });
  });

  describe('Preference Visualization', () => {
    it('should load and display user preference visualization', async () => {
      // Mock preference visualization API
      server.use(
        http.get('/api/v1/users/test-user-123/preferences/visualization', () => {
          return HttpResponse.json([
            { preferenceType: 'liked', weight: 0.8, count: 10 },
            { preferenceType: 'viewed', weight: 0.5, count: 20 },
            { preferenceType: 'searched', weight: 0.3, count: 15 },
          ]);
        })
      );

      render(
        <TestWrapper>
          <VisualizationPage />
        </TestWrapper>
      );

      // Wait for visualization to load
      await waitFor(() => {
        expect(screen.getByText(/preference/i)).toBeInTheDocument();
      });

      // Check that preference data is displayed
      // This depends on how your visualization component renders the data
      await waitFor(() => {
        expect(screen.getByText(/liked/i)).toBeInTheDocument();
        expect(screen.getByText(/viewed/i)).toBeInTheDocument();
        expect(screen.getByText(/searched/i)).toBeInTheDocument();
      });
    });

    it('should handle visualization API errors', async () => {
      // Mock API error
      server.use(
        http.get('/api/v1/users/test-user-123/preferences/visualization', () => {
          return HttpResponse.error();
        })
      );

      render(
        <TestWrapper>
          <VisualizationPage />
        </TestWrapper>
      );

      // Wait for error message
      await waitFor(() => {
        expect(
          screen.getByText(/error loading visualization/i) ||
          screen.getByText(/unable to load data/i)
        ).toBeInTheDocument();
      });
    });
  });

  describe('Embedding Visualization', () => {
    it('should load and display item embedding visualization', async () => {
      // Mock embedding visualization API
      server.use(
        http.get('/api/v1/users/test-user-123/embeddings/visualization', () => {
          return HttpResponse.json([
            { id: '1', x: 0.1, y: 0.2, category: 'electronics', name: 'Camera' },
            { id: '2', x: 0.3, y: 0.4, category: 'furniture', name: 'Chair' },
            { id: '3', x: 0.5, y: 0.6, category: 'electronics', name: 'Laptop' },
          ]);
        })
      );

      render(
        <TestWrapper>
          <VisualizationPage />
        </TestWrapper>
      );

      // Wait for embedding visualization to load
      await waitFor(() => {
        expect(screen.getByText(/embedding/i)).toBeInTheDocument();
      });

      // Check that embedding data is displayed
      // This depends on how your visualization component renders the data
      await waitFor(() => {
        expect(screen.getByText(/camera/i)).toBeInTheDocument();
        expect(screen.getByText(/chair/i)).toBeInTheDocument();
        expect(screen.getByText(/laptop/i)).toBeInTheDocument();
      });
    });
  });

  describe('Recommendation Interactions', () => {
    it('should handle clicking on recommended items', async () => {
      // Mock recommendations API
      server.use(
        http.get('/api/v1/recommendations/personalized', () => {
          return HttpResponse.json([
            {
              id: '1',
              item_id: '1',
              score: 0.95,
              reason: 'Based on your previous rentals',
              item: {
                id: '1',
                title: 'Professional Camera',
                description: 'High-quality DSLR camera',
                price: 45.00,
                category: 'electronics',
                location: 'Test City',
              },
            },
          ]);
        })
      );

      render(
        <TestWrapper>
          <RecommendationsPage />
        </TestWrapper>
      );

      // Wait for recommendations to load
      await waitFor(() => {
        expect(screen.getByText('Professional Camera')).toBeInTheDocument();
      });

      // Click on a recommended item
      const cameraItem = screen.getByText('Professional Camera');
      await user.click(cameraItem);

      // Verify navigation to item details
      // This depends on your routing implementation
      await waitFor(() => {
        expect(window.location.pathname).toContain('/items/1');
      });
    });

    it('should track recommendation clicks for analytics', async () => {
      // Mock analytics tracking
      const trackingMock = jest.fn();
      
      // This would depend on your analytics implementation
      // You might mock a tracking service or check for API calls
      
      // Mock recommendations API
      server.use(
        http.get('/api/v1/recommendations/personalized', () => {
          return HttpResponse.json([
            {
              id: '1',
              item_id: '1',
              score: 0.95,
              reason: 'Based on your previous rentals',
              item: {
                id: '1',
                title: 'Professional Camera',
                description: 'High-quality DSLR camera',
                price: 45.00,
                category: 'electronics',
                location: 'Test City',
              },
            },
          ]);
        }),
        // Mock analytics tracking endpoint
        http.post('/api/v1/analytics/recommendation-click', () => {
          trackingMock();
          return HttpResponse.json({ success: true });
        })
      );

      render(
        <TestWrapper>
          <RecommendationsPage />
        </TestWrapper>
      );

      // Wait for recommendations to load
      await waitFor(() => {
        expect(screen.getByText('Professional Camera')).toBeInTheDocument();
      });

      // Click on a recommended item
      const cameraItem = screen.getByText('Professional Camera');
      await user.click(cameraItem);

      // Verify tracking was called
      await waitFor(() => {
        expect(trackingMock).toHaveBeenCalled();
      });
    });
  });
});
