import React, { useState, useEffect } from 'react';
import { <PERSON>, But<PERSON>, Badge } from '../ui';

// Mock data types
interface AnalyticsSummary {
  totalViews: number;
  totalInteractions: number;
  conversionRate: number;
  averageRating: number;
}

interface CategoryPerformance {
  category: string;
  views: number;
  interactions: number;
  conversionRate: number;
}

interface TimeSeriesData {
  date: string;
  views: number;
  interactions: number;
}

interface UserSegment {
  segment: string;
  percentage: number;
  growth: number;
}

interface ResponsiveAnalyticsDashboardProps {
  title?: string;
  className?: string;
  itemId?: string;
  userId?: string;
  period?: 'day' | 'week' | 'month' | 'year';
}

/**
 * A responsive analytics dashboard component
 * Optimized for different screen sizes with collapsible sections on mobile
 */
const ResponsiveAnalyticsDashboard: React.FC<ResponsiveAnalyticsDashboardProps> = ({
  title = 'Analytics Dashboard',
  className = '',
  itemId,
  userId,
  period = 'month',
}) => {
  const [summary, setSummary] = useState<AnalyticsSummary | null>(null);
  const [categoryData, setCategoryData] = useState<CategoryPerformance[]>([]);
  const [timeSeriesData, setTimeSeriesData] = useState<TimeSeriesData[]>([]);
  const [userSegments, setUserSegments] = useState<UserSegment[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // Track which sections are expanded on mobile
  const [expandedSections, setExpandedSections] = useState({
    summary: true,
    categories: false,
    timeSeries: false,
    segments: false
  });

  // Toggle section expansion
  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Fetch analytics data
  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setLoading(true);
        
        // In a real implementation, this would fetch from an API
        // Mock data for demonstration
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock summary data
        setSummary({
          totalViews: 12458,
          totalInteractions: 3271,
          conversionRate: 0.26,
          averageRating: 4.7
        });
        
        // Mock category performance data
        setCategoryData([
          { category: 'Furniture', views: 5240, interactions: 1423, conversionRate: 0.31 },
          { category: 'Electronics', views: 3120, interactions: 876, conversionRate: 0.22 },
          { category: 'Kitchen', views: 2340, interactions: 612, conversionRate: 0.28 },
          { category: 'Outdoor', views: 1758, interactions: 360, conversionRate: 0.19 }
        ]);
        
        // Mock time series data
        setTimeSeriesData([
          { date: '2023-01', views: 980, interactions: 245 },
          { date: '2023-02', views: 1120, interactions: 312 },
          { date: '2023-03', views: 1340, interactions: 356 },
          { date: '2023-04', views: 1210, interactions: 298 },
          { date: '2023-05', views: 1450, interactions: 402 },
          { date: '2023-06', views: 1680, interactions: 489 }
        ]);
        
        // Mock user segment data
        setUserSegments([
          { segment: 'Young Professionals', percentage: 42, growth: 5.2 },
          { segment: 'Families', percentage: 28, growth: 3.1 },
          { segment: 'Students', percentage: 18, growth: 7.4 },
          { segment: 'Seniors', percentage: 12, growth: -1.2 }
        ]);
        
        setLoading(false);
      } catch (err) {
        console.error('Error fetching analytics data:', err);
        setError('Failed to load analytics data');
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [itemId, userId, period]);

  // Loading state
  if (loading) {
    return (
      <Card className={`${className}`} responsivePadding variant="elevated">
        <Card.Header title={title} />
        <Card.Content>
          <div className="animate-pulse space-y-4">
            <div className="h-20 bg-gray-200 rounded"></div>
            <div className="h-40 bg-gray-200 rounded"></div>
            <div className="h-40 bg-gray-200 rounded"></div>
          </div>
        </Card.Content>
      </Card>
    );
  }

  // Error state
  if (error) {
    return (
      <Card className={`${className}`} responsivePadding variant="elevated">
        <Card.Header title={title} />
        <Card.Content>
          <div className="p-4 bg-error-50 text-error-700 rounded-md">
            {error}
          </div>
        </Card.Content>
      </Card>
    );
  }

  // No data state
  if (!summary) {
    return (
      <Card className={`${className}`} responsivePadding variant="elevated">
        <Card.Header title={title} />
        <Card.Content>
          <div className="p-4 bg-gray-50 text-gray-500 text-center rounded-md">
            No analytics data available.
          </div>
        </Card.Content>
      </Card>
    );
  }

  return (
    <Card className={`${className}`} responsivePadding variant="elevated">
      <Card.Header title={title} />
      <Card.Content>
        {/* Summary Section - Always visible */}
        <div className="mb-6">
          <div className="md:hidden flex justify-between items-center mb-2">
            <h3 className="text-lg font-medium">Summary</h3>
            <button 
              className="text-gray-500"
              onClick={() => toggleSection('summary')}
              aria-label={expandedSections.summary ? 'Collapse summary' : 'Expand summary'}
              aria-expanded={expandedSections.summary}
            >
              {expandedSections.summary ? '−' : '+'}
            </button>
          </div>
          
          <h3 className="text-lg font-medium mb-3 hidden md:block">Summary</h3>
          
          <div className={`grid grid-cols-2 md:grid-cols-4 gap-3 ${expandedSections.summary ? '' : 'hidden md:grid'}`}>
            <div className="bg-primary-50 p-3 rounded-lg">
              <p className="text-sm text-gray-600">Total Views</p>
              <p className="text-2xl font-semibold text-primary-700">{summary.totalViews.toLocaleString()}</p>
            </div>
            <div className="bg-secondary-50 p-3 rounded-lg">
              <p className="text-sm text-gray-600">Interactions</p>
              <p className="text-2xl font-semibold text-secondary-700">{summary.totalInteractions.toLocaleString()}</p>
            </div>
            <div className="bg-accent-50 p-3 rounded-lg">
              <p className="text-sm text-gray-600">Conversion Rate</p>
              <p className="text-2xl font-semibold text-accent-700">{(summary.conversionRate * 100).toFixed(1)}%</p>
            </div>
            <div className="bg-success-50 p-3 rounded-lg">
              <p className="text-sm text-gray-600">Average Rating</p>
              <p className="text-2xl font-semibold text-success-700">{summary.averageRating.toFixed(1)}</p>
            </div>
          </div>
        </div>
        
        {/* Category Performance Section */}
        <div className="mb-6">
          <div className="md:hidden flex justify-between items-center mb-2">
            <h3 className="text-lg font-medium">Category Performance</h3>
            <button 
              className="text-gray-500"
              onClick={() => toggleSection('categories')}
              aria-label={expandedSections.categories ? 'Collapse categories' : 'Expand categories'}
              aria-expanded={expandedSections.categories}
            >
              {expandedSections.categories ? '−' : '+'}
            </button>
          </div>
          
          <h3 className="text-lg font-medium mb-3 hidden md:block">Category Performance</h3>
          
          <div className={expandedSections.categories ? '' : 'hidden md:block'}>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                    <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Views</th>
                    <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Interactions</th>
                    <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Conv. Rate</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {categoryData.map((category, index) => (
                    <tr key={index}>
                      <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{category.category}</td>
                      <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500 text-right">{category.views.toLocaleString()}</td>
                      <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500 text-right">{category.interactions.toLocaleString()}</td>
                      <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500 text-right">{(category.conversionRate * 100).toFixed(1)}%</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
        
        {/* Time Series Data Section */}
        <div className="mb-6">
          <div className="md:hidden flex justify-between items-center mb-2">
            <h3 className="text-lg font-medium">Performance Over Time</h3>
            <button 
              className="text-gray-500"
              onClick={() => toggleSection('timeSeries')}
              aria-label={expandedSections.timeSeries ? 'Collapse time series' : 'Expand time series'}
              aria-expanded={expandedSections.timeSeries}
            >
              {expandedSections.timeSeries ? '−' : '+'}
            </button>
          </div>
          
          <h3 className="text-lg font-medium mb-3 hidden md:block">Performance Over Time</h3>
          
          <div className={expandedSections.timeSeries ? '' : 'hidden md:block'}>
            {/* This would be a chart in a real implementation */}
            <div className="bg-gray-50 p-4 rounded-lg text-center">
              <p className="text-gray-500">Chart visualization would appear here</p>
              <div className="h-40 flex items-end justify-between mt-4 px-4">
                {timeSeriesData.map((data, index) => (
                  <div key={index} className="flex flex-col items-center">
                    <div className="w-8 bg-primary-500 rounded-t" style={{ height: `${data.views / 50}px` }}></div>
                    <p className="text-xs mt-1">{data.date.split('-')[1]}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
        
        {/* User Segments Section */}
        <div>
          <div className="md:hidden flex justify-between items-center mb-2">
            <h3 className="text-lg font-medium">User Segments</h3>
            <button 
              className="text-gray-500"
              onClick={() => toggleSection('segments')}
              aria-label={expandedSections.segments ? 'Collapse segments' : 'Expand segments'}
              aria-expanded={expandedSections.segments}
            >
              {expandedSections.segments ? '−' : '+'}
            </button>
          </div>
          
          <h3 className="text-lg font-medium mb-3 hidden md:block">User Segments</h3>
          
          <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 ${expandedSections.segments ? '' : 'hidden md:grid'}`}>
            {userSegments.map((segment, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-3">
                <h4 className="font-medium">{segment.segment}</h4>
                <p className="text-2xl font-semibold mt-1">{segment.percentage}%</p>
                <div className="flex items-center mt-2">
                  <Badge 
                    variant={segment.growth >= 0 ? 'success' : 'error'}
                    size="sm"
                  >
                    {segment.growth >= 0 ? '+' : ''}{segment.growth}%
                  </Badge>
                  <span className="text-xs text-gray-500 ml-2">vs. last period</span>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Action buttons */}
        <div className="mt-6 flex flex-col sm:flex-row gap-3 justify-center sm:justify-end">
          <Button
            variant="secondary"
            className="touch-friendly"
          >
            Export Data
          </Button>
          <Button
            className="touch-friendly"
          >
            View Full Report
          </Button>
        </div>
      </Card.Content>
    </Card>
  );
};

export default ResponsiveAnalyticsDashboard;
