import React from 'react';

interface ErrorFallbackProps {
  error?: Error | null;
  resetErrorBoundary?: () => void;
}

/**
 * A reusable error fallback component to display when an error occurs
 */
const ErrorFallback: React.FC<ErrorFallbackProps> = ({ error, resetErrorBoundary }) => {
  return (
    <div className="error-fallback p-6 bg-red-50 border border-red-200 rounded-lg max-w-2xl mx-auto my-8">
      <div className="flex items-center mb-4">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-8 w-8 text-red-600 mr-3"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
          />
        </svg>
        <h2 className="text-xl font-semibold text-red-700">Something went wrong</h2>
      </div>
      
      <p className="text-gray-700 mb-4">
        We're sorry, but an error occurred while rendering this page. Our team has been notified.
      </p>
      
      {error && (
        <details className="bg-white p-4 rounded-md shadow-sm mb-4">
          <summary className="cursor-pointer font-medium text-red-600 mb-2">
            View error details
          </summary>
          <pre className="mt-2 text-sm text-gray-700 overflow-auto p-2 bg-gray-50 rounded">
            {error.toString()}
          </pre>
        </details>
      )}
      
      <div className="flex space-x-4">
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
        >
          Reload Page
        </button>
        
        {resetErrorBoundary && (
          <button
            onClick={resetErrorBoundary}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
          >
            Try Again
          </button>
        )}
        
        <a
          href="/"
          className="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors"
        >
          Go to Home
        </a>
      </div>
    </div>
  );
};

export default ErrorFallback;
