import React, { useState } from 'react';
import use<PERSON>rror<PERSON>and<PERSON> from '../../hooks/useErrorHandler';
import ErrorBoundary from './ErrorBoundary';
import ErrorFallback from './ErrorFallback';

/**
 * A component that demonstrates various error handling techniques
 */
const ErrorDemo: React.FC = () => {
  const { handleError, tryCatch, createError, ErrorType } = useErrorHandler();
  const [count, setCount] = useState(0);

  // Function that will throw an error
  const throwError = () => {
    throw new Error('This is a manually thrown error');
  };

  // Async function that will throw an error
  const throwAsyncError = async () => {
    await new Promise(resolve => setTimeout(resolve, 500));
    throw new Error('This is an async error');
  };

  // Function that will create a network error
  const simulateNetworkError = async () => {
    await new Promise(resolve => setTimeout(resolve, 500));
    throw new TypeError('Failed to fetch');
  };

  // Function that will create a 401 error
  const simulateAuthError = async () => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const error = new Error('Unauthorized') as Error & { status: number };
    error.status = 401;
    throw error;
  };

  // Handle the error with our custom hook
  const handleManualError = () => {
    try {
      throwError();
    } catch (error) {
      handleError(error);
    }
  };

  // Handle an async error with our custom hook
  const handleAsyncError = async () => {
    await tryCatch(
      throwAsyncError,
      'Something went wrong while processing your request'
    );
  };

  // Create and handle a custom error
  const handleCustomError = () => {
    const error = createError(
      ErrorType.VALIDATION,
      'Invalid input data',
      'Please check your input and try again'
    );
    handleError(error);
  };

  // This will cause the ErrorBoundary to catch the error
  const triggerErrorBoundary = () => {
    setCount(prevCount => {
      if (prevCount > 2) {
        throw new Error('Count exceeded limit');
      }
      return prevCount + 1;
    });
  };

  return (
    <div className="error-demo max-w-2xl mx-auto my-8 p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">Error Handling Demo</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
        <button
          onClick={handleManualError}
          className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
        >
          Trigger Manual Error
        </button>

        <button
          onClick={handleAsyncError}
          className="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 transition-colors"
        >
          Trigger Async Error
        </button>

        <button
          onClick={() => tryCatch(simulateNetworkError)}
          className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 transition-colors"
        >
          Simulate Network Error
        </button>

        <button
          onClick={() => tryCatch(simulateAuthError)}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
        >
          Simulate Auth Error
        </button>

        <button
          onClick={handleCustomError}
          className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors"
        >
          Create Custom Error
        </button>
      </div>

      <div className="border-t border-gray-200 pt-6">
        <h3 className="text-xl font-semibold text-gray-800 mb-4">Error Boundary Demo</h3>
        <p className="text-gray-600 mb-4">
          Click the button below 3 times to trigger an error that will be caught by the ErrorBoundary.
        </p>

        <ErrorBoundary
          fallback={
            <ErrorFallback
              error={new Error('Count exceeded limit')}
              resetErrorBoundary={() => setCount(0)}
            />
          }
        >
          <div className="bg-gray-100 p-4 rounded-md mb-4">
            <p className="text-lg font-medium">Current count: {count}</p>
            <button
              onClick={triggerErrorBoundary}
              className="mt-2 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
            >
              Increment Count
            </button>
          </div>
        </ErrorBoundary>
      </div>
    </div>
  );
};

export default ErrorDemo;
