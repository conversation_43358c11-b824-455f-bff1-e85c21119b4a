import React, { useState, useRef, useEffect } from 'react';

interface FloatingDropdownProps {
  trigger: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  contentClassName?: string;
  placement?: 'bottom-left' | 'bottom-right' | 'bottom-center';
  onOpen?: () => void;
  onClose?: () => void;
}

const FloatingDropdown: React.FC<FloatingDropdownProps> = ({
  trigger,
  children,
  className = '',
  contentClassName = '',
  placement = 'bottom-left',
  onOpen,
  onClose
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleDropdown = () => {
    const newState = !isOpen;
    setIsOpen(newState);
    if (newState && onOpen) onOpen();
    if (!newState && onClose) onClose();
  };

  const closeDropdown = () => {
    if (isOpen) {
      setIsOpen(false);
      if (onClose) onClose();
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        closeDropdown();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const getPlacementClasses = () => {
    switch (placement) {
      case 'bottom-right':
        return 'right-0';
      case 'bottom-center':
        return 'left-1/2 transform -translate-x-1/2';
      case 'bottom-left':
      default:
        return 'left-0';
    }
  };

  return (
    <div className={`relative inline-block ${className}`} ref={dropdownRef}>
      <div onClick={toggleDropdown} className="cursor-pointer">
        {trigger}
      </div>
      
      {isOpen && (
        <div 
          className={`absolute z-50 mt-2 ${getPlacementClasses()} bg-white rounded-lg shadow-lg overflow-hidden ${contentClassName}`}
        >
          {children}
        </div>
      )}
    </div>
  );
};

export default FloatingDropdown;
