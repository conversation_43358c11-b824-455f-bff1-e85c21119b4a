import React, { useState, useEffect } from 'react';
import { MAIN_CATEGORIES } from '../../config/categories';
import CategoryIcon from './CategoryIcons';
import { InteractiveLink } from '../../shared/ui';
import { getCategories, Category } from '../../services/categoryService';

const CategoryShowcase: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        const data = await getCategories({ limit: 8 });
        setCategories(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError('Failed to load categories. Using fallback data.');
        // Fallback to static data if API fails
        setCategories(MAIN_CATEGORIES.slice(0, 8).map(cat => ({
          id: cat.id,
          name: cat.name,
          description: cat.description,
          icon: cat.icon,
          is_prohibited: false,
          subcategories: cat.subCategories?.map(sub => ({
            id: sub.id,
            name: sub.name,
            description: sub.description,
            is_prohibited: false
          }))
        })));
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Use API data or fallback to static data
  const displayCategories = categories.length > 0 ? categories : MAIN_CATEGORIES.slice(0, 8);

  return (
    <section className="py-20 bg-white" aria-labelledby="category-heading">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <span className="text-accent font-semibold text-sm uppercase tracking-wider mb-2 inline-block">Explore Our Categories</span>
          <h2 id="category-heading" className="text-3xl md:text-4xl font-bold text-near-black mb-4 font-display">Browse by Category</h2>
          <p className="text-dark-gray max-w-2xl mx-auto text-lg">
            Discover thousands of items available for rent across our diverse categories
          </p>
          <div className="w-24 h-1 bg-accent mx-auto mt-6 rounded-full"></div>
          {error && (
            <div className="mt-4 text-sm text-red-600">
              {error}
            </div>
          )}
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-6 md:gap-8">
            {displayCategories.map((category) => (
              <InteractiveLink
                key={category.id}
                to={`/search?category=${category.id}`}
                className="group focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-xl overflow-hidden"
                aria-label={`${category.name} category with ${category.subcategories?.length || 0} subcategories`}
                variant="primary"
                title={`Browse ${category.name} items`}
              >
                <div className="bg-light-gray rounded-xl p-6 md:p-8 flex flex-col items-center justify-center text-center h-full min-h-[220px] transition-all duration-300 group-hover:shadow-xl group-hover:bg-white border border-gray-100 group-hover:border-primary/20 transform group-hover:-translate-y-2 group-focus:shadow-lg group-focus:border-primary relative z-10 overflow-hidden">
                  {/* Background gradient on hover */}
                  <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>

                  {/* Icon with animated background */}
                  <div className="relative mb-6">
                    <div className="absolute inset-0 bg-primary/10 rounded-full w-20 h-20 -m-4 transform scale-0 group-hover:scale-100 transition-transform duration-300"></div>
                    <div className="text-primary transform group-hover:scale-110 transition-transform duration-300" aria-hidden="true">
                      <CategoryIcon category={category.id} className="w-16 h-16" />
                    </div>
                  </div>

                  <h3 className="font-semibold text-near-black mb-3 group-hover:text-primary transition-colors text-lg md:text-xl">
                    {category.name}
                  </h3>
                  <p className="text-sm text-medium-gray group-hover:text-dark-gray transition-colors">
                    {category.subcategories?.length || 0} subcategories
                  </p>

                  {/* Category description on hover */}
                  <div className="mt-3 h-0 overflow-hidden group-hover:h-auto transition-all duration-300">
                    <p className="text-xs text-medium-gray italic">{category.description}</p>
                  </div>

                  {/* Animated arrow on hover */}
                  <div className="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mx-auto animate-pulse" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              </InteractiveLink>
            ))}
          </div>
        )}

        <div className="text-center mt-16">
          <InteractiveLink
            to="/search"
            className="inline-block text-white px-10 py-4 rounded-md transition-all font-semibold shadow-md hover:shadow-lg transform hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 text-lg"
            aria-label="View all categories"
            title="Browse all available categories"
            variant="primary"
          >
            <span className="flex items-center">
              <span>View All Categories</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </span>
          </InteractiveLink>
        </div>
      </div>
    </section>
  );
};

export default CategoryShowcase;
