import React from 'react';
import CategoryCard from './CategoryCard';

interface Category {
  name: string;
  icon: React.ReactNode;
  count?: number;
  to: string;
}

interface CategorySectionProps {
  title: string;
  subtitle?: string;
  categories: Category[];
}

const CategorySection: React.FC<CategorySectionProps> = ({ title, subtitle, categories }) => {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-near-black mb-4">{title}</h2>
          {subtitle && <p className="text-lg text-dark-gray max-w-2xl mx-auto">{subtitle}</p>}
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6 justify-items-center mx-auto max-w-7xl">
          {categories.map((category, index) => (
            <CategoryCard
              key={index}
              name={category.name}
              icon={category.icon}
              count={category.count}
              to={category.to}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default CategorySection;
