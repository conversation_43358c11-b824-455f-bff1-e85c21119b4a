import React, { useState, useEffect } from 'react';
import { MAIN_CATEGORIES, Category, findCategoryById } from '../config/categories';

interface CategorySelectorProps {
  selectedMainCategory?: string;
  selectedSubCategory?: string;
  onMainCategoryChange: (categoryId: string) => void;
  onSubCategoryChange: (categoryId: string) => void;
  allowSuggestion?: boolean;
}

const CategorySelector: React.FC<CategorySelectorProps> = ({
  selectedMainCategory,
  selectedSubCategory,
  onMainCategoryChange,
  onSubCategoryChange,
  allowSuggestion = true
}) => {
  const [mainCategory, setMainCategory] = useState<string | undefined>(selectedMainCategory);
  const [subCategory, setSubCategory] = useState<string | undefined>(selectedSubCategory);
  const [showSuggestionForm, setShowSuggestionForm] = useState(false);
  const [suggestion, setSuggestion] = useState('');
  const [suggestionDescription, setSuggestionDescription] = useState('');

  // Update local state when props change
  useEffect(() => {
    setMainCategory(selectedMainCategory);
    setSubCategory(selectedSubCategory);
  }, [selectedMainCategory, selectedSubCategory]);

  // Get subcategories for the selected main category
  const getSubCategories = (): Category[] => {
    if (!mainCategory) return [];
    const category = findCategoryById(mainCategory);
    return category?.subCategories || [];
  };

  // Handle main category change
  const handleMainCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newMainCategory = e.target.value;
    setMainCategory(newMainCategory);
    setSubCategory(undefined); // Reset subcategory when main category changes
    onMainCategoryChange(newMainCategory);
  };

  // Handle subcategory change
  const handleSubCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSubCategory = e.target.value;
    setSubCategory(newSubCategory);
    onSubCategoryChange(newSubCategory);
  };

  // Handle suggestion submission
  const handleSuggestionSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // In a real implementation, this would send the suggestion to the backend
    console.log('Category suggestion:', {
      parentCategory: mainCategory,
      suggestion,
      description: suggestionDescription
    });
    
    // Show success message and reset form
    alert('Thank you for your suggestion! Our team will review it shortly.');
    setSuggestion('');
    setSuggestionDescription('');
    setShowSuggestionForm(false);
  };

  return (
    <div className="space-y-4">
      {/* Main Category Selector */}
      <div>
        <label htmlFor="mainCategory" className="block text-sm font-medium text-gray-700 mb-1">
          Main Category
        </label>
        <select
          id="mainCategory"
          value={mainCategory || ''}
          onChange={handleMainCategoryChange}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
          required
        >
          <option value="" disabled>Select a category</option>
          {MAIN_CATEGORIES.map((category) => (
            <option key={category.id} value={category.id}>
              {category.name}
            </option>
          ))}
        </select>
      </div>

      {/* Sub Category Selector (only shown if main category is selected) */}
      {mainCategory && (
        <div>
          <label htmlFor="subCategory" className="block text-sm font-medium text-gray-700 mb-1">
            Sub Category
          </label>
          <select
            id="subCategory"
            value={subCategory || ''}
            onChange={handleSubCategoryChange}
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
            required
          >
            <option value="" disabled>Select a sub-category</option>
            {getSubCategories().map((category) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
          <p className="mt-1 text-xs text-gray-500">
            {subCategory ? findCategoryById(subCategory)?.description : 'Select a sub-category for your item'}
          </p>
        </div>
      )}

      {/* Category Suggestion Link */}
      {allowSuggestion && mainCategory && (
        <div className="mt-2">
          <button
            type="button"
            onClick={() => setShowSuggestionForm(!showSuggestionForm)}
            className="text-sm text-primary hover:text-primary-dark"
          >
            Can't find the right category? Suggest a new one
          </button>
        </div>
      )}

      {/* Category Suggestion Form */}
      {showSuggestionForm && (
        <div className="mt-4 p-4 bg-gray-50 rounded-md">
          <h4 className="text-sm font-medium mb-2">Suggest a New Category</h4>
          <form onSubmit={handleSuggestionSubmit}>
            <div className="mb-3">
              <label htmlFor="suggestion" className="block text-sm text-gray-700 mb-1">
                Category Name
              </label>
              <input
                type="text"
                id="suggestion"
                value={suggestion}
                onChange={(e) => setSuggestion(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                required
              />
            </div>
            <div className="mb-3">
              <label htmlFor="suggestionDescription" className="block text-sm text-gray-700 mb-1">
                Description
              </label>
              <textarea
                id="suggestionDescription"
                value={suggestionDescription}
                onChange={(e) => setSuggestionDescription(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                rows={3}
                required
              />
            </div>
            <div className="flex justify-end space-x-2">
              <button
                type="button"
                onClick={() => setShowSuggestionForm(false)}
                className="px-3 py-1 text-sm text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-3 py-1 text-sm text-white bg-primary rounded-md hover:bg-primary-dark"
              >
                Submit Suggestion
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
};

export default CategorySelector;
