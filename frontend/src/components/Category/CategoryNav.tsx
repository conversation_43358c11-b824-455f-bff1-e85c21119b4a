import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { MAIN_CATEGORIES } from '../config/categories';

const CategoryNav: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState<string | null>(null);

  const handleMouseEnter = (categoryId: string) => {
    setActiveCategory(categoryId);
  };

  const handleMouseLeave = () => {
    setActiveCategory(null);
  };

  return (
    <div className="bg-white border-b border-gray-200">
      <div className="container mx-auto px-4">
        <div className="flex items-center">
          <div className="relative group py-3 px-4 bg-primary text-white font-medium flex items-center">
            <span className="mr-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </span>
            <span>Rent By Categories</span>

            {/* Dropdown for all categories */}
            <div className="absolute top-full left-0 w-64 bg-white shadow-lg rounded-b-md z-50 hidden group-hover:block">
              <ul className="py-2">
                {MAIN_CATEGORIES.map((category) => (
                  <li key={category.id} className="relative group/item">
                    <Link
                      to={`/search?category=${category.id}`}
                      className="flex px-4 py-2 text-dark-gray hover:bg-light-gray hover:text-primary justify-between items-center"
                    >
                      {category.name}
                      {category.subCategories && category.subCategories.length > 0 && (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      )}
                    </Link>

                    {/* Subcategories dropdown */}
                    {category.subCategories && category.subCategories.length > 0 && (
                      <div className="absolute top-0 left-full w-64 bg-white shadow-lg rounded-md z-50 hidden group-hover/item:block">
                        <ul className="py-2">
                          {category.subCategories.map((subCategory) => (
                            <li key={subCategory.id}>
                              <Link
                                to={`/search?category=${category.id}&subcategory=${subCategory.id}`}
                                className="block px-4 py-2 text-dark-gray hover:bg-light-gray hover:text-primary"
                              >
                                {subCategory.name}
                              </Link>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Main category navigation */}
          <nav className="flex-1">
            <ul className="flex">
              {MAIN_CATEGORIES.slice(0, 8).map((category) => (
                <li
                  key={category.id}
                  className="relative"
                  onMouseEnter={() => handleMouseEnter(category.id)}
                  onMouseLeave={handleMouseLeave}
                >
                  <Link
                    to={`/search?category=${category.id}`}
                    className="flex px-4 py-3 text-dark-gray hover:text-primary items-center"
                  >
                    {category.name}
                    {category.subCategories && category.subCategories.length > 0 && (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    )}
                  </Link>

                  {/* Mega menu dropdown */}
                  {activeCategory === category.id && category.subCategories && category.subCategories.length > 0 && (
                    <div className="absolute top-full left-0 w-full bg-white shadow-lg z-50 p-6 grid grid-cols-4 gap-6">
                      <div className="col-span-3 grid grid-cols-3 gap-6">
                        {category.subCategories.map((subCategory) => (
                          <div key={subCategory.id}>
                            <h3 className="font-semibold text-near-black mb-3">{subCategory.name}</h3>
                            <p className="text-sm text-medium-gray mb-2">{subCategory.description}</p>
                            <Link
                              to={`/search?category=${category.id}&subcategory=${subCategory.id}`}
                              className="text-sm text-primary hover:underline"
                            >
                              Browse {subCategory.name}
                            </Link>
                          </div>
                        ))}
                      </div>
                      <div className="col-span-1">
                        <div className="bg-light-gray p-4 rounded-md">
                          <h3 className="font-semibold text-near-black mb-2">Featured</h3>
                          <div className="aspect-w-1 aspect-h-1 bg-white rounded-md overflow-hidden mb-3">
                            <img
                              src="https://placehold.jp/300x300.png"
                              alt="Featured item"
                              className="object-cover w-full h-full"
                            />
                          </div>
                          <h4 className="text-sm font-medium text-dark-gray mb-1">Featured Product</h4>
                          <p className="text-sm text-primary font-semibold">$25/day</p>
                        </div>
                      </div>
                    </div>
                  )}
                </li>
              ))}
            </ul>
          </nav>
        </div>
      </div>
    </div>
  );
};

export default CategoryNav;
