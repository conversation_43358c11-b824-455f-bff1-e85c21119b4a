import React from 'react';
import { Link } from 'react-router-dom';

interface CategoryCardProps {
  name: string;
  icon: React.ReactNode;
  count?: number;
  to: string;
}

const CategoryCard: React.FC<CategoryCardProps> = ({ name, icon, count, to }) => {
  return (
    <Link
      to={to}
      className="flex flex-col items-center justify-center p-6 bg-white rounded-lg shadow-sm border border-light-gray hover:shadow-md transition-shadow group text-center h-full w-full max-w-[200px]"
    >
      <div className="text-primary mb-3 group-hover:scale-110 transition-transform">
        {icon}
      </div>
      <h3 className="text-lg font-semibold text-near-black">{name}</h3>
      {count !== undefined && (
        <p className="text-sm text-medium-gray mt-1">{count} items</p>
      )}
    </Link>
  );
};

export default CategoryCard;
