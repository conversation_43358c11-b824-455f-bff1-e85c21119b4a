import React from 'react';
import { Link } from 'react-router-dom';
import FloatingDropdown from './FloatingDropdown';
import { MAIN_CATEGORIES } from '../config/categories';
import '../styles/dropdown-fix.css';

interface ShopByCategoriesDropdownProps {
  onCategorySelect?: () => void;
}

const ShopByCategoriesDropdown: React.FC<ShopByCategoriesDropdownProps> = ({
  onCategorySelect,
}) => {
  return (
    <FloatingDropdown
      trigger={
        <button
          className="relative group py-3 px-4 bg-primary text-white font-medium flex items-center cursor-pointer"
          title="Browse categories"
          aria-label="Toggle categories menu"
        >
          <span className="mr-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </span>
          <span className="hidden sm:inline">Rent By Categories</span>
          <span className="sm:hidden">Categories</span>
        </button>
      }
      content={
        <div style={{ paddingTop: '2px' }}>
          <div className="bg-white shadow-lg rounded-b-md z-50 w-64 max-h-[calc(100vh-100px)] overflow-y-auto border-none categories-dropdown">
          <ul className="py-2">
            {MAIN_CATEGORIES.map((category) => (
              <li key={category.id} role="none">
                <a
                  href={`/search?category=${category.id}`}
                  className="flex px-4 py-2 text-dark-gray hover:bg-blue-50 hover:text-blue-600 items-center w-full border-none"
                  onClick={onCategorySelect}
                  role="menuitem"
                  title={`Browse ${category.name}`}
                >
                  <span className="flex items-center">
                    <span className="text-blue-600 mr-2">{category.icon}</span>
                    <span>{category.name}</span>
                  </span>
                </a>
              </li>
            ))}
            <li role="none">
              <Link
                to="/search"
                className="block px-4 py-2 text-dark-gray hover:bg-blue-50 hover:text-blue-600 font-medium border-none all-categories w-full"
                onClick={onCategorySelect}
                role="menuitem"
              >
                View All Categories
              </Link>
            </li>
          </ul>
          </div>
        </div>
      }
      placement="bottom"
      offset={2}
      closeOnClickOutside={true}
    />
  );
};

export default ShopByCategoriesDropdown;
