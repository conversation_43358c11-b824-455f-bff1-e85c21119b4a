import React from 'react';
import { Link } from 'react-router-dom';
import FloatingDropdown from './FloatingDropdown';
import { MAIN_CATEGORIES } from '../config/categories';
import '../styles/dropdown-fix.css';

interface CategoryDropdownProps {
  onCategorySelect?: () => void;
  buttonClassName?: string;
  dropdownClassName?: string;
}

const CategoryDropdown: React.FC<CategoryDropdownProps> = ({
  onCategorySelect,
  buttonClassName = '',
  dropdownClassName = '',
}) => {
  return (
    <FloatingDropdown
      trigger={
        <button
          type="button"
          className={`h-full flex items-center px-4 py-2 bg-white border border-gray-300 border-r-0 rounded-l-md text-dark-gray hover:bg-blue-50 transition-colors ${buttonClassName}`}
          aria-haspopup="true"
        >
          <span className="flex items-center">
            <span>All Categories</span>
            <svg
              className="w-4 h-4 ml-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </span>
        </button>
      }
      content={
        <div style={{ paddingTop: '2px' }}>
          <div
            className={`bg-white rounded-md shadow-lg py-1 w-64 max-h-[calc(100vh-100px)] overflow-y-auto border-none categories-dropdown ${dropdownClassName}`}
          >
          {/* All Categories Option */}
          <Link
            to="/search"
            className="flex px-4 py-2 text-dark-gray hover:bg-blue-50 hover:text-blue-600 items-center font-medium border-none all-categories w-full"
            onClick={onCategorySelect}
          >
            All Categories
          </Link>

          {/* Divider */}
          <div className="my-1 h-px bg-gray-100"></div>

          {/* Category List */}
          {MAIN_CATEGORIES.map((category) => (
            <Link
              key={category.id}
              to={`/search?category=${category.id}`}
              className="flex px-4 py-2 text-dark-gray hover:bg-blue-50 hover:text-blue-600 justify-between items-center w-full group border-none"
              onClick={onCategorySelect}
            >
              <span className="flex items-center">
                <span>{category.name}</span>
              </span>
            </Link>
          ))}
          </div>
        </div>
      }
      placement="bottom"
      offset={2}
      closeOnClickOutside={true}
    />
  );
};

export default CategoryDropdown;
