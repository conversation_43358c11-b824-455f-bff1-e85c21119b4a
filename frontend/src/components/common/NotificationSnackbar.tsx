import React from 'react';
import { Snackbar, Alert } from '@mui/material';

/**
 * NotificationSnackbar Component
 * 
 * Shared notification component that displays messages using Material-UI Snackbar.
 * Supports different severity levels (success, error, info, warning).
 * 
 * @example
 * ```tsx
 * import { NotificationSnackbar } from '@/components/common/NotificationSnackbar';
 * 
 * const [snackbar, setSnackbar] = useState({
 *   open: false,
 *   message: '',
 *   severity: 'info' as const
 * });
 * 
 * <NotificationSnackbar
 *   snackbar={snackbar}
 *   onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
 * />
 * ```
 */
export interface SnackbarState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'info' | 'warning';
}

export interface NotificationSnackbarProps {
  snackbar: SnackbarState;
  onClose: () => void;
  autoHideDuration?: number;
  anchorOrigin?: {
    vertical: 'top' | 'bottom';
    horizontal: 'left' | 'center' | 'right';
  };
}

export const NotificationSnackbar: React.FC<NotificationSnackbarProps> = ({
  snackbar,
  onClose,
  autoHideDuration = 6000,
  anchorOrigin = { vertical: 'bottom', horizontal: 'right' }
}) => {
  return (
    <Snackbar
      open={snackbar.open}
      autoHideDuration={autoHideDuration}
      onClose={onClose}
      anchorOrigin={anchorOrigin}
    >
      <Alert 
        onClose={onClose} 
        severity={snackbar.severity}
        variant="filled"
        sx={{ width: '100%' }}
      >
        {snackbar.message}
      </Alert>
    </Snackbar>
  );
};

export default NotificationSnackbar;
