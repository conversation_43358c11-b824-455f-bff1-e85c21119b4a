import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { <PERSON>ton, Card, Input, Select, Badge } from '../ui';

interface CurationOption {
  id: string;
  name: string;
  description: string;
  category: string;
}

interface CurationResult {
  id: string;
  name: string;
  score: number;
  reason: string;
}

interface ResponsiveAICurationInterfaceProps {
  title?: string;
  description?: string;
  className?: string;
}

/**
 * A responsive AI curation interface component
 * Optimized for different screen sizes with simplified controls on mobile
 */
const ResponsiveAICurationInterface: React.FC<ResponsiveAICurationInterfaceProps> = ({
  title = 'AI Curation',
  description = 'Use AI to curate items based on your preferences',
  className = '',
}) => {
  const [options, setOptions] = useState<CurationOption[]>([]);
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const [advancedMode, setAdvancedMode] = useState<boolean>(false);
  const [customPrompt, setCustomPrompt] = useState<string>('');
  const [results, setResults] = useState<CurationResult[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState<boolean>(false);

  // Fetch curation options
  useEffect(() => {
    const fetchOptions = async () => {
      try {
        // In a real implementation, this would fetch from an API
        // Mock data for demonstration
        const mockOptions: CurationOption[] = [
          { id: 'opt1', name: 'Modern', description: 'Modern style items', category: 'Style' },
          { id: 'opt2', name: 'Vintage', description: 'Vintage and retro items', category: 'Style' },
          { id: 'opt3', name: 'Minimalist', description: 'Simple, clean designs', category: 'Style' },
          { id: 'opt4', name: 'Luxury', description: 'High-end premium items', category: 'Quality' },
          { id: 'opt5', name: 'Budget', description: 'Affordable options', category: 'Quality' },
          { id: 'opt6', name: 'Sustainable', description: 'Eco-friendly options', category: 'Features' },
          { id: 'opt7', name: 'Smart Home', description: 'Connected devices', category: 'Features' },
          { id: 'opt8', name: 'Family-friendly', description: 'Good for families', category: 'Usage' },
        ];
        
        setOptions(mockOptions);
      } catch (err) {
        console.error('Error fetching curation options:', err);
        setError('Failed to load curation options');
      }
    };

    fetchOptions();
  }, []);

  // Toggle option selection
  const toggleOption = (optionId: string) => {
    setSelectedOptions(prev => 
      prev.includes(optionId)
        ? prev.filter(id => id !== optionId)
        : [...prev, optionId]
    );
  };

  // Submit curation request
  const submitCuration = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // In a real implementation, this would call an API
      // Mock data for demonstration
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const mockResults: CurationResult[] = [
        { id: 'item1', name: 'Modern Sofa', score: 0.95, reason: 'Matches your modern style preference' },
        { id: 'item2', name: 'Minimalist Desk', score: 0.89, reason: 'Aligns with your minimalist preference' },
        { id: 'item3', name: 'Smart Lighting', score: 0.82, reason: 'Matches your smart home preference' },
        { id: 'item4', name: 'Eco-friendly Chair', score: 0.78, reason: 'Sustainable materials as preferred' },
      ];
      
      setResults(mockResults);
      setLoading(false);
    } catch (err) {
      console.error('Error submitting curation request:', err);
      setError('Failed to process curation request');
      setLoading(false);
    }
  };

  // Reset all selections
  const resetSelections = () => {
    setSelectedOptions([]);
    setCustomPrompt('');
    setResults([]);
  };

  // Group options by category for better organization
  const optionsByCategory = options.reduce((acc, option) => {
    if (!acc[option.category]) {
      acc[option.category] = [];
    }
    acc[option.category].push(option);
    return acc;
  }, {} as Record<string, CurationOption[]>);

  return (
    <Card 
      className={`${className}`}
      responsivePadding
      variant="elevated"
    >
      <Card.Header 
        title={title}
        subtitle={description}
      />
      
      <Card.Content>
        {/* Mobile view: Simplified controls */}
        <div className="md:hidden">
          <Select
            label="Select preferences"
            options={options.map(opt => ({ 
              value: opt.id, 
              label: `${opt.name} (${opt.category})` 
            }))}
            value=""
            onChange={(e) => toggleOption(e.target.value)}
            placeholder="Select preferences"
            className="mb-4"
          />
          
          {/* Selected options display */}
          {selectedOptions.length > 0 && (
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-2">Selected preferences:</p>
              <div className="flex flex-wrap gap-2">
                {selectedOptions.map(optId => {
                  const option = options.find(o => o.id === optId);
                  return option ? (
                    <Badge 
                      key={optId}
                      variant="primary"
                      className="flex items-center"
                    >
                      {option.name}
                      <button 
                        className="ml-1 text-xs"
                        onClick={() => toggleOption(optId)}
                        aria-label={`Remove ${option.name}`}
                      >
                        ×
                      </button>
                    </Badge>
                  ) : null;
                })}
              </div>
            </div>
          )}
          
          {/* Advanced options toggle */}
          <div className="mb-4">
            <button
              className="text-sm text-primary-600 flex items-center"
              onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
            >
              {showAdvancedOptions ? 'Hide' : 'Show'} advanced options
              <span className="ml-1">{showAdvancedOptions ? '▲' : '▼'}</span>
            </button>
            
            {showAdvancedOptions && (
              <div className="mt-3">
                <Input
                  label="Custom prompt"
                  placeholder="Describe what you're looking for..."
                  value={customPrompt}
                  onChange={(e) => setCustomPrompt(e.target.value)}
                  className="mb-3"
                />
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="advancedMode-mobile"
                    checked={advancedMode}
                    onChange={() => setAdvancedMode(!advancedMode)}
                    className="mr-2 h-4 w-4"
                  />
                  <label htmlFor="advancedMode-mobile" className="text-sm">
                    Use advanced AI model
                  </label>
                </div>
              </div>
            )}
          </div>
        </div>
        
        {/* Desktop view: Full controls */}
        <div className="hidden md:block">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {Object.entries(optionsByCategory).map(([category, categoryOptions]) => (
              <div key={category}>
                <h4 className="text-lg font-medium mb-3">{category}</h4>
                <div className="flex flex-wrap gap-2">
                  {categoryOptions.map(option => (
                    <button
                      key={option.id}
                      className={`px-3 py-2 rounded-md text-sm transition-colors ${
                        selectedOptions.includes(option.id)
                          ? 'bg-primary-100 text-primary-800 border border-primary-300'
                          : 'bg-gray-100 text-gray-800 border border-gray-200 hover:bg-gray-200'
                      }`}
                      onClick={() => toggleOption(option.id)}
                      title={option.description}
                    >
                      {option.name}
                    </button>
                  ))}
                </div>
              </div>
            ))}
          </div>
          
          <div className="mb-6">
            <Input
              label="Custom prompt (optional)"
              placeholder="Describe what you're looking for in your own words..."
              value={customPrompt}
              onChange={(e) => setCustomPrompt(e.target.value)}
            />
          </div>
          
          <div className="flex items-center mb-6">
            <input
              type="checkbox"
              id="advancedMode-desktop"
              checked={advancedMode}
              onChange={() => setAdvancedMode(!advancedMode)}
              className="mr-2 h-4 w-4"
            />
            <label htmlFor="advancedMode-desktop">
              Use advanced AI model (may take longer but provides better results)
            </label>
          </div>
        </div>
        
        {/* Action buttons - same for both views */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center sm:justify-end">
          <Button
            variant="tertiary"
            onClick={resetSelections}
            disabled={loading || (selectedOptions.length === 0 && !customPrompt)}
            className="touch-friendly"
          >
            Reset
          </Button>
          <Button
            onClick={submitCuration}
            disabled={loading || (selectedOptions.length === 0 && !customPrompt)}
            loading={loading}
            className="touch-friendly"
          >
            {loading ? 'Curating...' : 'Curate Items'}
          </Button>
        </div>
        
        {/* Error message */}
        {error && (
          <div className="mt-4 p-3 bg-error-50 text-error-700 rounded-md">
            {error}
          </div>
        )}
        
        {/* Results section */}
        {results.length > 0 && (
          <div className="mt-6">
            <h4 className="text-lg font-medium mb-3">Curated Items</h4>
            <div className="grid-responsive-cards">
              {results.map(result => (
                <Card key={result.id} variant="outlined" interactive>
                  <Card.Header 
                    title={result.name} 
                    subtitle={`Match score: ${Math.round(result.score * 100)}%`}
                  />
                  <Card.Content>
                    <p className="text-sm text-gray-600">{result.reason}</p>
                  </Card.Content>
                  <Card.Footer>
                    <Button size="sm" variant="secondary" fullWidth>View Item</Button>
                  </Card.Footer>
                </Card>
              ))}
            </div>
          </div>
        )}
      </Card.Content>
    </Card>
  );
};

export default ResponsiveAICurationInterface;
