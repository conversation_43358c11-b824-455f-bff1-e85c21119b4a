import React, { useState } from 'react';
import { FaChevronDown, FaChevronUp, FaStar, FaHeart, FaEye, FaShoppingCart } from 'react-icons/fa';

interface ResponsiveRecommendationSectionProps {
  title: string;
  type: 'personalized' | 'similar' | 'trending';
  limit?: number;
  collapsible?: boolean;
  initiallyExpanded?: boolean;
}

interface ResponsiveAICurationInterfaceProps {
  title: string;
  description?: string;
}

interface ResponsiveAnalyticsDashboardProps {
  title: string;
  period: 'day' | 'week' | 'month' | 'year';
}

interface ItemCardProps {
  id: string;
  title: string;
  category: string;
  price: number;
  priceUnit: string;
  imageUrl: string;
  secondaryImageUrl?: string;
  reliability: {
    score: number;
    level: 'excellent' | 'good' | 'average' | 'poor';
  };
  owner: {
    name: string;
    avatarUrl: string;
    rating: number;
    reviewCount: number;
  };
}

/**
 * ResponsiveRecommendationSection Component
 *
 * A responsive grid of AI-recommended items that adapts to different screen sizes:
 * - Mobile: Single column, collapsible
 * - Tablet: Two columns
 * - Desktop: Three columns
 */
export const ResponsiveRecommendationSection: React.FC<ResponsiveRecommendationSectionProps> = ({
  title,
  type,
  limit = 6,
  collapsible = true,
  initiallyExpanded = true,
}) => {
  const [expanded, setExpanded] = useState(initiallyExpanded);
  const [items, setItems] = useState<ItemCardProps[]>(getMockItems(limit));

  // Mock function to get items - in a real implementation, this would be an API call
  function getMockItems(count: number): ItemCardProps[] {
    return Array.from({ length: count }, (_, i) => ({
      id: `item-${i}`,
      title: `Item ${i + 1}`,
      category: ['Electronics', 'Home & Garden', 'Sports', 'Tools'][i % 4],
      price: 10 + i * 5,
      priceUnit: 'day',
      imageUrl: `https://source.unsplash.com/random/300x200?sig=${i}`,
      secondaryImageUrl: `https://source.unsplash.com/random/300x200?sig=${i + 100}`,
      reliability: {
        score: 70 + Math.floor(Math.random() * 30),
        level: ['good', 'excellent', 'good', 'average'][i % 4] as 'excellent' | 'good' | 'average' | 'poor',
      },
      owner: {
        name: `User ${i + 1}`,
        avatarUrl: `https://i.pravatar.cc/150?img=${i + 10}`,
        rating: 3.5 + Math.random() * 1.5,
        reviewCount: 10 + i,
      },
    }));
  }

  return (
    <div className="collapsible-section">
      <button
        className="collapsible-section-header"
        onClick={() => collapsible && setExpanded(!expanded)}
        aria-expanded={expanded}
      >
        <h2 className="text-xl font-semibold">{title}</h2>
        {collapsible && (
          <span className="collapsible-section-toggle">
            {expanded ? <FaChevronUp /> : <FaChevronDown />}
          </span>
        )}
      </button>
      <div className={`collapsible-section-content ${expanded ? 'expanded' : ''}`}>
        <div className="grid-responsive-cards">
          {items.map((item) => (
            <ItemCard key={item.id} {...item} />
          ))}
        </div>
      </div>
    </div>
  );
};

/**
 * ItemCard Component
 *
 * A responsive card component for displaying items with hover effects:
 * - Shows secondary image on hover
 * - Displays overlay actions on hover
 * - Includes reliability badge
 */
export const ItemCard: React.FC<ItemCardProps> = ({
  id,
  title,
  category,
  price,
  priceUnit,
  imageUrl,
  secondaryImageUrl,
  reliability,
  owner,
}) => {
  return (
    <div className="item-card">
      <div className="item-card-media">
        <img src={imageUrl} alt={title} className="primary-image" />
        {secondaryImageUrl && (
          <img
            src={secondaryImageUrl}
            alt={`${title} - alternate view`}
            className="secondary-image"
            style={{ position: 'absolute', top: 0, left: 0, opacity: 0, transition: 'opacity 0.3s ease' }}
            onMouseOver={(e) => { e.currentTarget.style.opacity = '1'; }}
            onMouseOut={(e) => { e.currentTarget.style.opacity = '0'; }}
          />
        )}
        <div className={`item-card-reliability item-card-reliability-${reliability.level}`}>
          <span>{reliability.score}</span>
        </div>
        <div className="item-card-actions">
          <button className="item-card-action-button" aria-label="Quick view">
            <FaEye />
          </button>
          <button className="item-card-action-button" aria-label="Add to wishlist">
            <FaHeart />
          </button>
          <button className="item-card-action-button" aria-label="Rent now">
            <FaShoppingCart />
          </button>
        </div>
      </div>
      <div className="item-card-content">
        <h3 className="item-card-title">{title}</h3>
        <div className="item-card-category">{category}</div>
        <div className="item-card-price">
          <span>${price}</span>
          <span className="item-card-price-period">per {priceUnit}</span>
        </div>
        <div className="item-card-owner">
          <div className="item-card-owner-avatar">
            <img src={owner.avatarUrl} alt={owner.name} />
          </div>
          <div className="item-card-owner-info">
            <div className="item-card-owner-name">{owner.name}</div>
            <div className="item-card-owner-rating">
              <FaStar className="text-warning-500" />
              <span>{owner.rating.toFixed(1)}</span>
              <span>({owner.reviewCount})</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * ResponsiveAICurationInterface Component
 *
 * A responsive interface for AI curation features:
 * - Mobile: Simplified controls with dropdown menus
 * - Tablet/Desktop: Full controls with appropriate spacing
 */
export const ResponsiveAICurationInterface: React.FC<ResponsiveAICurationInterfaceProps> = ({
  title,
  description,
}) => {
  const [expanded, setExpanded] = useState(false);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 100]);

  const categories = [
    'Electronics', 'Home & Garden', 'Sports', 'Tools', 'Outdoor',
    'Photography', 'Music', 'Party', 'Vehicles', 'Clothing'
  ];

  return (
    <div className="collapsible-section">
      <button
        className="collapsible-section-header"
        onClick={() => setExpanded(!expanded)}
        aria-expanded={expanded}
      >
        <div>
          <h2 className="text-xl font-semibold">{title}</h2>
          {description && <p className="text-sm text-gray-600 mt-1 hide-xs">{description}</p>}
        </div>
        <span className="collapsible-section-toggle">
          {expanded ? <FaChevronUp /> : <FaChevronDown />}
        </span>
      </button>
      <div className={`collapsible-section-content ${expanded ? 'expanded' : ''}`}>
        {/* Mobile controls */}
        <div className="block md:hidden">
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">Categories</label>
            <select
              className="touch-friendly-input w-full"
              multiple={false}
              onChange={(e) => {
                const value = e.target.value;
                if (value && !selectedCategories.includes(value)) {
                  setSelectedCategories([...selectedCategories, value]);
                }
              }}
            >
              <option value="">Select a category</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
            <div className="flex flex-wrap gap-2 mt-2">
              {selectedCategories.map(category => (
                <div key={category} className="bg-primary-100 text-primary-700 px-2 py-1 rounded-full text-sm flex items-center">
                  {category}
                  <button
                    className="ml-1"
                    onClick={() => setSelectedCategories(selectedCategories.filter(c => c !== category))}
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          </div>
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Price Range: ${priceRange[0]} - ${priceRange[1]}
            </label>
            <input
              type="range"
              min="0"
              max="100"
              value={priceRange[1]}
              onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}
              className="w-full"
            />
          </div>
        </div>

        {/* Tablet/Desktop controls */}
        <div className="hidden md:block">
          <div className="grid grid-cols-2 gap-6 lg:grid-cols-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Categories</label>
              <div className="grid grid-cols-2 gap-2">
                {categories.map(category => (
                  <div key={category} className="flex items-center">
                    <input
                      type="checkbox"
                      id={`category-${category}`}
                      checked={selectedCategories.includes(category)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedCategories([...selectedCategories, category]);
                        } else {
                          setSelectedCategories(selectedCategories.filter(c => c !== category));
                        }
                      }}
                      className="mr-2"
                    />
                    <label htmlFor={`category-${category}`} className="text-sm">{category}</label>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Price Range: ${priceRange[0]} - ${priceRange[1]}
              </label>
              <div className="flex items-center gap-4">
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={priceRange[0]}
                  onChange={(e) => setPriceRange([parseInt(e.target.value), priceRange[1]])}
                  className="flex-1"
                />
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={priceRange[1]}
                  onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}
                  className="flex-1"
                />
              </div>
            </div>
          </div>
        </div>

        <div className="mt-4 flex justify-end">
          <button className="touch-friendly bg-primary-600 text-white rounded-md">
            Apply AI Curation
          </button>
        </div>
      </div>
    </div>
  );
};

/**
 * ResponsiveAnalyticsDashboard Component
 *
 * A responsive dashboard for AI analytics:
 * - Mobile: Accordion pattern with expandable sections
 * - Tablet: Tabs for section navigation
 * - Desktop: All sections visible in multi-column layout
 */
export const ResponsiveAnalyticsDashboard: React.FC<ResponsiveAnalyticsDashboardProps> = ({
  title,
  period,
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [expandedSections, setExpandedSections] = useState<string[]>(['overview']);

  const toggleSection = (section: string) => {
    if (expandedSections.includes(section)) {
      setExpandedSections(expandedSections.filter(s => s !== section));
    } else {
      setExpandedSections([...expandedSections, section]);
    }
  };

  const sections = [
    { id: 'overview', title: 'Overview', icon: '📊' },
    { id: 'recommendations', title: 'Recommendations', icon: '🎯' },
    { id: 'trends', title: 'Trends', icon: '📈' },
    { id: 'predictions', title: 'Predictions', icon: '🔮' },
  ];

  return (
    <div className="responsive-analytics-dashboard">
      <h2 className="text-2xl font-semibold mb-4">{title}</h2>

      {/* Period selector */}
      <div className="flex gap-2 mb-6 overflow-x-auto pb-2">
        {['day', 'week', 'month', 'year'].map((p) => (
          <button
            key={p}
            className={`px-4 py-2 rounded-full text-sm font-medium ${
              period === p
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {p.charAt(0).toUpperCase() + p.slice(1)}
          </button>
        ))}
      </div>

      {/* Mobile: Accordion */}
      <div className="block md:hidden">
        {sections.map(section => (
          <div key={section.id} className="mb-4 border rounded-lg overflow-hidden">
            <button
              className="w-full p-4 flex justify-between items-center bg-gray-50"
              onClick={() => toggleSection(section.id)}
            >
              <div className="flex items-center">
                <span className="mr-2">{section.icon}</span>
                <span className="font-medium">{section.title}</span>
              </div>
              <span>
                {expandedSections.includes(section.id) ? <FaChevronUp /> : <FaChevronDown />}
              </span>
            </button>
            {expandedSections.includes(section.id) && (
              <div className="p-4">
                <div className="aspect-ratio-container aspect-ratio-16-9 mb-4">
                  <div className="aspect-ratio-content bg-gray-200 flex items-center justify-center">
                    {section.icon} {section.title} Chart
                  </div>
                </div>
                <p className="text-sm text-gray-600">
                  This is the {section.title.toLowerCase()} section of the analytics dashboard.
                  In a real implementation, this would contain actual data and visualizations.
                </p>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Tablet: Tabs */}
      <div className="hidden md:block lg:hidden">
        <div className="tabs-responsive mb-4">
          {sections.map(section => (
            <button
              key={section.id}
              className={`tabs-responsive-tab ${activeTab === section.id ? 'active' : ''}`}
              onClick={() => setActiveTab(section.id)}
            >
              <span className="mr-2">{section.icon}</span>
              {section.title}
            </button>
          ))}
        </div>

        {sections.map(section => (
          <div
            key={section.id}
            className={`tabs-responsive-content ${activeTab === section.id ? 'active' : ''}`}
          >
            <div className="aspect-ratio-container aspect-ratio-16-9 mb-4">
              <div className="aspect-ratio-content bg-gray-200 flex items-center justify-center">
                {section.icon} {section.title} Chart
              </div>
            </div>
            <p className="text-gray-600">
              This is the {section.title.toLowerCase()} section of the analytics dashboard.
              In a real implementation, this would contain actual data and visualizations.
            </p>
          </div>
        ))}
      </div>

      {/* Desktop: Multi-column */}
      <div className="hidden lg:grid lg:grid-cols-2 gap-6">
        {sections.map(section => (
          <div key={section.id} className="border rounded-lg p-4">
            <h3 className="text-lg font-medium mb-3 flex items-center">
              <span className="mr-2">{section.icon}</span>
              {section.title}
            </h3>
            <div className="aspect-ratio-container aspect-ratio-16-9 mb-4">
              <div className="aspect-ratio-content bg-gray-200 flex items-center justify-center">
                {section.icon} {section.title} Chart
              </div>
            </div>
            <p className="text-gray-600">
              This is the {section.title.toLowerCase()} section of the analytics dashboard.
              In a real implementation, this would contain actual data and visualizations.
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};