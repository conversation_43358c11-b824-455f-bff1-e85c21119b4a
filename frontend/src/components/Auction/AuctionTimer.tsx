import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';

interface AuctionTimerProps {
  endTime: string;
  compact?: boolean; // For smaller UI in constrained spaces
  showLabels?: boolean; // Whether to show labels for days, hours, etc.
  onEnd?: () => void; // Callback when timer ends
  className?: string;
}

const AuctionTimer: React.FC<AuctionTimerProps> = ({
  endTime,
  compact = false,
  showLabels = false,
  onEnd,
  className = ''
}) => {
  const [timeLeft, setTimeLeft] = useState<{
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
    totalSeconds: number;
  }>({ days: 0, hours: 0, minutes: 0, seconds: 0, totalSeconds: 0 });

  const [isEnded, setIsEnded] = useState(false);

  // Calculate time left
  const calculateTimeLeft = useCallback(() => {
    const difference = new Date(endTime).getTime() - new Date().getTime();

    if (difference <= 0) {
      if (!isEnded) {
        setIsEnded(true);
        if (onEnd) onEnd();
      }
      return { days: 0, hours: 0, minutes: 0, seconds: 0, totalSeconds: 0 };
    }

    const totalSeconds = Math.floor(difference / 1000);

    return {
      days: Math.floor(difference / (1000 * 60 * 60 * 24)),
      hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
      minutes: Math.floor((difference / 1000 / 60) % 60),
      seconds: Math.floor((difference / 1000) % 60),
      totalSeconds
    };
  }, [endTime, isEnded, onEnd]);

  useEffect(() => {
    // Initial calculation
    setTimeLeft(calculateTimeLeft());

    // Update every second
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    // Clear interval on component unmount
    return () => clearInterval(timer);
  }, [calculateTimeLeft]);

  // Format with leading zeros
  const formatTime = (value: number): string => {
    return value < 10 ? `0${value}` : `${value}`;
  };

  // Determine color based on time left
  const getTimerColor = () => {
    if (timeLeft.days === 0 && timeLeft.hours === 0) {
      if (timeLeft.minutes < 5) {
        return 'text-red-600'; // Less than 5 minutes
      } else if (timeLeft.minutes < 30) {
        return 'text-orange-500'; // Less than 30 minutes
      }
    }
    return 'text-gray-700'; // Default color
  };

  // Calculate progress percentage (inverse - starts at 100% and goes to 0%)
  const calculateProgress = () => {
    // Assume a maximum auction duration of 7 days (604800 seconds)
    const maxDuration = 7 * 24 * 60 * 60; // 7 days in seconds
    const progress = (timeLeft.totalSeconds / maxDuration) * 100;
    return Math.min(progress, 100); // Cap at 100%
  };

  // Compact timer just shows the most significant unit
  const renderCompactTimer = () => {
    if (timeLeft.days > 0) {
      return `${timeLeft.days}d ${formatTime(timeLeft.hours)}h`;
    } else if (timeLeft.hours > 0) {
      return `${formatTime(timeLeft.hours)}h ${formatTime(timeLeft.minutes)}m`;
    } else {
      return `${formatTime(timeLeft.minutes)}:${formatTime(timeLeft.seconds)}`;
    }
  };

  // Full timer shows all units
  const renderFullTimer = () => {
    return (
      <>
        {timeLeft.days > 0 && (
          <span className="timer-unit">
            <span className="timer-value">{timeLeft.days}</span>
            {showLabels && <span className="timer-label">d</span>}
          </span>
        )}
        <span className="timer-unit">
          <span className="timer-value">{formatTime(timeLeft.hours)}</span>
          {showLabels && <span className="timer-label">h</span>}
        </span>
        <span className="timer-separator">:</span>
        <span className="timer-unit">
          <span className="timer-value">{formatTime(timeLeft.minutes)}</span>
          {showLabels && <span className="timer-label">m</span>}
        </span>
        <span className="timer-separator">:</span>
        <span className="timer-unit">
          <span className="timer-value">{formatTime(timeLeft.seconds)}</span>
          {showLabels && <span className="timer-label">s</span>}
        </span>
      </>
    );
  };

  return (
    <div className={`text-center ${className}`}>
      {isEnded ? (
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className={`${compact ? 'text-xs' : 'text-sm'} text-red-600 font-semibold`}
        >
          Auction Ended
        </motion.p>
      ) : (
        <>
          <p className={`${compact ? 'text-xs' : 'text-xs'} text-gray-500 mb-1`}>
            Time Remaining
          </p>

          {/* Progress bar */}
          <div className="w-full bg-gray-200 rounded-full h-1 mb-1">
            <motion.div
              className={`h-1 rounded-full ${
                timeLeft.days === 0 && timeLeft.hours === 0 && timeLeft.minutes < 5
                  ? 'bg-red-600'
                  : timeLeft.days === 0 && timeLeft.hours === 0 && timeLeft.minutes < 30
                  ? 'bg-orange-500'
                  : 'bg-primary'
              }`}
              initial={{ width: `${calculateProgress()}%` }}
              animate={{ width: `${calculateProgress()}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>

          <div className={`font-mono font-semibold ${getTimerColor()} ${compact ? 'text-xs' : 'text-sm'}`}>
            {compact ? renderCompactTimer() : renderFullTimer()}
          </div>
        </>
      )}
    </div>
  );
};

export default AuctionTimer;