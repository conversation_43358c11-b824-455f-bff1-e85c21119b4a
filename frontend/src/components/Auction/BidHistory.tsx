import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface Bid {
  id: string;
  bidder: {
    id: string;
    name: string;
    avatar?: string;
  };
  amount: number;
  timestamp: string;
  status: 'active' | 'outbid' | 'won' | 'invalid';
}

interface BidHistoryProps {
  bids: Bid[];
  currentUserId?: string;
  className?: string;
  compact?: boolean; // For smaller UI in constrained spaces
  maxHeight?: string; // Custom max height for the bid list
  showHeader?: boolean; // Whether to show the header
  limit?: number; // Limit the number of bids shown
}

const BidHistory: React.FC<BidHistoryProps> = ({
  bids,
  currentUserId,
  className = '',
  compact = false,
  maxHeight = '96',
  showHeader = true,
  limit
}) => {
  const [expanded, setExpanded] = useState(false);

  // Sort bids by timestamp (newest first)
  const sortedBids = [...bids].sort((a, b) =>
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );

  // Apply limit if specified and not expanded
  const displayedBids = limit && !expanded
    ? sortedBids.slice(0, limit)
    : sortedBids;

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    // Show relative time for recent bids
    if (diffMins < 1) {
      return 'Just now';
    } else if (diffMins < 60) {
      return `${diffMins}m ago`;
    } else if (diffHours < 24) {
      return `${diffHours}h ago`;
    } else if (diffDays < 7) {
      return `${diffDays}d ago`;
    } else {
      // For older bids, show the date
      return date.toLocaleDateString();
    }
  };

  // Format for screen readers
  const formatTimestampFull = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  return (
    <div className={`bg-white rounded-lg shadow ${compact ? 'p-3' : 'p-4'} ${className}`}>
      {showHeader && (
        <div className="flex justify-between items-center mb-3">
          <h3 className={`${compact ? 'text-base' : 'text-lg'} font-semibold`}>Bid History</h3>
          <span className="text-xs text-gray-500">{bids.length} {bids.length === 1 ? 'bid' : 'bids'}</span>
        </div>
      )}

      {sortedBids.length === 0 ? (
        <div className="text-gray-500 text-center py-4 bg-gray-50 rounded-md">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mx-auto text-gray-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p className={`${compact ? 'text-sm' : 'text-base'}`}>No bids yet. Be the first to bid!</p>
        </div>
      ) : (
        <>
          <div className={`space-y-2 overflow-y-auto pr-1 ${compact ? 'max-h-64' : `max-h-${maxHeight}`}`}>
            <AnimatePresence initial={false}>
              {displayedBids.map((bid, index) => (
                <motion.div
                  key={bid.id}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.2, delay: index * 0.05 }}
                  className={`
                    flex items-center justify-between ${compact ? 'p-2' : 'p-3'} rounded-md
                    ${bid.status === 'won' ? 'bg-green-50 border border-green-200' : ''}
                    ${bid.status === 'outbid' ? 'bg-gray-50' : ''}
                    ${bid.bidder.id === currentUserId ? 'bg-blue-50' : ''}
                  `}
                >
                  <div className="flex items-center">
                    <div className={`${compact ? 'w-6 h-6' : 'w-8 h-8'} rounded-full bg-gray-200 flex items-center justify-center overflow-hidden mr-2`}>
                      {bid.bidder.avatar ? (
                        <img
                          src={bid.bidder.avatar}
                          alt={bid.bidder.name}
                          className="w-full h-full object-cover"
                          loading="lazy"
                        />
                      ) : (
                        <span className={`${compact ? 'text-xs' : 'text-sm'} font-semibold`}>
                          {bid.bidder.name.charAt(0).toUpperCase()}
                        </span>
                      )}
                    </div>
                    <div>
                      <p className={`${compact ? 'text-sm' : 'text-base'} font-medium`}>
                        {bid.bidder.id === currentUserId ? 'You' : bid.bidder.name}
                        {bid.status === 'won' && (
                          <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
                            Winner
                          </span>
                        )}
                      </p>
                      <p className="text-xs text-gray-500" title={formatTimestampFull(bid.timestamp)}>
                        {formatTimestamp(bid.timestamp)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`${compact ? 'text-sm' : 'text-base'} font-bold text-primary`}>
                      ${bid.amount.toFixed(2)}
                    </p>
                    {bid.status === 'outbid' && (
                      <p className="text-xs text-gray-500">Outbid</p>
                    )}
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>

          {/* Show more/less button if limit is specified and there are more bids */}
          {limit && sortedBids.length > limit && (
            <button
              onClick={() => setExpanded(!expanded)}
              className="w-full mt-2 text-sm text-primary hover:text-primary-dark py-1 rounded-md transition-colors"
            >
              {expanded ? 'Show Less' : `Show All (${sortedBids.length})`}
            </button>
          )}
        </>
      )}
    </div>
  );
};

export default BidHistory;