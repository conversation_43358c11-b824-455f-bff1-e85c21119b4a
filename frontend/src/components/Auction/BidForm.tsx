import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { InteractiveButton } from '../../shared/ui';

interface BidFormProps {
  auctionId: string;
  currentBid: number;
  minIncrement: number;
  onBidSubmit: (amount: number) => Promise<void>;
  isLoading?: boolean;
  className?: string;
  compact?: boolean; // For smaller UI in constrained spaces
  showQuickBids?: boolean; // Whether to show quick bid options
  onCancel?: () => void; // Optional cancel handler
}

const BidForm: React.FC<BidFormProps> = ({
  auctionId,
  currentBid,
  minIncrement,
  onBidSubmit,
  isLoading = false,
  className = '',
  compact = false,
  showQuickBids = true,
  onCancel
}) => {
  const suggestedBid = currentBid + minIncrement;
  const [bidAmount, setBidAmount] = useState<number>(suggestedBid);
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<boolean>(false);
  const [touched, setTouched] = useState<boolean>(false);

  // Reset form when current bid changes
  useEffect(() => {
    const newSuggestedBid = currentBid + minIncrement;
    setBidAmount(newSuggestedBid);
    setError('');
    setSuccess(false);
    setTouched(false);
  }, [currentBid, minIncrement]);

  const validateBid = (value: number): string => {
    if (isNaN(value) || value <= 0) {
      return 'Please enter a valid bid amount';
    } else if (value <= currentBid) {
      return `Bid must be higher than current bid ($${currentBid.toFixed(2)})`;
    } else if (value < currentBid + minIncrement) {
      return `Minimum increment is $${minIncrement.toFixed(2)}`;
    }
    return '';
  };

  const handleBidChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    setBidAmount(value);
    setTouched(true);
    setError(validateBid(value));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setTouched(true);

    // Validate before submission
    const validationError = validateBid(bidAmount);
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      await onBidSubmit(bidAmount);
      // Show success message
      setSuccess(true);
      // Reset error
      setError('');
      // Reset form after successful submission
      setTimeout(() => {
        setBidAmount(bidAmount + minIncrement);
        setSuccess(false);
      }, 2000);
    } catch (err) {
      // Error handling is done in the parent component
      setSuccess(false);
    }
  };

  // Generate quick bid options
  const quickBidOptions = [
    suggestedBid,
    suggestedBid + minIncrement,
    suggestedBid + (minIncrement * 2),
    suggestedBid + (minIncrement * 5)
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-white rounded-lg shadow ${compact ? 'p-3' : 'p-4'} ${className}`}
    >
      <h3 className={`${compact ? 'text-base' : 'text-lg'} font-semibold mb-3`}>
        Place Your Bid
      </h3>

      <div className="mb-3">
        <div className="flex justify-between text-sm text-gray-600 mb-1">
          <span>Current Bid:</span>
          <span className="font-semibold">${currentBid.toFixed(2)}</span>
        </div>
        <div className="flex justify-between text-sm text-gray-600">
          <span>Minimum Bid:</span>
          <span className="font-semibold">${suggestedBid.toFixed(2)}</span>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="mb-3">
          <label
            htmlFor={`bidAmount-${auctionId}`}
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Your Bid Amount ($)
          </label>
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
            <input
              type="number"
              id={`bidAmount-${auctionId}`}
              value={bidAmount}
              onChange={handleBidChange}
              step="0.01"
              min={currentBid + minIncrement}
              className={`w-full p-2 pl-6 border rounded-md focus:ring-primary focus:border-primary touch-friendly
                ${error && touched ? 'border-red-500' : 'border-gray-300'}
              `}
              required
              aria-invalid={!!error}
              aria-describedby={error ? `bidError-${auctionId}` : undefined}
            />

            {/* Success indicator */}
            <AnimatePresence>
              {success && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.5 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.5 }}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          <AnimatePresence>
            {error && touched && (
              <motion.p
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-1 text-sm text-red-600"
                id={`bidError-${auctionId}`}
              >
                {error}
              </motion.p>
            )}
          </AnimatePresence>
        </div>

        {showQuickBids && (
          <div className="mb-3">
            <p className={`${compact ? 'text-xs' : 'text-sm'} text-gray-600 mb-1`}>
              Quick Bid Options:
            </p>
            <div className={`grid ${compact ? 'grid-cols-2 gap-1' : 'grid-cols-2 sm:grid-cols-4 gap-2'}`}>
              {quickBidOptions.map((option, index) => (
                <motion.button
                  key={index}
                  type="button"
                  whileTap={{ scale: 0.95 }}
                  onClick={() => {
                    setBidAmount(option);
                    setError('');
                    setTouched(true);
                  }}
                  className={`
                    py-1 px-2 bg-gray-100 hover:bg-gray-200 text-gray-800
                    rounded-md text-sm transition-colors touch-friendly
                    ${bidAmount === option ? 'bg-primary-100 border border-primary text-primary-800' : ''}
                  `}
                  aria-label={`Quick bid $${option.toFixed(2)}`}
                >
                  ${option.toFixed(2)}
                </motion.button>
              ))}
            </div>
          </div>
        )}

        <div className={`${onCancel ? 'flex gap-2' : ''}`}>
          <InteractiveButton
            type="submit"
            variant="primary"
            fullWidth={!onCancel}
            disabled={isLoading || (!!error && touched) || bidAmount <= currentBid}
            isLoading={isLoading}
            className={`
              ${isLoading || (!!error && touched) || bidAmount <= currentBid ? 'bg-gray-400' : ''}
              ${compact ? 'py-1.5 text-sm' : 'py-2'}
              touch-friendly
            `}
            title="Submit your bid"
          >
            {isLoading ? 'Processing...' : success ? 'Bid Placed!' : 'Place Bid'}
          </InteractiveButton>

          {onCancel && (
            <InteractiveButton
              type="button"
              variant="secondary"
              onClick={onCancel}
              className={`
                ${compact ? 'py-1.5 text-sm' : 'py-2'}
                touch-friendly
              `}
              title="Cancel bidding"
            >
              Cancel
            </InteractiveButton>
          )}
        </div>
      </form>
    </motion.div>
  );
};

export default BidForm;