import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import AuctionTimer from './AuctionTimer';
import { Auction } from '../../services/auctionService';

interface AuctionCardProps {
  auction: Auction;
  className?: string;
  compact?: boolean; // For smaller UI in constrained spaces
  showLocation?: boolean;
  showSeller?: boolean;
}

const AuctionCard: React.FC<AuctionCardProps> = ({
  auction,
  className = '',
  compact = false,
  showLocation = false,
  showSeller = false
}) => {
  // Map status values between our interfaces
  const statusMap = {
    'active': 'active',
    'ended': 'ended',
    'canceled': 'canceled',
    'scheduled': 'scheduled'
  };

  // Get normalized status
  const normalizedStatus = statusMap[auction.status as keyof typeof statusMap] || auction.status;

  const isActive = normalizedStatus === 'active';
  const isEnded = normalizedStatus === 'ended';
  const isScheduled = normalizedStatus === 'scheduled';

  // Status badge colors
  const statusColors = {
    active: 'bg-green-200 text-green-900 border border-green-300',
    ended: 'bg-gray-200 text-gray-900 border border-gray-300',
    canceled: 'bg-red-200 text-red-900 border border-red-300',
    scheduled: 'bg-blue-200 text-blue-900 border border-blue-300'
  };

  return (
    <motion.div
      whileHover={{ y: -5 }}
      transition={{ duration: 0.2 }}
      className={`bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-shadow ${className}`}
    >
      <div className="relative">
        <img
          src={auction.imageUrl || auction.images?.[0] || '/placeholder-item.jpg'}
          alt={auction.title}
          className={`w-full object-cover ${compact ? 'h-36' : 'h-48 sm:h-56'}`}
          loading="lazy"
        />
        <div className="absolute top-2 right-2">
          <span className={`
            px-2 py-1 text-xs font-semibold rounded-full
            ${statusColors[normalizedStatus as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'}
          `}>
            {normalizedStatus.charAt(0).toUpperCase() + normalizedStatus.slice(1)}
          </span>
        </div>

        {/* Reserve price badge if current bid is below reserve */}
        {auction.current_highest_bid < (auction.reserve_price || 0) && isActive && (
          <div className="absolute bottom-2 left-2">
            <span className="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
              Reserve not met
            </span>
          </div>
        )}

        {/* Auction type badge */}
        {auction.auctionType && (
          <div className="absolute bottom-2 right-2">
            <span className="px-2 py-1 text-xs font-semibold rounded-full bg-primary bg-opacity-90 text-white">
              {auction.auctionType.charAt(0).toUpperCase() + auction.auctionType.slice(1)}
            </span>
          </div>
        )}
      </div>

      <div className={`p-4 ${compact ? 'p-3' : 'p-5'}`}>
        <h3 className={`${compact ? 'text-base' : 'text-lg'} font-semibold mb-2 truncate text-gray-900`}>
          {auction.title}
        </h3>

        {showLocation && auction.location && (
          <div className="flex items-center text-xs text-gray-700 mb-2 font-medium">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            {auction.location}
          </div>
        )}

        {!compact && (
          <p className="text-gray-700 text-sm mb-3 line-clamp-2">{auction.description}</p>
        )}

        <div className="flex justify-between items-center mb-4 bg-gray-100 p-2 rounded-md border border-gray-200">
          <div>
            <p className={`${compact ? 'text-xs' : 'text-sm'} text-gray-700 font-medium`}>Current Bid</p>
            <p className={`${compact ? 'text-base' : 'text-lg'} font-bold text-primary-700`}>
              ${auction.current_highest_bid.toFixed(2)}
            </p>
          </div>
          <div className="text-right">
            <p className={`${compact ? 'text-xs' : 'text-sm'} text-gray-700 font-medium`}>Bids</p>
            <p className={`${compact ? 'text-sm' : 'text-md'} font-semibold text-gray-900`}>
              {auction.bidCount || auction.bids?.length || 0}
            </p>
          </div>
        </div>

        {isActive && (
          <div className="mb-3">
            <AuctionTimer endTime={auction.end_time} compact={compact} />
          </div>
        )}

        {isScheduled && (
          <div className="mb-3 text-sm text-gray-600">
            <p>Starting soon</p>
          </div>
        )}

        {showSeller && (auction.owner || auction.sellerName) && (
          <div className="flex items-center mb-3 bg-gray-50 p-2 rounded-md border border-gray-200">
            <div className="w-7 h-7 rounded-full bg-primary-100 border border-primary-300 flex items-center justify-center text-xs font-bold text-primary-700 mr-2">
              {(auction.sellerName || (auction.owner && auction.owner.name) || 'S').charAt(0).toUpperCase()}
            </div>
            <div>
              <p className="text-xs font-medium text-gray-800">{auction.sellerName || (auction.owner && auction.owner.name) || 'Seller'}</p>
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => {
                  const rating = auction.sellerRating || (auction.owner && auction.owner.rating) || 0;
                  return (
                    <svg
                      key={i}
                      xmlns="http://www.w3.org/2000/svg"
                      className={`h-3 w-3 ${i < Math.round(rating) ? 'text-yellow-500' : 'text-gray-300'}`}
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  );
                })}
              </div>
            </div>
          </div>
        )}

        <div className="mt-4">
          <Link
            to={`/auctions/${auction.id}`}
            className={`
              block w-full text-center bg-primary text-white rounded-md hover:bg-primary-dark transition-colors
              ${compact ? 'py-1.5 text-sm' : 'py-2.5 text-base font-medium'}
              touch-friendly shadow-md
            `}
            aria-label={isActive ? `Place bid on ${auction.title}` : `View details for ${auction.title}`}
          >
            {isActive ? 'Place Bid' : 'View Details'}
          </Link>
        </div>

        {/* Category tag if available */}
        {auction.category && !compact && (
          <div className="mt-3 flex flex-wrap gap-1.5">
            <span className="inline-block bg-gray-200 text-gray-800 text-xs px-2.5 py-1 rounded-md border border-gray-300 font-medium">
              {auction.category}
            </span>
            {auction.subcategory && (
              <span className="inline-block bg-gray-200 text-gray-800 text-xs px-2.5 py-1 rounded-md border border-gray-300 font-medium">
                {auction.subcategory}
              </span>
            )}
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default AuctionCard;