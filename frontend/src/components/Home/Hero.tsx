import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { InteractiveLink } from '../../shared/ui';
import SearchBar from '../SearchBar';

interface HeroProps {
  // This component doesn't require props but we define the interface for consistency
  className?: string;
}

const Hero: React.FC<HeroProps> = () => {
  const navigate = useNavigate();
  const [currentSlide, setCurrentSlide] = useState(0);

  // Slides data
  const slides = [
    {
      title: "New Bluetooth Calling Smart Watch, 550",
      description: "Activity Tracker, Calorie Tracker",
      badge: "CLAIM THIS OFFER NOW",
      image: "https://images.unsplash.com/photo-1546868871-7041f2a55e12?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=764&q=80",
      category: "smart-watches",
      bgColor: "bg-primary-light" // Light blue for better button visibility
    },
    {
      title: "Premium Drone With 4K Camera",
      description: "30 Minutes Flight Time, GPS Return",
      badge: "NEW ARRIVAL",
      image: "https://images.unsplash.com/photo-1473968512647-3e447244af8f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
      category: "drones",
      bgColor: "bg-secondary-light" // Light teal for better button visibility
    },
    {
      title: "Professional DSLR Camera Kit",
      description: "24MP, 4K Video, 3 Lenses Included",
      badge: "WEEKEND SPECIAL",
      image: "https://images.unsplash.com/photo-1516035069371-29a1b244cc32?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=764&q=80",
      category: "cameras",
      bgColor: "bg-primary-dark" // Dark blue for better button visibility
    }
  ];

  // Auto-rotate slides
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [slides.length]);

  return (
    <div className="bg-white py-8 overflow-hidden">
      <div className="container mx-auto px-4">
        {/* Hero Search Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-4">
            The Community Marketplace for Endless Shared Possibilities
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Rent anything you need, share what you own. Join thousands of people saving money and reducing waste in your community.
          </p>
          <div className="max-w-2xl mx-auto">
            <SearchBar
              placeholder="What would you like to rent today?"
              className="w-full"
            />
          </div>
        </div>

        {/* Featured Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          {/* Main Featured Product - Slideshow */}
          <div className="md:col-span-2 rounded-lg overflow-hidden shadow-md relative h-[352px]">
            {/* Slide */}
            <div className={`${slides[currentSlide].bgColor} w-full h-full rounded-lg overflow-hidden`}>
              <div className="flex flex-col md:flex-row h-full">
                <div className="md:w-1/2 p-6 space-y-4 z-10 text-white flex flex-col justify-between">
                  <div>
                    <div className="text-sm font-semibold uppercase tracking-wider bg-accent px-3 py-1.5 rounded-md inline-block shadow-sm border border-accent-dark">
                      {slides[currentSlide].badge}
                    </div>
                    <h2 className="text-3xl md:text-4xl font-bold mt-4">{slides[currentSlide].title}</h2>
                    <p className="text-white/90 mt-2">{slides[currentSlide].description}</p>
                  </div>
                  <div className="flex flex-col space-y-4">
                    <InteractiveLink
                      to={`/search?category=${slides[currentSlide].category}`}
                      asButton
                      buttonVariant="accent"
                      className="uppercase font-semibold px-6 py-2.5 inline-block text-white rounded-md transition-colors w-32 text-center shadow-md border border-accent-dark"
                      title={`Rent ${slides[currentSlide].category.replace('-', ' ')}`}
                    >
                      RENT NOW
                    </InteractiveLink>
                    <div className="flex space-x-2">
                      {slides.map((_, index) => (
                        <button
                          key={index}
                          onClick={() => setCurrentSlide(index)}
                          className={`w-4 h-4 rounded-full ${index === currentSlide ? 'bg-accent border border-white' : 'bg-white/70 border border-white/80'}`}
                          aria-label={`Go to slide ${index + 1}`}
                        />
                      ))}
                    </div>
                  </div>
                </div>
                <div className="md:w-1/2 flex justify-center items-center p-6">
                  <div className="w-full h-[280px] flex items-center justify-center overflow-hidden">
                    <div className="w-[280px] h-[280px] flex items-center justify-center">
                      <img
                        src={slides[currentSlide].image}
                        alt={slides[currentSlide].title}
                        className="w-auto h-auto max-h-[250px] max-w-[250px] object-contain transform hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side Products - Stacked */}
          <div className="md:col-span-1 space-y-4 flex flex-col">
            {/* iPhone Product */}
            <div className="bg-warning/20 rounded-lg overflow-hidden shadow-md p-4 flex-1 flex flex-col relative h-[172px] border border-warning/30">
              <div>
                <div className="text-sm font-semibold mb-2 bg-accent text-white px-3 py-1.5 rounded-md inline-block shadow-sm border border-accent-dark">15% CASH BACK</div>
                <h3 className="text-xl font-bold mb-1 text-near-black">Apple iPhone 14 Pro Max</h3>
                <p className="text-sm mb-4 text-gray-700 font-medium">Retina XDR Display</p>
              </div>
              <div className="flex items-end justify-between">
                <InteractiveLink
                  to="/search?category=smartphones"
                  asButton
                  buttonVariant="primary"
                  className="uppercase text-sm font-semibold px-6 py-2.5 rounded-md w-32 text-center inline-block shadow-md border border-primary-dark"
                  title="Rent smartphones"
                >
                  RENT NOW
                </InteractiveLink>
                <div className="h-24 w-24 flex items-end justify-center overflow-hidden pb-2.5">
                  <img
                    src="https://images.unsplash.com/photo-1678911820864-e2c567c655d7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80"
                    alt="iPhone 14 Pro Max"
                    className="w-auto h-auto max-h-full max-w-full object-contain"
                  />
                </div>
              </div>
            </div>

            {/* VR Headset Product */}
            <div className="bg-secondary/20 rounded-lg overflow-hidden shadow-md p-4 flex-1 flex flex-col relative h-[172px] border border-secondary/30">
              <div>
                <div className="text-sm font-semibold mb-2 bg-primary text-white px-3 py-1.5 rounded-md inline-block shadow-sm border border-primary-dark">BEST DISCOUNTS</div>
                <h3 className="text-xl font-bold mb-1 text-near-black">Meta Oculus Quest 128GB</h3>
                <p className="text-sm mb-4 text-gray-700 font-medium">High Resolution</p>
              </div>
              <div className="flex items-end justify-between">
                <InteractiveLink
                  to="/search?category=vr-headsets"
                  asButton
                  buttonVariant="accent"
                  className="uppercase text-sm font-semibold px-6 py-2.5 rounded-md w-32 text-center inline-block shadow-md border border-accent-dark"
                  title="Rent VR headsets"
                >
                  RENT NOW
                </InteractiveLink>
                <div className="h-24 w-24 flex items-end justify-center overflow-hidden pb-2.5">
                  <img
                    src="https://images.unsplash.com/photo-1622979135225-d2ba269cf1ac?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=870&q=80"
                    alt="Meta Oculus Quest"
                    className="w-auto h-auto max-h-full max-w-full object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Category Icons */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-4 py-8 border-t border-gray-100">
          {/* Android TV */}
          <div onClick={() => navigate('/search?category=android-tv')} className="flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-all p-3 rounded-lg hover:bg-white group border border-gray-200 hover:border-primary-300">
            <div className="w-16 h-16 flex items-center justify-center text-primary mb-2 group-hover:text-accent transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <rect x="2" y="3" width="20" height="14" rx="2" strokeWidth="2" />
                <line x1="8" y1="21" x2="16" y2="21" strokeWidth="2" />
                <line x1="12" y1="17" x2="12" y2="21" strokeWidth="2" />
              </svg>
            </div>
            <p className="text-sm font-medium text-gray-700 group-hover:text-gray-900 transition-colors">Android TV</p>
          </div>

          {/* Speakers */}
          <div onClick={() => navigate('/search?category=speakers')} className="flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-all p-3 rounded-lg hover:bg-white group border border-gray-200 hover:border-primary-300">
            <div className="w-16 h-16 flex items-center justify-center text-primary mb-2 group-hover:text-accent transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <rect x="4" y="2" width="16" height="20" rx="2" strokeWidth="2" />
                <circle cx="12" cy="14" r="4" strokeWidth="2" />
                <circle cx="12" cy="7" r="1" strokeWidth="2" />
              </svg>
            </div>
            <p className="text-sm font-medium text-gray-700 group-hover:text-gray-900 transition-colors">Speakers</p>
          </div>

          {/* Cameras */}
          <div onClick={() => navigate('/search?category=cameras')} className="flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-all p-3 rounded-lg hover:bg-white group border border-gray-200 hover:border-primary-300">
            <div className="w-16 h-16 flex items-center justify-center text-primary mb-2 group-hover:text-accent transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <p className="text-sm font-medium text-gray-700 group-hover:text-gray-900 transition-colors">Cameras</p>
          </div>

          {/* Mobile */}
          <div onClick={() => navigate('/search?category=mobile')} className="flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-all p-3 rounded-lg hover:bg-white group border border-gray-200 hover:border-primary-300">
            <div className="w-16 h-16 flex items-center justify-center text-primary mb-2 group-hover:text-accent transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <rect x="5" y="2" width="14" height="20" rx="2" strokeWidth="2" />
                <line x1="12" y1="18" x2="12" y2="18.01" strokeWidth="3" />
              </svg>
            </div>
            <p className="text-sm font-medium text-gray-700 group-hover:text-gray-900 transition-colors">Mobile</p>
          </div>

          {/* New Laptop */}
          <div onClick={() => navigate('/search?category=laptops')} className="flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-all p-3 rounded-lg hover:bg-white group border border-gray-200 hover:border-primary-300">
            <div className="w-16 h-16 flex items-center justify-center text-primary mb-2 group-hover:text-accent transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            </div>
            <p className="text-sm font-medium text-gray-700 group-hover:text-gray-900 transition-colors">New Laptop</p>
          </div>

          {/* Smart Watches */}
          <div onClick={() => navigate('/search?category=smart-watches')} className="flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-all p-3 rounded-lg hover:bg-white group border border-gray-200 hover:border-primary-300">
            <div className="w-16 h-16 flex items-center justify-center text-primary mb-2 group-hover:text-accent transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <rect x="6" y="6" width="12" height="12" rx="3" strokeWidth="2" />
                <path d="M9 18v3h6v-3" strokeWidth="2" />
                <path d="M9 6V3h6v3" strokeWidth="2" />
              </svg>
            </div>
            <p className="text-sm font-medium text-gray-700 group-hover:text-gray-900 transition-colors">Smart Watches</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;
