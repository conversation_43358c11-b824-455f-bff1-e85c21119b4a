import React from 'react';

interface RatingDisplayProps {
  rating: number;
  reviewCount: number;
}

const RatingDisplay: React.FC<RatingDisplayProps> = ({ rating, reviewCount }) => {
  return (
    <div className="flex items-center">
      <div className="text-yellow-500 text-xl mr-1">
        {/* You can use star icons here */}
        ★
      </div>
      <div className="text-gray-700 font-semibold">{rating}</div>
      <div className="text-gray-500 ml-1">({reviewCount} reviews)</div>
    </div>
  );
};

export default RatingDisplay;
