import React, { ReactNode } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth, UserRole } from '../../contexts/AuthContext';
import ProtectedRoute from './ProtectedRoute';

interface RoleBasedRouteProps {
  children: ReactNode;
  roles: UserRole | UserRole[];
  redirectTo?: string;
}

/**
 * A component that protects routes by checking if the user has the required role(s).
 * If the user is not authenticated, they are redirected to the login page.
 * If the user is authenticated but doesn't have the required role(s), they are redirected to the unauthorized page.
 *
 * @param children - The components to render if the user has the required role(s)
 * @param roles - The role(s) required to access the route
 * @param redirectTo - The path to redirect to if the user doesn't have the required role(s) (default: /unauthorized)
 */
const RoleBasedRoute: React.FC<RoleBasedRouteProps> = ({
  children,
  roles,
  redirectTo = '/unauthorized'
}) => {
  const { hasRole } = useAuth();

  // First, check if the user is authenticated using ProtectedRoute
  return (
    <ProtectedRoute>
      {hasRole(roles) ? (
        // User has the required role(s), render the children
        <>{children}</>
      ) : (
        // User doesn't have the required role(s), redirect to unauthorized page
        <Navigate to={redirectTo} replace />
      )}
    </ProtectedRoute>
  );
};

export default RoleBasedRoute;
