import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Chip,
  IconButton,
  Collapse,
  Divider,
  InputAdornment,
  Autocomplete,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList,
  Clear,
  CalendarMonth,
  Person,
  Description,
  ExpandMore,
  ExpandLess
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

// Interface for search filters
interface SearchFilters {
  searchTerm: string;
  status: string[];
  dateRange: {
    startDate: Date | null;
    endDate: Date | null;
  };
  parties: string[];
  signatureStatus: string;
  templateType: string;
}

// Interface for search results
interface Agreement {
  id: string;
  title: string;
  status: 'draft' | 'pending' | 'active' | 'completed' | 'disputed';
  createdAt: string;
  parties: {
    id: string;
    name: string;
    role: 'owner' | 'renter';
  }[];
  templateType: string;
  signatureStatus: 'unsigned' | 'partially_signed' | 'fully_signed';
}

// Props interface
interface AgreementSearchProps {
  onSearch: (filters: SearchFilters) => void;
  onResultSelect?: (agreementId: string) => void;
  initialFilters?: Partial<SearchFilters>;
  loading?: boolean;
  error?: string | null;
  results?: Agreement[];
  showResults?: boolean;
}

// Mock data for parties autocomplete
const MOCK_PARTIES = [
  { id: 'user-1', name: 'John Doe' },
  { id: 'user-2', name: 'Jane Smith' },
  { id: 'user-3', name: 'Michael Johnson' },
  { id: 'user-4', name: 'Sarah Williams' },
  { id: 'user-5', name: 'Robert Brown' },
  { id: 'user-6', name: 'Emily Davis' }
];

const AgreementSearch: React.FC<AgreementSearchProps> = ({
  onSearch,
  onResultSelect,
  initialFilters,
  loading = false,
  error = null,
  results = [],
  showResults = true
}) => {
  // Default filters
  const defaultFilters: SearchFilters = {
    searchTerm: '',
    status: [],
    dateRange: {
      startDate: null,
      endDate: null
    },
    parties: [],
    signatureStatus: 'all',
    templateType: 'all'
  };

  // State
  const [filters, setFilters] = useState<SearchFilters>({
    ...defaultFilters,
    ...initialFilters
  });
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [parties, setParties] = useState<{ id: string; name: string }[]>([]);
  const [partiesLoading, setPartiesLoading] = useState(false);

  // Fetch parties for autocomplete
  useEffect(() => {
    const fetchParties = async () => {
      setPartiesLoading(true);
      try {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // In a real app, this would be an API call
        setParties(MOCK_PARTIES);
      } catch (err) {
        console.error('Error fetching parties:', err);
      } finally {
        setPartiesLoading(false);
      }
    };

    fetchParties();
  }, []);

  // Handle search term change
  const handleSearchTermChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFilters(prev => ({
      ...prev,
      searchTerm: event.target.value
    }));
  };

  // Handle status change
  const handleStatusChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setFilters(prev => ({
      ...prev,
      status: event.target.value as string[]
    }));
  };

  // Handle start date change
  const handleStartDateChange = (date: Date | null) => {
    setFilters(prev => ({
      ...prev,
      dateRange: {
        ...prev.dateRange,
        startDate: date
      }
    }));
  };

  // Handle end date change
  const handleEndDateChange = (date: Date | null) => {
    setFilters(prev => ({
      ...prev,
      dateRange: {
        ...prev.dateRange,
        endDate: date
      }
    }));
  };

  // Handle parties change
  const handlePartiesChange = (event: React.SyntheticEvent, value: { id: string; name: string }[]) => {
    setFilters(prev => ({
      ...prev,
      parties: value.map(v => v.id)
    }));
  };

  // Handle signature status change
  const handleSignatureStatusChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setFilters(prev => ({
      ...prev,
      signatureStatus: event.target.value as string
    }));
  };

  // Handle template type change
  const handleTemplateTypeChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setFilters(prev => ({
      ...prev,
      templateType: event.target.value as string
    }));
  };

  // Handle search
  const handleSearch = () => {
    onSearch(filters);
  };

  // Handle reset
  const handleReset = () => {
    setFilters(defaultFilters);
  };

  // Handle result click
  const handleResultClick = (agreementId: string) => {
    if (onResultSelect) {
      onResultSelect(agreementId);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return '#9e9e9e';
      case 'pending':
        return '#ff9800';
      case 'active':
        return '#4caf50';
      case 'completed':
        return '#2196f3';
      case 'disputed':
        return '#f44336';
      default:
        return '#9e9e9e';
    }
  };

  // Get signature status text
  const getSignatureStatusText = (status: string) => {
    switch (status) {
      case 'unsigned':
        return 'Unsigned';
      case 'partially_signed':
        return 'Partially Signed';
      case 'fully_signed':
        return 'Fully Signed';
      default:
        return status;
    }
  };

  return (
    <Box>
      {/* Search Form */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Search Agreements
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Find agreements by keyword, status, date, or parties involved.
          </Typography>
        </Box>

        <Grid container spacing={2}>
          {/* Basic Search */}
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Search Agreements"
              placeholder="Search by title, ID, or content..."
              variant="outlined"
              value={filters.searchTerm}
              onChange={handleSearchTermChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                endAdornment: filters.searchTerm ? (
                  <InputAdornment position="end">
                    <IconButton
                      size="small"
                      onClick={() => setFilters(prev => ({ ...prev, searchTerm: '' }))}
                    >
                      <Clear fontSize="small" />
                    </IconButton>
                  </InputAdornment>
                ) : null
              }}
            />
          </Grid>

          {/* Status Filter */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>Status</InputLabel>
              <Select
                multiple
                value={filters.status}
                onChange={handleStatusChange}
                label="Status"
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {(selected as string[]).map((value) => (
                      <Chip 
                        key={value} 
                        label={value.charAt(0).toUpperCase() + value.slice(1)} 
                        size="small" 
                        sx={{ bgcolor: getStatusColor(value), color: 'white' }}
                      />
                    ))}
                  </Box>
                )}
              >
                <MenuItem value="draft">Draft</MenuItem>
                <MenuItem value="pending">Pending</MenuItem>
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="completed">Completed</MenuItem>
                <MenuItem value="disputed">Disputed</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {/* Advanced Filters Toggle */}
          <Grid item xs={12} sm={6}>
            <Button
              fullWidth
              variant="outlined"
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              endIcon={showAdvancedFilters ? <ExpandLess /> : <ExpandMore />}
              sx={{ height: '56px' }}
            >
              {showAdvancedFilters ? 'Hide Advanced Filters' : 'Show Advanced Filters'}
            </Button>
          </Grid>

          {/* Advanced Filters */}
          <Grid item xs={12}>
            <Collapse in={showAdvancedFilters}>
              <Box sx={{ mt: 2 }}>
                <Grid container spacing={2}>
                  {/* Date Range */}
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                      <CalendarMonth fontSize="small" sx={{ mr: 1 }} />
                      Date Range
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                          <DatePicker
                            label="From"
                            value={filters.dateRange.startDate}
                            onChange={handleStartDateChange}
                            slotProps={{ textField: { fullWidth: true, size: 'small' } }}
                          />
                        </LocalizationProvider>
                      </Grid>
                      <Grid item xs={6}>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                          <DatePicker
                            label="To"
                            value={filters.dateRange.endDate}
                            onChange={handleEndDateChange}
                            slotProps={{ textField: { fullWidth: true, size: 'small' } }}
                          />
                        </LocalizationProvider>
                      </Grid>
                    </Grid>
                  </Grid>

                  {/* Parties */}
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                      <Person fontSize="small" sx={{ mr: 1 }} />
                      Parties Involved
                    </Typography>
                    <Autocomplete
                      multiple
                      options={parties}
                      getOptionLabel={(option) => option.name}
                      loading={partiesLoading}
                      onChange={handlePartiesChange}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Select Parties"
                          variant="outlined"
                          size="small"
                          InputProps={{
                            ...params.InputProps,
                            endAdornment: (
                              <>
                                {partiesLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                {params.InputProps.endAdornment}
                              </>
                            )
                          }}
                        />
                      )}
                    />
                  </Grid>

                  {/* Signature Status */}
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                      <Description fontSize="small" sx={{ mr: 1 }} />
                      Signature Status
                    </Typography>
                    <FormControl fullWidth variant="outlined" size="small">
                      <Select
                        value={filters.signatureStatus}
                        onChange={handleSignatureStatusChange}
                      >
                        <MenuItem value="all">All Signature Statuses</MenuItem>
                        <MenuItem value="unsigned">Unsigned</MenuItem>
                        <MenuItem value="partially_signed">Partially Signed</MenuItem>
                        <MenuItem value="fully_signed">Fully Signed</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  {/* Template Type */}
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                      <Description fontSize="small" sx={{ mr: 1 }} />
                      Template Type
                    </Typography>
                    <FormControl fullWidth variant="outlined" size="small">
                      <Select
                        value={filters.templateType}
                        onChange={handleTemplateTypeChange}
                      >
                        <MenuItem value="all">All Template Types</MenuItem>
                        <MenuItem value="standard">Standard Rental</MenuItem>
                        <MenuItem value="vehicle">Vehicle Rental</MenuItem>
                        <MenuItem value="real-estate">Real Estate</MenuItem>
                        <MenuItem value="equipment">Equipment Rental</MenuItem>
                        <MenuItem value="event">Event Space</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </Box>
            </Collapse>
          </Grid>

          {/* Action Buttons */}
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>
              <Button
                variant="outlined"
                onClick={handleReset}
              >
                Reset
              </Button>
              <Button
                variant="contained"
                onClick={handleSearch}
                startIcon={<SearchIcon />}
                disabled={loading}
              >
                {loading ? 'Searching...' : 'Search'}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Search Results */}
      {showResults && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Search Results
          </Typography>

          {loading && (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          )}

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {!loading && !error && results.length === 0 && (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="body1" color="text.secondary">
                No agreements found matching your search criteria.
              </Typography>
            </Box>
          )}

          {!loading && !error && results.length > 0 && (
            <Box>
              {results.map((agreement, index) => (
                <React.Fragment key={agreement.id}>
                  <Box 
                    sx={{ 
                      py: 2, 
                      cursor: 'pointer',
                      '&:hover': {
                        bgcolor: 'rgba(0, 0, 0, 0.04)'
                      }
                    }}
                    onClick={() => handleResultClick(agreement.id)}
                  >
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="subtitle1" fontWeight="medium">
                          {agreement.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          ID: {agreement.id}
                        </Typography>
                      </Grid>
                      <Grid item xs={6} sm={3}>
                        <Typography variant="body2" color="text.secondary">
                          Created: {formatDate(agreement.createdAt)}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                          <Chip 
                            label={agreement.status.charAt(0).toUpperCase() + agreement.status.slice(1)} 
                            size="small"
                            sx={{ 
                              bgcolor: getStatusColor(agreement.status),
                              color: 'white'
                            }}
                          />
                        </Box>
                      </Grid>
                      <Grid item xs={6} sm={3}>
                        <Typography variant="body2" color="text.secondary">
                          Signature: {getSignatureStatusText(agreement.signatureStatus)}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                          Type: {agreement.templateType}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Box>
                  {index < results.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </Box>
          )}
        </Paper>
      )}
    </Box>
  );
};

export default AgreementSearch;
