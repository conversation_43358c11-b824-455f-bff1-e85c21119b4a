import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ClauseExplainer from './ClauseExplainer';
import { useMediaQuery } from '../../hooks/useMediaQuery';

interface AgreementViewerProps {
  agreementHtml: string;
  contentHash: string;
  status: 'draft' | 'pending' | 'active' | 'completed' | 'disputed';
  ownerSigned: boolean;
  renterSigned: boolean;
  isOwner: boolean;
  isRenter: boolean;
  onSignClick?: () => void;
  onDownloadClick?: () => void;
  className?: string;
  compact?: boolean; // For smaller UI in constrained spaces
  title?: string; // Optional custom title
}

const AgreementViewer: React.FC<AgreementViewerProps> = ({
  agreementHtml,
  contentHash,
  status,
  ownerSigned,
  renterSigned,
  isOwner,
  isRenter,
  onSignClick,
  onDownloadClick,
  className = '',
  compact = false,
  title = 'Rental Agreement'
}) => {
  const [showExplainer, setShowExplainer] = useState(false);
  const [selectedClause, setSelectedClause] = useState<string>('');
  const [zoomLevel, setZoomLevel] = useState<number>(1);
  const contentRef = useRef<HTMLDivElement>(null);

  // Use media query to determine if we're on mobile
  const isMobile = useMediaQuery('(max-width: 640px)');
  const isTablet = useMediaQuery('(min-width: 641px) and (max-width: 1024px)');

  // Function to handle clause selection for explanation
  const handleClauseClick = (clauseText: string) => {
    setSelectedClause(clauseText);
    setShowExplainer(true);
  };

  // Make the function available to the window object
  // This is a workaround for the TypeScript error
  // In a real application, you would use a ref or a more elegant solution
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (window as any).handleClauseClick = handleClauseClick;

  // Determine if the current user can sign
  const canSign = () => {
    if (status !== 'draft' && status !== 'pending') return false;
    if (isOwner && !ownerSigned) return true;
    if (isRenter && !renterSigned) return true;
    return false;
  };

  // Get status badge color
  const getStatusColor = () => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'active': return 'bg-green-100 text-green-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      case 'disputed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Handle zoom in/out
  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 0.1, 1.5));
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 0.1, 0.7));
  };

  // Reset zoom when component unmounts
  useEffect(() => {
    return () => {
      setZoomLevel(1);
    };
  }, []);

  return (
    <div className={`bg-white rounded-lg shadow-md overflow-hidden ${className}`}>
      {/* Agreement Header */}
      <div className={`${compact ? 'p-3' : 'p-4'} border-b`}>
        <div className="flex justify-between items-center">
          <h2 className={`${compact ? 'text-lg' : 'text-xl'} font-semibold`}>{title}</h2>
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor()}`}>
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </span>
        </div>
        <p className="text-xs text-gray-500 mt-1">
          Content Hash: {contentHash.substring(0, isMobile ? 6 : 8)}...
        </p>
      </div>

      {/* Signature Status */}
      <div className={`${compact ? 'p-3' : 'p-4'} bg-gray-50 border-b`}>
        <div className={`${isMobile ? 'flex flex-col space-y-2' : 'flex justify-between'}`}>
          <div className={isMobile ? 'flex justify-between items-center' : ''}>
            <p className="text-sm font-medium">Owner Signature:</p>
            <p className="text-sm">
              {ownerSigned ? (
                <span className="text-green-600">✓ Signed</span>
              ) : (
                <span className="text-gray-500">Awaiting signature</span>
              )}
            </p>
          </div>
          <div className={isMobile ? 'flex justify-between items-center' : ''}>
            <p className="text-sm font-medium">Renter Signature:</p>
            <p className="text-sm">
              {renterSigned ? (
                <span className="text-green-600">✓ Signed</span>
              ) : (
                <span className="text-gray-500">Awaiting signature</span>
              )}
            </p>
          </div>
        </div>
      </div>

      {/* Zoom Controls */}
      <div className="flex justify-end items-center px-4 py-2 border-b">
        <div className="flex space-x-2 items-center">
          <button
            type="button"
            onClick={handleZoomOut}
            className="p-1 rounded-md bg-gray-100 hover:bg-gray-200 text-gray-700 touch-friendly"
            aria-label="Zoom out"
            disabled={zoomLevel <= 0.7}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clipRule="evenodd" />
            </svg>
          </button>
          <span className="text-xs text-gray-500">{Math.round(zoomLevel * 100)}%</span>
          <button
            type="button"
            onClick={handleZoomIn}
            className="p-1 rounded-md bg-gray-100 hover:bg-gray-200 text-gray-700 touch-friendly"
            aria-label="Zoom in"
            disabled={zoomLevel >= 1.5}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>

      {/* Agreement Content */}
      <div
        className={`${compact ? 'p-3' : 'p-4'} border-b overflow-auto`}
        style={{ maxHeight: compact ? '300px' : '500px' }}
      >
        <div
          ref={contentRef}
          className="prose max-w-none agreement-content"
          style={{
            transform: `scale(${zoomLevel})`,
            transformOrigin: 'top left',
            width: `${100 / zoomLevel}%`
          }}
          dangerouslySetInnerHTML={{ __html: agreementHtml }}
        />
      </div>

      {/* Action Buttons */}
      <div className={`${compact ? 'p-3' : 'p-4'} ${isMobile ? 'flex flex-col space-y-2' : 'flex justify-between items-center'}`}>
        <div className={`${isMobile ? 'flex justify-between' : ''}`}>
          <button
            type="button"
            onClick={() => window.print()}
            className={`
              ${compact ? 'px-3 py-1.5 text-sm' : 'px-4 py-2'}
              bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors touch-friendly
              ${isMobile ? 'flex-1 mr-2' : ''}
            `}
            aria-label="Print agreement"
          >
            <span className="flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5 4v3H4a2 2 0 00-2 2v3a2 2 0 002 2h1v2a2 2 0 002 2h6a2 2 0 002-2v-2h1a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zm0 8H7v4h6v-4z" clipRule="evenodd" />
              </svg>
              Print
            </span>
          </button>

          {onDownloadClick && (
            <button
              type="button"
              onClick={onDownloadClick}
              className={`
                ${compact ? 'px-3 py-1.5 text-sm' : 'px-4 py-2'}
                bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors touch-friendly
                ${isMobile ? 'flex-1' : 'ml-2'}
              `}
              aria-label="Download agreement as PDF"
            >
              <span className="flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
                Download PDF
              </span>
            </button>
          )}
        </div>

        {canSign() && onSignClick && (
          <motion.button
            whileTap={{ scale: 0.95 }}
            type="button"
            onClick={onSignClick}
            className={`
              ${compact ? 'px-3 py-1.5 text-sm' : 'px-4 py-2'}
              bg-primary text-white rounded-md hover:bg-primary-dark transition-colors touch-friendly
              ${isMobile ? 'w-full' : ''}
            `}
            aria-label="Sign agreement"
          >
            <span className="flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z" />
                <path fillRule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clipRule="evenodd" />
              </svg>
              Sign Agreement
            </span>
          </motion.button>
        )}
      </div>

      {/* Clause Explainer Modal */}
      <AnimatePresence>
        {showExplainer && (
          <ClauseExplainer
            clauseText={selectedClause}
            onClose={() => setShowExplainer(false)}
          />
        )}
      </AnimatePresence>

      {/* CSS for making clauses clickable */}
      <style>{`
        .agreement-content .clause {
          cursor: pointer;
          position: relative;
          border-bottom: 1px dotted #3b82f6;
          padding-bottom: 1px;
        }
        .agreement-content .clause:hover {
          background-color: rgba(59, 130, 246, 0.1);
        }

        /* Desktop tooltip */
        @media (min-width: 768px) {
          .agreement-content .clause::after {
            content: '?';
            position: absolute;
            right: -20px;
            top: 0;
            background-color: #e5e7eb;
            color: #374151;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            opacity: 0;
            transition: opacity 0.2s;
          }
          .agreement-content .clause:hover::after {
            opacity: 1;
          }
        }

        /* Mobile tooltip */
        @media (max-width: 767px) {
          .agreement-content .clause {
            border-bottom: 1px dotted #3b82f6;
            text-decoration: none;
          }
        }

        /* Print styles */
        @media print {
          .agreement-content {
            transform: scale(1) !important;
            width: 100% !important;
          }
          .agreement-content .clause {
            border-bottom: none;
            text-decoration: none;
          }
          .agreement-content .clause::after {
            display: none;
          }
        }
      `}</style>
    </div>
  );
};

export default AgreementViewer;