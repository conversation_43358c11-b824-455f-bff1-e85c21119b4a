import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useMediaQuery } from '../../hooks/useMediaQuery';
import { API_URL } from '../../config/constants';

interface ClauseExplainerProps {
  clauseText: string;
  onClose: () => void;
  compact?: boolean; // For smaller UI in constrained spaces
}

const ClauseExplainer: React.FC<ClauseExplainerProps> = ({
  clauseText,
  onClose,
  compact = false
}) => {
  const [explanation, setExplanation] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const modalRef = useRef<HTMLDivElement>(null);

  // Use media query to determine if we're on mobile
  const isMobile = useMediaQuery('(max-width: 640px)');

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  // Close modal when pressing Escape key
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscapeKey);
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [onClose]);

  useEffect(() => {
    // In a real implementation, this would call an API to get the explanation
    // For now, we'll simulate an API call with a timeout
    const getExplanation = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // In a real implementation, this would be an API call
        // const response = await axios.post(`${API_URL}/api/v1/agreements/explain-clause`, {
        //   clause_text: clauseText
        // });
        // setExplanation(response.data.explanation);

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Generate a simple explanation based on the clause text
        // In production, this would come from the AI service
        const simulatedExplanation = generateSimpleExplanation(clauseText);
        setExplanation(simulatedExplanation);
      } catch (error) {
        console.error('Error getting explanation:', error);
        setError('Sorry, we could not generate an explanation for this clause. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    if (clauseText) {
      getExplanation();
    }
  }, [clauseText]);

  // Simple function to generate explanations based on keywords
  // This is just a placeholder - in production, this would use the AI service
  const generateSimpleExplanation = (text: string): string => {
    const lowerText = text.toLowerCase();

    if (lowerText.includes('damage')) {
      return 'This clause explains your responsibility for any damage to the item during the rental period. It means you may need to pay for repairs if the item is returned damaged.';
    } else if (lowerText.includes('payment')) {
      return 'This clause outlines when and how you need to pay for the rental. It specifies the payment method, due dates, and any late payment penalties.';
    } else if (lowerText.includes('cancel')) {
      return 'This clause explains the cancellation policy, including any fees that may apply if you cancel the rental after a certain point.';
    } else if (lowerText.includes('insurance')) {
      return 'This clause describes insurance requirements for the rental. It specifies who is responsible for insurance coverage and what types of coverage are required.';
    } else if (lowerText.includes('return')) {
      return 'This clause outlines the conditions for returning the item, including the expected condition, timing, and any penalties for late returns.';
    } else if (lowerText.includes('liability')) {
      return 'This clause defines who is responsible (liable) for various situations that might occur during the rental period, such as accidents, injuries, or property damage.';
    } else if (lowerText.includes('term')) {
      return 'This clause specifies the duration of the rental agreement, including start and end dates, and any conditions for extending the rental period.';
    } else {
      return 'This clause is part of your rental agreement. It establishes the legal terms that both parties agree to follow during the rental period.';
    }
  };

  // Retry getting explanation
  const handleRetry = () => {
    if (clauseText) {
      setIsLoading(true);
      setError(null);

      // Simulate API call
      setTimeout(() => {
        const simulatedExplanation = generateSimpleExplanation(clauseText);
        setExplanation(simulatedExplanation);
        setIsLoading(false);
      }, 1000);
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      >
        <motion.div
          ref={modalRef}
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          transition={{ type: 'spring', damping: 25, stiffness: 300 }}
          className={`bg-white rounded-lg shadow-xl w-full ${isMobile ? 'max-w-full' : 'max-w-md'} max-h-[90vh] flex flex-col`}
        >
          <div className={`${compact ? 'p-3' : 'p-4'} border-b flex justify-between items-center`}>
            <h3 className={`${compact ? 'text-base' : 'text-lg'} font-semibold`}>Clause Explanation</h3>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100 transition-colors touch-friendly"
              aria-label="Close explanation"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className={`${compact ? 'p-3' : 'p-4'} overflow-y-auto flex-grow`}>
            <div className="mb-4 p-3 bg-gray-50 rounded-md">
              <h4 className={`${compact ? 'text-xs' : 'text-sm'} font-medium text-gray-500 mb-1`}>Original Clause:</h4>
              <p className={`${compact ? 'text-sm' : 'text-base'} text-gray-800`}>{clauseText}</p>
            </div>

            <div>
              <h4 className={`${compact ? 'text-xs' : 'text-sm'} font-medium text-gray-500 mb-1`}>Simple Explanation:</h4>
              {isLoading ? (
                <div className="flex justify-center items-center h-20">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                </div>
              ) : error ? (
                <div className="p-3 bg-red-50 rounded-md">
                  <p className="text-red-600 mb-2">{error}</p>
                  <button
                    onClick={handleRetry}
                    className="text-sm bg-red-100 hover:bg-red-200 text-red-700 px-3 py-1 rounded-md transition-colors"
                  >
                    Try Again
                  </button>
                </div>
              ) : (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="p-3 bg-blue-50 rounded-md"
                >
                  <p className={`${compact ? 'text-sm' : 'text-base'} text-gray-800`}>{explanation}</p>
                </motion.div>
              )}
            </div>

            <div className="mt-4 text-xs text-gray-500">
              <p>Note: This explanation is provided to help you understand the legal language, but it is not legal advice. If you have concerns about this clause, consider consulting with a legal professional.</p>
            </div>
          </div>

          <div className={`${compact ? 'p-3' : 'p-4'} border-t`}>
            <motion.button
              whileTap={{ scale: 0.95 }}
              onClick={onClose}
              className={`
                w-full ${compact ? 'py-1.5 text-sm' : 'py-2'}
                bg-primary text-white rounded-md hover:bg-primary-dark
                transition-colors touch-friendly
              `}
            >
              Got it
            </motion.button>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ClauseExplainer;