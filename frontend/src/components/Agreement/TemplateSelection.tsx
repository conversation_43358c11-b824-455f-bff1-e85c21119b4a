import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActionArea,
  Button,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider
} from '@mui/material';
import {
  Description,
  Check,
  Preview,
  ArrowForward,
  FilterList
} from '@mui/icons-material';

// Template interface
interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  jurisdiction: string;
  previewImage: string;
  popularity: number;
  lastUpdated: string;
  clauses: number;
  variables: string[];
}

// Mock template data
const MOCK_TEMPLATES: Template[] = [
  {
    id: 'template-1',
    name: 'Standard Rental Agreement',
    description: 'A comprehensive rental agreement suitable for most items and equipment.',
    category: 'general',
    jurisdiction: 'United States',
    previewImage: 'https://via.placeholder.com/300x200?text=Standard+Rental+Agreement',
    popularity: 4.8,
    lastUpdated: '2023-05-15',
    clauses: 12,
    variables: ['item_name', 'rental_period', 'rental_fee', 'deposit_amount', 'owner_name', 'renter_name']
  },
  {
    id: 'template-2',
    name: 'Vehicle Rental Agreement',
    description: 'Specialized agreement for vehicle rentals with additional clauses for insurance and liability.',
    category: 'vehicle',
    jurisdiction: 'United States',
    previewImage: 'https://via.placeholder.com/300x200?text=Vehicle+Rental+Agreement',
    popularity: 4.6,
    lastUpdated: '2023-06-01',
    clauses: 18,
    variables: ['vehicle_make', 'vehicle_model', 'vehicle_year', 'license_plate', 'mileage_limit', 'fuel_policy']
  },
  {
    id: 'template-3',
    name: 'Real Estate Rental Agreement',
    description: 'Comprehensive lease agreement for residential and commercial properties.',
    category: 'real-estate',
    jurisdiction: 'United States',
    previewImage: 'https://via.placeholder.com/300x200?text=Real+Estate+Rental',
    popularity: 4.9,
    lastUpdated: '2023-06-10',
    clauses: 24,
    variables: ['property_address', 'lease_term', 'monthly_rent', 'security_deposit', 'utilities_included']
  },
  {
    id: 'template-4',
    name: 'Equipment Rental Agreement',
    description: 'Specialized agreement for heavy equipment and machinery rentals.',
    category: 'equipment',
    jurisdiction: 'United States',
    previewImage: 'https://via.placeholder.com/300x200?text=Equipment+Rental',
    popularity: 4.5,
    lastUpdated: '2023-05-20',
    clauses: 16,
    variables: ['equipment_type', 'equipment_serial', 'rental_rate', 'maintenance_responsibility']
  },
  {
    id: 'template-5',
    name: 'Event Space Rental Agreement',
    description: 'Agreement for renting venues and event spaces with special provisions for events.',
    category: 'event',
    jurisdiction: 'United States',
    previewImage: 'https://via.placeholder.com/300x200?text=Event+Space+Rental',
    popularity: 4.7,
    lastUpdated: '2023-05-25',
    clauses: 14,
    variables: ['venue_name', 'event_date', 'event_type', 'guest_count', 'catering_allowed']
  },
  {
    id: 'template-6',
    name: 'Photography Equipment Rental',
    description: 'Specialized agreement for camera and photography equipment rentals.',
    category: 'equipment',
    jurisdiction: 'United States',
    previewImage: 'https://via.placeholder.com/300x200?text=Photography+Equipment',
    popularity: 4.4,
    lastUpdated: '2023-04-30',
    clauses: 15,
    variables: ['camera_model', 'lens_details', 'accessories', 'replacement_value']
  }
];

interface TemplateSelectionProps {
  onSelect: (templateId: string) => void;
  selectedTemplateId?: string;
  rentalType?: string;
  itemCategory?: string;
}

const TemplateSelection: React.FC<TemplateSelectionProps> = ({
  onSelect,
  selectedTemplateId,
  rentalType,
  itemCategory
}) => {
  // State
  const [templates, setTemplates] = useState<Template[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [previewTemplate, setPreviewTemplate] = useState<Template | null>(null);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [jurisdictionFilter, setJurisdictionFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Fetch templates
  useEffect(() => {
    const fetchTemplates = async () => {
      setLoading(true);
      setError(null);

      try {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // In a real app, this would be an API call
        setTemplates(MOCK_TEMPLATES);
      } catch (err) {
        setError('Failed to load templates. Please try again.');
        console.error('Error fetching templates:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchTemplates();
  }, []);

  // Apply filters
  useEffect(() => {
    let filtered = [...templates];

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        template =>
          template.name.toLowerCase().includes(term) ||
          template.description.toLowerCase().includes(term)
      );
    }

    // Apply category filter
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(template => template.category === categoryFilter);
    }

    // Apply jurisdiction filter
    if (jurisdictionFilter !== 'all') {
      filtered = filtered.filter(template => template.jurisdiction === jurisdictionFilter);
    }

    // Apply rental type and item category filters if provided
    if (rentalType) {
      // This would be more sophisticated in a real app
      const rentalTypeMap: Record<string, string[]> = {
        'short-term': ['general', 'equipment', 'event'],
        'long-term': ['general', 'real-estate', 'vehicle'],
        'auction': ['general', 'equipment', 'vehicle']
      };
      
      if (rentalTypeMap[rentalType]) {
        filtered = filtered.filter(template => 
          rentalTypeMap[rentalType].includes(template.category)
        );
      }
    }

    if (itemCategory) {
      // This would be more sophisticated in a real app
      const categoryMap: Record<string, string> = {
        'electronics': 'equipment',
        'vehicles': 'vehicle',
        'real-estate': 'real-estate',
        'photography': 'equipment',
        'event-spaces': 'event'
      };
      
      if (categoryMap[itemCategory]) {
        filtered = filtered.filter(template => 
          template.category === categoryMap[itemCategory]
        );
      }
    }

    setFilteredTemplates(filtered);
  }, [templates, searchTerm, categoryFilter, jurisdictionFilter, rentalType, itemCategory]);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Handle template selection
  const handleTemplateSelect = (templateId: string) => {
    onSelect(templateId);
  };

  // Handle template preview
  const handlePreview = (template: Template) => {
    setPreviewTemplate(template);
    setPreviewOpen(true);
  };

  // Handle preview close
  const handlePreviewClose = () => {
    setPreviewOpen(false);
  };

  // Get unique categories
  const categories = ['all', ...new Set(templates.map(template => template.category))];

  // Get unique jurisdictions
  const jurisdictions = ['all', ...new Set(templates.map(template => template.jurisdiction))];

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5">Select Agreement Template</Typography>
        <Button
          variant="outlined"
          startIcon={<FilterList />}
          onClick={() => setShowFilters(!showFilters)}
        >
          {showFilters ? 'Hide Filters' : 'Show Filters'}
        </Button>
      </Box>

      {/* Filters */}
      {showFilters && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Search Templates"
                variant="outlined"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                size="small"
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth size="small">
                <InputLabel>Category</InputLabel>
                <Select
                  value={categoryFilter}
                  label="Category"
                  onChange={(e) => setCategoryFilter(e.target.value)}
                >
                  <MenuItem value="all">All Categories</MenuItem>
                  {categories
                    .filter(cat => cat !== 'all')
                    .map(category => (
                      <MenuItem key={category} value={category}>
                        {category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ')}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth size="small">
                <InputLabel>Jurisdiction</InputLabel>
                <Select
                  value={jurisdictionFilter}
                  label="Jurisdiction"
                  onChange={(e) => setJurisdictionFilter(e.target.value)}
                >
                  <MenuItem value="all">All Jurisdictions</MenuItem>
                  {jurisdictions
                    .filter(jur => jur !== 'all')
                    .map(jurisdiction => (
                      <MenuItem key={jurisdiction} value={jurisdiction}>
                        {jurisdiction}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Paper>
      )}

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab label="All Templates" />
          <Tab label="Recommended" />
          <Tab label="Recently Used" />
        </Tabs>
      </Paper>

      {/* Templates Grid */}
      <Grid container spacing={3}>
        {filteredTemplates.length > 0 ? (
          filteredTemplates.map((template) => (
            <Grid item xs={12} sm={6} md={4} key={template.id}>
              <Card 
                sx={{ 
                  height: '100%',
                  border: selectedTemplateId === template.id ? '2px solid #1976d2' : 'none',
                  boxShadow: selectedTemplateId === template.id ? '0 0 10px rgba(25, 118, 210, 0.3)' : 'inherit'
                }}
              >
                <CardActionArea 
                  onClick={() => handleTemplateSelect(template.id)}
                  sx={{ height: '100%', display: 'flex', flexDirection: 'column', alignItems: 'stretch' }}
                >
                  <CardMedia
                    component="img"
                    height="140"
                    image={template.previewImage}
                    alt={template.name}
                  />
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Typography variant="h6" component="div">
                        {template.name}
                      </Typography>
                      {selectedTemplateId === template.id && (
                        <Check color="primary" />
                      )}
                    </Box>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {template.description}
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
                      <Chip 
                        label={template.category.charAt(0).toUpperCase() + template.category.slice(1).replace('-', ' ')} 
                        size="small" 
                        color="primary" 
                        variant="outlined" 
                      />
                      <Chip 
                        label={template.jurisdiction} 
                        size="small" 
                        color="default" 
                        variant="outlined" 
                      />
                      <Chip 
                        label={`${template.clauses} Clauses`} 
                        size="small" 
                        color="default" 
                        variant="outlined" 
                      />
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="caption" color="text.secondary">
                        Updated: {template.lastUpdated}
                      </Typography>
                      <Button 
                        size="small" 
                        onClick={(e) => {
                          e.stopPropagation();
                          handlePreview(template);
                        }}
                        startIcon={<Preview />}
                      >
                        Preview
                      </Button>
                    </Box>
                  </CardContent>
                </CardActionArea>
              </Card>
            </Grid>
          ))
        ) : (
          <Grid item xs={12}>
            <Paper sx={{ p: 3, textAlign: 'center' }}>
              <Typography variant="body1" color="text.secondary">
                No templates found matching your criteria.
              </Typography>
            </Paper>
          </Grid>
        )}
      </Grid>

      {/* Preview Dialog */}
      <Dialog
        open={previewOpen}
        onClose={handlePreviewClose}
        maxWidth="md"
        fullWidth
      >
        {previewTemplate && (
          <>
            <DialogTitle>
              <Typography variant="h6">{previewTemplate.name}</Typography>
              <Typography variant="caption" color="text.secondary">
                Template Preview
              </Typography>
            </DialogTitle>
            <DialogContent dividers>
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Template Details
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2">
                      <strong>Category:</strong> {previewTemplate.category.charAt(0).toUpperCase() + previewTemplate.category.slice(1).replace('-', ' ')}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Jurisdiction:</strong> {previewTemplate.jurisdiction}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Last Updated:</strong> {previewTemplate.lastUpdated}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2">
                      <strong>Number of Clauses:</strong> {previewTemplate.clauses}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Popularity:</strong> {previewTemplate.popularity}/5.0
                    </Typography>
                  </Grid>
                </Grid>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle1" gutterBottom>
                Variables
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 3 }}>
                {previewTemplate.variables.map((variable) => (
                  <Chip 
                    key={variable} 
                    label={variable.replace('_', ' ')} 
                    size="small" 
                  />
                ))}
              </Box>

              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle1" gutterBottom>
                Preview
              </Typography>
              <Box 
                sx={{ 
                  border: '1px solid #ddd', 
                  borderRadius: 1, 
                  p: 2, 
                  height: 300, 
                  overflow: 'auto',
                  bgcolor: '#f9f9f9'
                }}
              >
                <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                  This is a preview of the template structure. The actual agreement will be populated with your specific rental details.
                </Typography>
                {/* Mock preview content - in a real app, this would be actual template content */}
                <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>RENTAL AGREEMENT</Typography>
                <Typography variant="body2" paragraph>
                  This Rental Agreement ("Agreement") is made and entered into on [DATE] by and between:
                </Typography>
                <Typography variant="body2" paragraph>
                  <strong>Owner:</strong> [OWNER_NAME], hereinafter referred to as "Owner"
                </Typography>
                <Typography variant="body2" paragraph>
                  <strong>Renter:</strong> [RENTER_NAME], hereinafter referred to as "Renter"
                </Typography>
                <Typography variant="h6" sx={{ mt: 3, mb: 1 }}>1. ITEM DETAILS</Typography>
                <Typography variant="body2" paragraph>
                  <strong>Item:</strong> [ITEM_NAME]
                </Typography>
                <Typography variant="body2" paragraph>
                  <strong>Description:</strong> [ITEM_DESCRIPTION]
                </Typography>
                {/* More mock content would go here */}
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={handlePreviewClose}>Close</Button>
              <Button 
                variant="contained" 
                endIcon={<ArrowForward />}
                onClick={() => {
                  handleTemplateSelect(previewTemplate.id);
                  handlePreviewClose();
                }}
              >
                Use This Template
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default TemplateSelection;
