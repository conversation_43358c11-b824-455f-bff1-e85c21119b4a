import React from 'react';
import { InteractiveButton, InteractiveLink } from '../../shared/ui';

/**
 * Demo component to showcase the interactive elements
 */
const InteractiveDemo: React.FC = () => {
  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-8">Interactive Elements Demo</h1>

      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4">Buttons</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
          <div className="p-4 border rounded">
            <h3 className="font-medium mb-2">Button Variants</h3>
            <div className="flex flex-wrap gap-2">
              <InteractiveButton variant="primary" title="Primary button">
                Primary
              </InteractiveButton>
              <InteractiveButton variant="secondary" title="Secondary button">
                Secondary
              </InteractiveButton>
              <InteractiveButton variant="accent" title="Accent button">
                Accent
              </InteractiveButton>
              <InteractiveButton variant="outline" title="Outline button">
                Outline
              </InteractiveButton>
              <InteractiveButton variant="glass" title="Glass button">
                Glass
              </InteractiveButton>
              <InteractiveButton variant="danger" title="Danger button">
                Danger
              </InteractiveButton>
              <InteractiveButton variant="success" title="Success button">
                Success
              </InteractiveButton>
            </div>
          </div>

          <div className="p-4 border rounded">
            <h3 className="font-medium mb-2">Button Sizes</h3>
            <div className="flex flex-wrap items-center gap-2">
              <InteractiveButton size="sm" title="Small button">
                Small
              </InteractiveButton>
              <InteractiveButton size="md" title="Medium button">
                Medium
              </InteractiveButton>
              <InteractiveButton size="lg" title="Large button">
                Large
              </InteractiveButton>
            </div>
          </div>

          <div className="p-4 border rounded">
            <h3 className="font-medium mb-2">Button States</h3>
            <div className="flex flex-wrap gap-2">
              <InteractiveButton isLoading title="Loading button">
                Loading
              </InteractiveButton>
              <InteractiveButton disabled title="Disabled button">
                Disabled
              </InteractiveButton>
              <InteractiveButton fullWidth title="Full width button">
                Full Width
              </InteractiveButton>
            </div>
          </div>
        </div>
      </section>

      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4">Links</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
          <div className="p-4 border rounded">
            <h3 className="font-medium mb-2">Link Variants</h3>
            <div className="flex flex-col gap-2">
              <InteractiveLink to="#" variant="primary" title="Primary link">
                Primary Link
              </InteractiveLink>
              <InteractiveLink to="#" variant="secondary" title="Secondary link">
                Secondary Link
              </InteractiveLink>
              <InteractiveLink to="#" variant="accent" title="Accent link">
                Accent Link
              </InteractiveLink>
              <InteractiveLink to="#" variant="dark" title="Dark link">
                Dark Link
              </InteractiveLink>
              <div className="bg-black p-2">
                <InteractiveLink to="#" variant="white" title="White link">
                  White Link
                </InteractiveLink>
              </div>
            </div>
          </div>

          <div className="p-4 border rounded">
            <h3 className="font-medium mb-2">Link Styles</h3>
            <div className="flex flex-col gap-2">
              <InteractiveLink to="#" underline title="Link with underline on hover">
                Underline on hover
              </InteractiveLink>
              <InteractiveLink to="#" bold title="Bold link">
                Bold Link
              </InteractiveLink>
              <InteractiveLink
                to="#"
                leftIcon={<span>👈</span>}
                title="Link with left icon"
              >
                With Left Icon
              </InteractiveLink>
              <InteractiveLink
                to="#"
                rightIcon={<span>👉</span>}
                title="Link with right icon"
              >
                With Right Icon
              </InteractiveLink>
            </div>
          </div>

          <div className="p-4 border rounded">
            <h3 className="font-medium mb-2">Button-Like Links</h3>
            <div className="flex flex-wrap gap-2">
              <InteractiveLink
                to="#"
                asButton
                buttonVariant="primary"
                title="Link that looks like a primary button"
              >
                Primary
              </InteractiveLink>
              <InteractiveLink
                to="#"
                asButton
                buttonVariant="secondary"
                title="Link that looks like a secondary button"
              >
                Secondary
              </InteractiveLink>
              <InteractiveLink
                to="#"
                asButton
                buttonVariant="outline"
                title="Link that looks like an outline button"
              >
                Outline
              </InteractiveLink>
              <InteractiveLink
                external
                externalHref="https://example.com"
                asButton
                buttonVariant="accent"
                title="External link that looks like a button"
              >
                External
              </InteractiveLink>
            </div>
          </div>
        </div>
      </section>

      <section>
        <h2 className="text-2xl font-semibold mb-4">Usage Examples</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 border rounded">
            <h3 className="font-medium mb-2">Search Form</h3>
            <div className="flex">
              <input
                type="text"
                placeholder="Search..."
                className="flex-1 px-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary"
              />
              <InteractiveButton
                variant="primary"
                className="rounded-l-none"
                title="Search button"
              >
                Search
              </InteractiveButton>
            </div>
          </div>

          <div className="p-4 border rounded">
            <h3 className="font-medium mb-2">Call to Action</h3>
            <div className="text-center">
              <h4 className="text-xl mb-2">Ready to get started?</h4>
              <p className="text-gray-600 mb-4">
                Join thousands of users who are already using our platform.
              </p>
              <div className="flex justify-center gap-4">
                <InteractiveLink
                  to="/signup"
                  asButton
                  buttonVariant="primary"
                  buttonSize="lg"
                  title="Sign up for an account"
                >
                  Sign Up
                </InteractiveLink>
                <InteractiveLink
                  to="/learn-more"
                  asButton
                  buttonVariant="outline"
                  buttonSize="lg"
                  title="Learn more about our platform"
                >
                  Learn More
                </InteractiveLink>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default InteractiveDemo;
