import React, { useEffect } from 'react';

const StyleDebugger: React.FC = () => {
  useEffect(() => {
    // This will run once when the component mounts
    console.log('=== STYLE DEBUGGER ===');

    // Check for any style elements in the head
    const styleElements = document.querySelectorAll('style');
    console.log(`Found ${styleElements.length} style elements:`);
    styleElements.forEach((style, index) => {
      console.log(`Style #${index + 1}:`, style.textContent);
    });

    // Check for any link elements (external stylesheets)
    const linkElements = document.querySelectorAll('link[rel="stylesheet"]');
    console.log(`Found ${linkElements.length} external stylesheets:`,
      Array.from(linkElements).map(link => link.getAttribute('href')));

    // Check computed styles for a test element
    const testDiv = document.createElement('div');
    testDiv.id = 'style-test-element';
    testDiv.className = 'bg-black';

    const testLink = document.createElement('a');
    testLink.textContent = 'Test Link';
    testLink.href = '#';
    testDiv.appendChild(testLink);

    document.body.appendChild(testDiv);

    // Get computed styles
    const computedStyle = window.getComputedStyle(testLink);
    console.log('Computed style for test link in bg-black:', {
      color: computedStyle.color,
      backgroundColor: computedStyle.backgroundColor,
      fontWeight: computedStyle.fontWeight
    });

    // Clean up
    setTimeout(() => {
      document.body.removeChild(testDiv);
    }, 1000);
  }, []);

  return null; // This component doesn't render anything
};

export default StyleDebugger;
