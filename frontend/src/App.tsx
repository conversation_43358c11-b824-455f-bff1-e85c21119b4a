import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { GoogleOAuthProvider } from '@react-oauth/google';
import { lazy, Suspense } from 'react';
import { HelmetProvider } from 'react-helmet-async';
import './App.css';
import Layout from './components/Layout/Layout';
import { ResourceHints } from './components/Performance';
import { ToastProvider } from './contexts/ToastContext';
import { AuthProvider } from './contexts/AuthContext';
import { UserRole } from './contexts/AuthContext';
import ErrorBoundary from './components/Error/ErrorBoundary';
import ErrorFallback from './components/Error/ErrorFallback';
import { trackWebVitals } from './utils/performanceMonitoring';

// Initialize i18n
import './i18n/i18n';

// Auth components
import ProtectedRoute from './components/Auth/ProtectedRoute';
import RoleBasedRoute from './components/Auth/RoleBasedRoute';

// Lazy load all pages for better performance
const Login = lazy(() => import('./pages/Login')); // Using the consolidated login page
const Register = lazy(() => import('./pages/Register'));
const Home = lazy(() => import('./pages/Home'));
const ProfilePage = lazy(() => import('./pages/ProfilePage'));
const ItemList = lazy(() => import('./pages/ItemList'));
const ItemForm = lazy(() => import('./pages/ItemForm'));
const SearchResults = lazy(() => import('./pages/SearchResults'));
const SearchPage = lazy(() => import('./pages/SearchPage'));
const BookingForm = lazy(() => import('./pages/BookingForm'));
const Payment = lazy(() => import('./pages/Payment'));
const ReportForm = lazy(() => import('./pages/ReportForm'));
const Messages = lazy(() => import('./pages/Messages'));
const ItemDetails = lazy(() => import('./pages/ItemDetails'));
const Notifications = lazy(() => import('./pages/Notifications'));
const AnalyticsDashboard = lazy(() => import('./pages/AnalyticsDashboard'));
const HowItWorks = lazy(() => import('./pages/HowItWorks'));

// Information pages
const Contact = lazy(() => import('./pages/Contact'));
const FinancialFAQ = lazy(() => import('./pages/FinancialFAQ'));
const Guidelines = lazy(() => import('./pages/Guidelines'));
const HelpCenter = lazy(() => import('./pages/HelpCenter'));
const Privacy = lazy(() => import('./pages/Privacy'));
const RentToBuy = lazy(() => import('./pages/RentToBuy'));
const SafetyCenter = lazy(() => import('./pages/SafetyCenter'));
const Sitemap = lazy(() => import('./pages/Sitemap'));
const Terms = lazy(() => import('./pages/Terms'));

// Auction-related pages
const AuctionList = lazy(() => import('./pages/AuctionList'));
const AuctionCreate = lazy(() => import('./pages/AuctionCreate/index')); // Refactored per May 2025 guidelines
const AuctionDetail = lazy(() => import('./pages/AuctionDetail'));
const AuctionDashboard = lazy(() => import('./pages/AuctionDashboard'));
const AuctionHelp = lazy(() => import('./pages/AuctionHelp'));

// Agreement-related pages
const AgreementView = lazy(() => import('./pages/AgreementView'));
const AgreementSign = lazy(() => import('./pages/AgreementSign'));
const AgreementManagement = lazy(() => import('./pages/AgreementManagement'));

// Verification center
const VerificationCenter = lazy(() => import('./pages/VerificationCenter'));

// Error demo
const ErrorDemo = lazy(() => import('./components/Error/ErrorDemo'));

// File Upload Demo
const FileUploadDemo = lazy(() => import('./pages/FileUploadDemo'));

// Visualization pages
const VisualizationDemoPage = lazy(() => import('./pages/VisualizationDemo'));
const ComprehensiveVisualizationDemoPage = lazy(() => import('./pages/demo/VisualizationDemoPage'));
const VisualizationPage = lazy(() => import('./pages/VisualizationPage'));
const PerformanceDashboardPage = lazy(() => import('./pages/PerformanceDashboard'));

// Rent Planner
const RentPlannerPage = lazy(() => import('./pages/RentPlannerPage'));

// Design System
const DesignSystem = lazy(() => import('./pages/DesignSystem'));
const ResponsiveDesignSystem = lazy(() => import('./pages/ResponsiveDesignSystem/index'));
// Note: ResponsiveDesignSystem has been refactored according to May 2025 code optimization guidelines

// Business pages
const BusinessDashboard = lazy(() => import('./pages/Business/BusinessDashboard'));
const BusinessCreate = lazy(() => import('./pages/Business/BusinessCreate'));
const BusinessSelect = lazy(() => import('./pages/Business/BusinessSelect'));
const BusinessMembers = lazy(() => import('./pages/Business/BusinessMembers'));
const BusinessSettings = lazy(() => import('./pages/Business/BusinessSettings'));

// Test pages (only for development and testing)
const TestServices = lazy(() => import('./pages/TestServices'));

// Auth pages
const Unauthorized = lazy(() => import('./pages/Unauthorized'));

// Loading component
const LoadingFallback = () => (
  <div className="flex items-center justify-center h-screen">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
  </div>
);

function App() {
  // Function to handle errors in the ErrorBoundary
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    // In a production app, you might want to send this to an error tracking service
    console.error('Error caught by root ErrorBoundary:', error, errorInfo);
  };

  // Start tracking web vitals when the app loads
  React.useEffect(() => {
    trackWebVitals();
  }, []);

  return (
    <ErrorBoundary
      fallback={<ErrorFallback />}
      onError={handleError}
    >
      <GoogleOAuthProvider clientId={import.meta.env.VITE_GOOGLE_CLIENT_ID || ''}>
        <HelmetProvider>
          {/* Add resource hints for performance optimization */}
          <ResourceHints
            preconnect={[
              'https://fonts.googleapis.com',
              'https://fonts.gstatic.com',
              'https://accounts.google.com'
            ]}
            preloadFonts={[
              { href: 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap', crossOrigin: 'anonymous' }
            ]}
          />
          <BrowserRouter>
            <AuthProvider>
              <ToastProvider>
                {/* Use a separate ErrorBoundary for the routes to prevent the entire app from crashing */}
                <ErrorBoundary
                  fallback={<ErrorFallback />}
                  onError={(error, errorInfo) => {
                    console.error('Error caught by routes ErrorBoundary:', error, errorInfo);
                  }}
                >
                <Suspense fallback={<LoadingFallback />}>
                  <Routes>
                    <Route path="/login" element={<Layout><Login /></Layout>} />
                    <Route path="/register" element={<Layout><Register /></Layout>} />
                    <Route path="/profile" element={<Layout><ProtectedRoute><ProfilePage /></ProtectedRoute></Layout>} />
                    <Route path="/items" element={<Layout><ProtectedRoute><ItemList /></ProtectedRoute></Layout>} />
                    <Route path="/items/new" element={<Layout><ProtectedRoute><ItemForm /></ProtectedRoute></Layout>} />
                    <Route path="/items/:itemId" element={<Layout><ItemDetails /></Layout>} />
                    <Route path="/items/:itemId/edit" element={<Layout><ProtectedRoute><ItemForm /></ProtectedRoute></Layout>} />
                    <Route path="/search" element={<Layout><SearchPage /></Layout>} />
                    <Route path="/search/results" element={<Layout><SearchResults /></Layout>} />
                    <Route path="/booking/:itemId" element={<Layout><ProtectedRoute><BookingForm /></ProtectedRoute></Layout>} />
                    <Route path="/payment/:bookingId" element={<Layout><ProtectedRoute><Payment /></ProtectedRoute></Layout>} />
                    <Route path="/report" element={<Layout><ProtectedRoute><ReportForm /></ProtectedRoute></Layout>} />
                    <Route path="/messages" element={<Layout><ProtectedRoute><Messages /></ProtectedRoute></Layout>} />
                    <Route path="/notifications" element={<Layout><ProtectedRoute><Notifications /></ProtectedRoute></Layout>} />
                    <Route path="/analytics" element={<Layout><RoleBasedRoute roles={[UserRole.ADMIN, UserRole.MODERATOR]}><AnalyticsDashboard /></RoleBasedRoute></Layout>} />

                    {/* Auction routes */}
                    <Route path="/auctions" element={<Layout><ProtectedRoute><AuctionList /></ProtectedRoute></Layout>} />
                    <Route path="/auctions/create" element={<Layout><ProtectedRoute><AuctionCreate /></ProtectedRoute></Layout>} />
                    <Route path="/auctions/dashboard" element={<Layout><ProtectedRoute><AuctionDashboard /></ProtectedRoute></Layout>} />
                    <Route path="/auctions/help" element={<Layout><AuctionHelp /></Layout>} />
                    <Route path="/auctions/:auctionId" element={<Layout><ProtectedRoute><AuctionDetail /></ProtectedRoute></Layout>} />

                    {/* Agreement routes */}
                    <Route path="/agreements" element={<Layout><ProtectedRoute><AgreementManagement /></ProtectedRoute></Layout>} />
                    <Route path="/agreements/:agreementId" element={<Layout><ProtectedRoute><AgreementView /></ProtectedRoute></Layout>} />
                    <Route path="/agreements/:agreementId/sign" element={<Layout><ProtectedRoute><AgreementSign /></ProtectedRoute></Layout>} />

                    {/* Verification route */}
                    <Route path="/verification" element={<Layout><ProtectedRoute><VerificationCenter /></ProtectedRoute></Layout>} />

                    {/* Error demo route */}
                    <Route path="/error-demo" element={<Layout><ErrorDemo /></Layout>} />

                    {/* File Upload Demo route */}
                    <Route path="/file-upload-demo" element={<Layout><FileUploadDemo /></Layout>} />

                    {/* Visualization routes */}
                    <Route path="/visualization-demo" element={<Layout><VisualizationDemoPage /></Layout>} />
                    <Route path="/visualization-demo/comprehensive" element={<Layout><ComprehensiveVisualizationDemoPage /></Layout>} />
                    <Route path="/visualization" element={<Layout><VisualizationPage /></Layout>} />
                    <Route path="/visualization/preferences" element={<Layout><ProtectedRoute><VisualizationPage /></ProtectedRoute></Layout>} />
                    <Route path="/visualization/relationships" element={<Layout><ProtectedRoute><VisualizationPage /></ProtectedRoute></Layout>} />
                    <Route path="/visualization/recommendations" element={<Layout><ProtectedRoute><VisualizationPage /></ProtectedRoute></Layout>} />
                    <Route path="/visualization/dashboard" element={<Layout><ProtectedRoute><VisualizationPage /></ProtectedRoute></Layout>} />
                    <Route path="/performance-dashboard" element={<Layout><RoleBasedRoute roles={[UserRole.ADMIN, UserRole.DEVELOPER]}><PerformanceDashboardPage /></RoleBasedRoute></Layout>} />

                    {/* Rent Planner route */}
                    <Route path="/rent-planner" element={<Layout><RentPlannerPage /></Layout>} />

                    {/* Design System routes */}
                    <Route path="/design-system" element={<Layout><DesignSystem /></Layout>} />
                    <Route path="/responsive-design-system" element={<Layout><ResponsiveDesignSystem /></Layout>} />

                    {/* Business routes */}
                    <Route path="/business/dashboard" element={<Layout><ProtectedRoute><BusinessDashboard /></ProtectedRoute></Layout>} />
                    <Route path="/business/create" element={<Layout><ProtectedRoute><BusinessCreate /></ProtectedRoute></Layout>} />
                    <Route path="/business/select" element={<Layout><ProtectedRoute><BusinessSelect /></ProtectedRoute></Layout>} />
                    <Route path="/business/members" element={<Layout><ProtectedRoute><BusinessMembers /></ProtectedRoute></Layout>} />
                    <Route path="/business/settings" element={<Layout><ProtectedRoute><BusinessSettings /></ProtectedRoute></Layout>} />

                    {/* Test routes (only for development and testing) */}
                    <Route path="/test-services" element={<Layout><TestServices /></Layout>} />

                    {/* Auth routes */}
                    <Route path="/unauthorized" element={<Layout><Unauthorized /></Layout>} />
                    <Route path="/auth/apple/callback" element={<Layout><Login /></Layout>} />

                    {/* Information pages */}
                    <Route path="/how-it-works" element={<Layout><HowItWorks /></Layout>} />
                    <Route path="/contact" element={<Layout><Contact /></Layout>} />
                    <Route path="/financial-faq" element={<Layout><FinancialFAQ /></Layout>} />
                    <Route path="/guidelines" element={<Layout><Guidelines /></Layout>} />
                    <Route path="/help" element={<Layout><HelpCenter /></Layout>} />
                    <Route path="/privacy" element={<Layout><Privacy /></Layout>} />
                    <Route path="/rent-to-buy" element={<Layout><RentToBuy /></Layout>} />
                    <Route path="/safety" element={<Layout><SafetyCenter /></Layout>} />
                    <Route path="/sitemap" element={<Layout><Sitemap /></Layout>} />
                    <Route path="/terms" element={<Layout><Terms /></Layout>} />

                    {/* Home page with its own ErrorBoundary */}
                    <Route path="/" element={
                      <Layout>
                        <ErrorBoundary fallback={<ErrorFallback />}>
                          <Home />
                        </ErrorBoundary>
                      </Layout>
                    } />

                    {/* Add a 404 Not Found route with better styling */}
                    <Route path="*" element={
                      <Layout>
                        <div className="max-w-md mx-auto my-12 p-6 bg-white rounded-lg shadow-md text-center">
                          <h1 className="text-3xl font-bold text-gray-800 mb-4">404</h1>
                          <h2 className="text-xl font-semibold text-gray-700 mb-4">Page Not Found</h2>
                          <p className="text-gray-600 mb-6">The page you are looking for doesn't exist or has been moved.</p>
                          <a href="/" className="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors">
                            Go to Home
                          </a>
                        </div>
                      </Layout>
                    } />
                  </Routes>
                </Suspense>
              </ErrorBoundary>
              </ToastProvider>
            </AuthProvider>
          </BrowserRouter>
        </HelmetProvider>
      </GoogleOAuthProvider>
    </ErrorBoundary>
  );
}

export default App;
