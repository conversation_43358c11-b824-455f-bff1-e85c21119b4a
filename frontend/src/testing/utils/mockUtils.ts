// Common mocks for testing
export const mockUseIsMobile = (isMobile: boolean = false) => {
  jest.mock('@/hooks/useMediaQuery', () => ({
    useIsMobile: () => isMobile,
  }));
};

export const mockAuthContext = (authState: any = {}) => {
  jest.mock('@/contexts/AuthContext', () => ({
    useAuth: () => ({
      user: null,
      isAuthenticated: false,
      login: jest.fn(),
      logout: jest.fn(),
      ...authState,
    }),
  }));
};

export const mockToastContext = () => {
  jest.mock('@/contexts/ToastContext', () => ({
    useToast: () => ({
      showToast: jest.fn(),
      hideToast: jest.fn(),
    }),
  }));
};

export const mockApiService = () => {
  jest.mock('@/services/apiClient', () => ({
    apiClient: {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
    },
  }));
};