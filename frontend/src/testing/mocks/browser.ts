/**
 * MSW Browser Setup for Development and E2E Testing
 * Updated for MSW v2.x (May 2025)
 */

import { setupWorker } from 'msw/browser';
import { handlers } from './handlers';

// Setup MSW worker for browser environment
export const worker = setupWorker(...handlers);

// Helper functions for browser testing
export const startMocking = async () => {
  if (typeof window !== 'undefined') {
    await worker.start({
      onUnhandledRequest: 'warn',
    });
  }
};

export const stopMocking = () => {
  if (typeof window !== 'undefined') {
    worker.stop();
  }
};

export const resetHandlers = () => {
  worker.resetHandlers();
};

export const useHandlers = (...newHandlers: any[]) => {
  worker.use(...newHandlers);
};

// Export for convenience
export { handlers };
