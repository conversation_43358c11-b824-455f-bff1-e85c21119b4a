/**
 * MSW Server Setup for Node.js (Jest Tests)
 * Updated for MSW v2.x (May 2025)
 */

import { setupServer } from 'msw/node';
import { handlers } from './handlers';

// Setup MSW server with our handlers
export const server = setupServer(...handlers);

// Helper functions for tests
export const resetHandlers = () => {
  server.resetHandlers();
};

export const useHandlers = (...newHandlers: any[]) => {
  server.use(...newHandlers);
};

// Export for convenience
export { handlers };
