/**
 * MSW (Mock Service Worker) API Handlers
 * Updated for MSW v2.x (May 2025) - using 'http' instead of deprecated 'rest'
 */

import { http, HttpResponse } from 'msw';

// Mock data
const mockUsers = [
  {
    id: '1',
    email: '<EMAIL>',
    name: 'Test User',
    verified: true,
  },
];

const mockItems = [
  {
    id: '1',
    title: 'Test Item 1',
    description: 'A test item for rental',
    price: 25.00,
    category: 'electronics',
    owner_id: '1',
    location: 'Test City',
    availability: true,
  },
  {
    id: '2',
    title: 'Test Item 2',
    description: 'Another test item',
    price: 15.00,
    category: 'furniture',
    owner_id: '1',
    location: 'Test City',
    availability: true,
  },
];

const mockRecommendations = [
  {
    id: '1',
    item_id: '1',
    score: 0.95,
    reason: 'Based on your previous rentals',
  },
  {
    id: '2',
    item_id: '2',
    score: 0.87,
    reason: 'Popular in your area',
  },
];

const mockAuctions = [
  {
    id: '1',
    item_id: '1',
    starting_price: 20.00,
    current_bid: 25.00,
    end_time: '2025-06-01T12:00:00Z',
    status: 'active',
  },
];

export const handlers = [
  // Authentication endpoints
  http.post('/api/v1/auth/register', async ({ request }) => {
    const body = await request.json() as any;
    return HttpResponse.json({
      user: {
        id: '1',
        email: body.email,
        name: body.name,
        verified: false,
      },
      access_token: 'mock-access-token',
      refresh_token: 'mock-refresh-token',
    }, { status: 201 });
  }),

  http.post('/api/v1/auth/login', async ({ request }) => {
    const body = await request.json() as any;
    return HttpResponse.json({
      user: mockUsers[0],
      access_token: 'mock-access-token',
      refresh_token: 'mock-refresh-token',
    });
  }),

  http.post('/api/v1/auth/refresh', () => {
    return HttpResponse.json({
      access_token: 'new-mock-access-token',
    });
  }),

  // User endpoints
  http.get('/api/v1/users/me', () => {
    return HttpResponse.json(mockUsers[0]);
  }),

  http.get('/api/v1/users/:id', ({ params }) => {
    const user = mockUsers.find(u => u.id === params.id);
    if (!user) {
      return HttpResponse.json({ error: 'User not found' }, { status: 404 });
    }
    return HttpResponse.json(user);
  }),

  // Items endpoints
  http.get('/api/v1/items', ({ request }) => {
    const url = new URL(request.url);
    const category = url.searchParams.get('category');
    const search = url.searchParams.get('search');
    
    let filteredItems = mockItems;
    
    if (category) {
      filteredItems = filteredItems.filter(item => item.category === category);
    }
    
    if (search) {
      filteredItems = filteredItems.filter(item => 
        item.title.toLowerCase().includes(search.toLowerCase()) ||
        item.description.toLowerCase().includes(search.toLowerCase())
      );
    }
    
    return HttpResponse.json({
      items: filteredItems,
      total: filteredItems.length,
      page: 1,
      per_page: 10,
    });
  }),

  http.get('/api/v1/items/:id', ({ params }) => {
    const item = mockItems.find(i => i.id === params.id);
    if (!item) {
      return HttpResponse.json({ error: 'Item not found' }, { status: 404 });
    }
    return HttpResponse.json(item);
  }),

  http.post('/api/v1/items', async ({ request }) => {
    const body = await request.json() as any;
    const newItem = {
      id: String(mockItems.length + 1),
      ...body,
      owner_id: '1',
      availability: true,
    };
    mockItems.push(newItem);
    return HttpResponse.json(newItem, { status: 201 });
  }),

  // Recommendations endpoints
  http.get('/api/v1/recommendations/personalized', ({ request }) => {
    const url = new URL(request.url);
    const userId = url.searchParams.get('user_id');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    
    return HttpResponse.json(mockRecommendations.slice(0, limit));
  }),

  http.get('/api/v1/recommendations/similar/:itemId', ({ params }) => {
    return HttpResponse.json(mockRecommendations);
  }),

  // Visualization endpoints
  http.get('/api/v1/users/:userId/preferences/visualization', () => {
    return HttpResponse.json([
      { preferenceType: 'liked', weight: 0.8, count: 10 },
      { preferenceType: 'viewed', weight: 0.5, count: 20 },
      { preferenceType: 'searched', weight: 0.3, count: 15 },
    ]);
  }),

  http.get('/api/v1/users/:userId/embeddings/visualization', () => {
    return HttpResponse.json([
      { id: '1', x: 0.1, y: 0.2, category: 'electronics', name: 'Item 1' },
      { id: '2', x: 0.3, y: 0.4, category: 'furniture', name: 'Item 2' },
      { id: '3', x: 0.5, y: 0.6, category: 'electronics', name: 'Item 3' },
    ]);
  }),

  // Auction endpoints
  http.get('/api/v1/auctions', () => {
    return HttpResponse.json({
      auctions: mockAuctions,
      total: mockAuctions.length,
    });
  }),

  http.get('/api/v1/auctions/:id', ({ params }) => {
    const auction = mockAuctions.find(a => a.id === params.id);
    if (!auction) {
      return HttpResponse.json({ error: 'Auction not found' }, { status: 404 });
    }
    return HttpResponse.json(auction);
  }),

  http.post('/api/v1/auctions/:id/bids', async ({ params, request }) => {
    const body = await request.json() as any;
    return HttpResponse.json({
      id: '1',
      auction_id: params.id,
      bidder_id: '1',
      amount: body.amount,
      timestamp: new Date().toISOString(),
    }, { status: 201 });
  }),

  // Health check
  http.get('/health', () => {
    return HttpResponse.json({ status: 'healthy' });
  }),

  // Catch-all for unhandled requests
  http.all('*', ({ request }) => {
    console.warn(`Unhandled ${request.method} request to ${request.url}`);
    return HttpResponse.json(
      { error: `Unhandled ${request.method} request to ${request.url}` },
      { status: 404 }
    );
  }),
];
