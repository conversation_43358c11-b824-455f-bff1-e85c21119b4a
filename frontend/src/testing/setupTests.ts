// Import Jest DOM matchers
import '@testing-library/jest-dom';

// Import custom matchers
import './matchers/visualizationMatchers';

// Polyfill for TextEncoder and TextDecoder
class TextEncoderPolyfill {
  encode(text: string): Uint8Array {
    const encoded = new Uint8Array(text.length);
    for (let i = 0; i < text.length; i++) {
      encoded[i] = text.charCodeAt(i);
    }
    return encoded;
  }
}

class TextDecoderPolyfill {
  decode(buffer: Uint8Array): string {
    return String.fromCharCode.apply(null, Array.from(buffer));
  }
}

global.TextEncoder = TextEncoderPolyfill as any;
global.TextDecoder = TextDecoderPolyfill as any;

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // Deprecated
    removeListener: jest.fn(), // Deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Global mock for useMediaQuery hooks
jest.mock('../hooks/useMediaQuery', () => ({
  __esModule: true,
  default: jest.fn().mockReturnValue(false),
  useMediaQuery: jest.fn().mockReturnValue(false),
  useIsMobile: jest.fn().mockReturnValue(false),
  useIsTablet: jest.fn().mockReturnValue(false),
  useIsDesktop: jest.fn().mockReturnValue(true),
  useIsDarkMode: jest.fn().mockReturnValue(false),
}));

// Global mock for useToast hook
jest.mock('../hooks/useToast', () => ({
  __esModule: true,
  useToast: jest.fn().mockReturnValue({
    showToast: jest.fn(),
    hideToast: jest.fn(),
    toasts: [],
  }),
}));

// Global mock for useAuth hook
jest.mock('../contexts/AuthContext', () => ({
  __esModule: true,
  useAuth: jest.fn().mockReturnValue({
    user: {
      id: 'test-user-id',
      email: '<EMAIL>',
      name: 'Test User',
      role: 'user',
      isVerified: true,
      createdAt: '2023-01-01T00:00:00Z',
    },
    login: jest.fn(),
    logout: jest.fn(),
    register: jest.fn(),
    isLoading: false,
    error: null,
    isAuthenticated: true,
    currentBusiness: {
      id: 'test-business-id',
      name: 'Test Business',
      description: 'Test Description',
      industry: 'technology',
      size: 'small',
      website: 'https://test.com',
      email: '<EMAIL>',
      phone_number: '+1234567890',
      tier: 'professional',
      is_verified: true,
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
      owner_id: 'test-user-id',
      member_count: 5,
      listing_count: 10,
      rental_count: 20,
      is_active: true,
      subscription_status: 'active',
      features: {
        max_members: 20,
        max_listings: 200,
        bulk_upload: true,
        analytics: true,
        priority_support: true,
        custom_branding: false,
        api_access: false,
        dedicated_account_manager: false,
        insurance_discount: 5,
        transaction_fee_discount: 10
      }
    },
    isBusinessAccount: true,
    switchToBusiness: jest.fn(),
    switchToPersonal: jest.fn(),
    hasBusinessRole: jest.fn().mockReturnValue(true),
    hasRole: jest.fn().mockReturnValue(true),
    updateUser: jest.fn(),
  }),
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock window.ResizeObserver
class ResizeObserverMock {
  observe = jest.fn();
  unobserve = jest.fn();
  disconnect = jest.fn();
}

Object.defineProperty(window, 'ResizeObserver', {
  writable: true,
  value: ResizeObserverMock,
});

// Mock window.IntersectionObserver
class IntersectionObserverMock {
  constructor(callback: IntersectionObserverCallback) {
    this.callback = callback;
  }

  callback: IntersectionObserverCallback;
  observe = jest.fn();
  unobserve = jest.fn();
  disconnect = jest.fn();

  // Helper method to simulate intersection
  simulateIntersection(entries: IntersectionObserverEntry[]) {
    this.callback(entries, this);
  }
}

Object.defineProperty(window, 'IntersectionObserver', {
  writable: true,
  value: IntersectionObserverMock,
});

// Mock console methods to catch warnings and errors
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
const originalConsoleLog = console.log;

beforeAll(() => {
  console.error = jest.fn((...args) => {
    // Check if this is a React warning about act()
    const message = args.join(' ');
    if (
      message.includes('Warning: An update to') &&
      message.includes('inside a test was not wrapped in act')
    ) {
      // Ignore act warnings
      return;
    }

    // Pass through to original console.error
    originalConsoleError(...args);
  });

  console.warn = jest.fn((...args) => {
    // Check if this is a React warning about deprecated features
    const message = args.join(' ');
    if (
      message.includes('Warning: ReactDOM.render is no longer supported') ||
      message.includes('Warning: findDOMNode is deprecated')
    ) {
      // Ignore deprecation warnings
      return;
    }

    // Pass through to original console.warn
    originalConsoleWarn(...args);
  });

  // Silence console.log in tests
  console.log = jest.fn();
});

afterAll(() => {
  // Restore console methods
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
  console.log = originalConsoleLog;
});

// Set up fake timers
jest.useFakeTimers();

// Clean up after each test
afterEach(() => {
  // Clear all mocks
  jest.clearAllMocks();

  // Reset fake timers
  jest.runOnlyPendingTimers();
  jest.clearAllTimers();
});

// Mock import.meta.env for Vite environment variables in Jest
// This is needed because Jest doesn't support import.meta
if (typeof global.import === 'undefined') {
  // @ts-ignore - intentionally adding this to the global scope for tests
  global.import = {
    meta: {
      env: {
        MODE: 'test',
        DEV: true,
        PROD: false,
        SSR: false,
        BASE_URL: '/',
        VITE_API_URL: 'http://localhost:8000',
      },
    },
  };
}

// API Mocking Setup for Integration Tests
// Using fetch mock for now - will upgrade to MSW v2.x once dependencies are resolved

// Mock fetch globally for tests
global.fetch = jest.fn();

// Helper function to setup API mocks
global.setupApiMocks = (mocks: Record<string, any>) => {
  (global.fetch as jest.Mock).mockImplementation((url: string, options?: any) => {
    const method = options?.method || 'GET';
    const key = `${method} ${url}`;

    if (mocks[key]) {
      return Promise.resolve({
        ok: true,
        status: 200,
        json: () => Promise.resolve(mocks[key]),
        text: () => Promise.resolve(JSON.stringify(mocks[key])),
      });
    }

    // Default response for unmocked requests
    return Promise.resolve({
      ok: false,
      status: 404,
      json: () => Promise.resolve({ error: 'Not found' }),
      text: () => Promise.resolve('{"error":"Not found"}'),
    });
  });
};

// Reset mocks after each test
afterEach(() => {
  jest.clearAllMocks();
});
