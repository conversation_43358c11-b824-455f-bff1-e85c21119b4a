import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import tailwindcss from '@tailwindcss/vite';
import { loadEnv } from 'vite';
import { visualizer } from 'rollup-plugin-visualizer';
import { VitePWA } from 'vite-plugin-pwa';
import viteCompression from 'vite-plugin-compression';
import viteImagemin from 'vite-plugin-imagemin';
import { splitVendorChunkPlugin } from 'vite';
import path from 'path';

// https://vitejs.dev/config/

export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current working directory.
  // Only load env variables with the VITE_ prefix for security
  const env = loadEnv(mode, process.cwd(), 'VITE_');
  const isProd = mode === 'production';

  return {
    plugins: [
      react(),
      tailwindcss(),
      splitVendorChunkPlugin(), // Split vendor chunks for better caching

      // Bundle visualization in production
      isProd && visualizer({
        filename: 'dist/stats.html',
        gzipSize: true,
        brotliSize: true,
        open: false,
      }),

      // PWA support for offline capabilities
      VitePWA({
        registerType: 'autoUpdate',
        includeAssets: ['favicon.ico', 'robots.txt', 'apple-touch-icon.png', 'llms.txt'],
        manifest: {
          name: 'RentUp',
          short_name: 'RentUp',
          description: 'The Community Marketplace for Endless Shared Possibilities',
          theme_color: '#ffffff',
          start_url: '/',
          display: 'standalone',
          background_color: '#ffffff',
          orientation: 'portrait-primary',
          categories: ['shopping', 'lifestyle', 'social'],
          icons: [
            {
              src: 'pwa-192x192.png',
              sizes: '192x192',
              type: 'image/png',
            },
            {
              src: 'pwa-512x512.png',
              sizes: '512x512',
              type: 'image/png',
              purpose: 'any maskable',
            },
          ],
          screenshots: [
            {
              src: '/screenshots/home-screen.png',
              sizes: '1280x720',
              type: 'image/png',
              platform: 'wide',
              label: 'Home Screen'
            },
            {
              src: '/screenshots/mobile-home.png',
              sizes: '750x1334',
              type: 'image/png',
              platform: 'narrow',
              label: 'Mobile Home Screen'
            }
          ],
        },
        workbox: {
          globPatterns: ['**/*.{js,css,html,ico,png,svg,webp,jpg,jpeg,json,woff,woff2}'],
          runtimeCaching: [
            {
              urlPattern: /^https:\/\/fonts\.googleapis\.com\/.*/i,
              handler: 'CacheFirst',
              options: {
                cacheName: 'google-fonts-cache',
                expiration: {
                  maxEntries: 10,
                  maxAgeSeconds: 60 * 60 * 24 * 365, // 1 year
                },
                cacheableResponse: {
                  statuses: [0, 200],
                },
              },
            },
            {
              urlPattern: /^https:\/\/fonts\.gstatic\.com\/.*/i,
              handler: 'CacheFirst',
              options: {
                cacheName: 'gstatic-fonts-cache',
                expiration: {
                  maxEntries: 10,
                  maxAgeSeconds: 60 * 60 * 24 * 365, // 1 year
                },
                cacheableResponse: {
                  statuses: [0, 200],
                },
              },
            },
            {
              urlPattern: /\.(?:png|jpg|jpeg|svg|gif|webp|avif)$/,
              handler: 'CacheFirst',
              options: {
                cacheName: 'images-cache',
                expiration: {
                  maxEntries: 100,
                  maxAgeSeconds: 60 * 60 * 24 * 30, // 30 days
                },
              },
            },
            {
              urlPattern: /^https:\/\/api\.rentup\.com\/api\/v1\/static\/.*/i,
              handler: 'CacheFirst',
              options: {
                cacheName: 'api-static-cache',
                expiration: {
                  maxEntries: 50,
                  maxAgeSeconds: 60 * 60 * 24 * 7, // 7 days
                },
                cacheableResponse: {
                  statuses: [0, 200],
                },
              },
            },
            {
              urlPattern: /^https:\/\/api\.rentup\.com\/api\/v1\/.*/i,
              handler: 'NetworkFirst',
              options: {
                cacheName: 'api-dynamic-cache',
                expiration: {
                  maxEntries: 100,
                  maxAgeSeconds: 60 * 60 * 24, // 24 hours
                },
                cacheableResponse: {
                  statuses: [0, 200],
                },
                networkTimeoutSeconds: 10, // Fallback to cache if network takes more than 10 seconds
              },
            },
            {
              urlPattern: /^https:\/\/api\.rentup\.com\/api\/v1\/recommendations\/.*/i,
              handler: 'StaleWhileRevalidate',
              options: {
                cacheName: 'api-recommendations-cache',
                expiration: {
                  maxEntries: 50,
                  maxAgeSeconds: 60 * 60 * 3, // 3 hours
                },
                cacheableResponse: {
                  statuses: [0, 200],
                },
              },
            },
          ],
          skipWaiting: true,
          clientsClaim: true,
        },
      }),

      // Compression for production builds
      isProd && viteCompression({
        algorithm: 'brotliCompress',
        threshold: 10240, // Only compress files > 10kb
      }),

      // Image optimization for production builds
      isProd && viteImagemin({
        gifsicle: {
          optimizationLevel: 7,
          interlaced: false,
        },
        optipng: {
          optimizationLevel: 7,
        },
        mozjpeg: {
          quality: 80,
        },
        pngquant: {
          quality: [0.8, 0.9],
          speed: 4,
        },
        svgo: {
          plugins: [
            {
              name: 'removeViewBox',
              active: false,
            },
            {
              name: 'removeEmptyAttrs',
              active: false,
            },
          ],
        },
        webp: {
          quality: 80,
        },
      }),
    ],

    // Resolve aliases for cleaner imports
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@components': path.resolve(__dirname, './src/components'),
        '@pages': path.resolve(__dirname, './src/pages'),
        '@hooks': path.resolve(__dirname, './src/hooks'),
        '@services': path.resolve(__dirname, './src/services'),
        '@utils': path.resolve(__dirname, './src/utils'),
        '@contexts': path.resolve(__dirname, './src/contexts'),
        '@assets': path.resolve(__dirname, './src/assets'),
      },
    },

    // Only expose specific environment variables
    define: {
      // Only expose environment variables that start with VITE_
      // This is safer than exposing all environment variables
      'process.env.VITE_GOOGLE_CLIENT_ID': JSON.stringify(env.VITE_GOOGLE_CLIENT_ID),
      'process.env.VITE_FACEBOOK_APP_ID': JSON.stringify(env.VITE_FACEBOOK_APP_ID),
      'process.env.VITE_API_URL': JSON.stringify(env.VITE_API_URL),
      'process.env.MODE': JSON.stringify(mode)
    },

    // Enhanced security headers (relaxed for development)
    server: {
      headers: {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'Permissions-Policy': 'camera=(), microphone=(), geolocation=(self), interest-cohort=()',
        // Relaxed CSP for development - allows inline scripts and localhost connections
        'Content-Security-Policy': isProd
          ? "default-src 'self'; script-src 'self' https://accounts.google.com https://apis.google.com; style-src 'self' https://fonts.googleapis.com 'unsafe-inline'; font-src 'self' https://fonts.gstatic.com https://fonts.googleapis.com; img-src 'self' data: https:; connect-src 'self' https://api.rentup.com; frame-src https://accounts.google.com;"
          : "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://accounts.google.com https://apis.google.com; style-src 'self' https://fonts.googleapis.com 'unsafe-inline'; font-src 'self' https://fonts.gstatic.com https://fonts.googleapis.com; img-src 'self' data: https:; connect-src 'self' ws://localhost:* http://localhost:* https://api.rentup.com; frame-src https://accounts.google.com;",
        '.js': 'application/javascript; charset=utf-8',
        '.css': 'text/css; charset=utf-8',
        '.ts': 'text/typescript; charset=utf-8',
        '.tsx': 'text/typescript; charset=utf-8',
        '.json': 'application/json; charset=utf-8',
        '.html': 'text/html; charset=utf-8'
      },
      hmr: {
        port: 5173,
        host: 'localhost'
      },
      fs: {
        allow: ['../', '../.env']
      }
    },

    // Enhanced build options for better performance
    build: {
      cssCodeSplit: true,
      minify: 'terser', // Use Terser for better minification
      terserOptions: {
        compress: {
          drop_console: isProd, // Remove console.log in production
          drop_debugger: isProd, // Remove debugger statements in production
        },
      },
      rollupOptions: {
        output: {
          // Chunk splitting strategy
          manualChunks: (id) => {
            // Create separate chunks for large dependencies
            if (id.includes('node_modules')) {
              if (id.includes('react') || id.includes('react-dom')) {
                return 'vendor-react';
              }
              if (id.includes('framer-motion')) {
                return 'vendor-framer-motion';
              }
              if (id.includes('@mui')) {
                return 'vendor-mui';
              }
              if (id.includes('axios') || id.includes('query')) {
                return 'vendor-data-fetching';
              }
              if (id.includes('tailwind') || id.includes('headless')) {
                return 'vendor-ui';
              }
              if (id.includes('zod') || id.includes('hook-form')) {
                return 'vendor-forms';
              }
              if (id.includes('date-fns')) {
                return 'vendor-date';
              }
              if (id.includes('zustand')) {
                return 'vendor-state';
              }
              return 'vendor'; // All other dependencies
            }

            // Group by feature for app code
            if (id.includes('/components/')) {
              if (id.includes('/Visualization/')) {
                return 'feature-visualization';
              }
              if (id.includes('/Auth/')) {
                return 'feature-auth';
              }
              if (id.includes('/Layout/')) {
                return 'feature-layout';
              }
              if (id.includes('/UI/')) {
                return 'feature-ui';
              }
              if (id.includes('/Recommendations/')) {
                return 'feature-recommendations';
              }
              if (id.includes('/Search/')) {
                return 'feature-search';
              }
              if (id.includes('/Item/')) {
                return 'feature-item';
              }
              if (id.includes('/Auction/')) {
                return 'feature-auction';
              }
              if (id.includes('/Agreement/')) {
                return 'feature-agreement';
              }
              return 'components';
            }

            if (id.includes('/pages/')) {
              // Split pages by feature
              if (id.includes('Visualization')) {
                return 'page-visualization';
              }
              if (id.includes('Item')) {
                return 'page-item';
              }
              if (id.includes('Search')) {
                return 'page-search';
              }
              if (id.includes('Auction')) {
                return 'page-auction';
              }
              if (id.includes('Agreement')) {
                return 'page-agreement';
              }
              if (id.includes('Login') || id.includes('Register')) {
                return 'page-auth';
              }
              if (id.includes('Home')) {
                return 'page-home';
              }
              return 'pages';
            }

            if (id.includes('/services/')) {
              return 'services';
            }

            if (id.includes('/hooks/')) {
              return 'hooks';
            }

            if (id.includes('/utils/')) {
              if (id.includes('imageOptimization')) {
                return 'utils-image';
              }
              if (id.includes('performanceMonitoring')) {
                return 'utils-performance';
              }
              if (id.includes('serviceWorker')) {
                return 'utils-offline';
              }
              return 'utils';
            }
          },
          entryFileNames: 'assets/[name]-[hash].js',
          chunkFileNames: 'assets/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash].[ext]'
        }
      },
      outDir: 'dist',
      emptyOutDir: true,
      sourcemap: !isProd, // Only generate sourcemaps in development
      // Preload critical chunks
      modulePreload: {
        polyfill: true,
      },
      // Target modern browsers for smaller bundles
      target: 'es2020',
    },

    // Optimize dependencies
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        'framer-motion',
        'axios',
        '@tanstack/react-query',
        'zod',
        'react-hook-form',
        '@hookform/resolvers',
        'zustand',
        'date-fns',
        'class-variance-authority',
      ],
      exclude: ['@vite/client', '@vite/env'],
    },

    // Performance metrics
    esbuild: {
      logOverride: { 'this-is-undefined-in-esm': 'silent' },
      legalComments: 'none',
      drop: isProd ? ['console', 'debugger'] : [],
    },
  };
});
