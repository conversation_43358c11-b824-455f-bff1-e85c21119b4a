{"summary": {"duplicate_files": 2, "similar_components": 3, "files_with_unused_imports": 0, "redundant_configs": 4}, "duplicate_files": {"dd6a359da2856999468a16f2eee1e25b": ["frontend/testResults/organized/playwright/visualization-demo-page.png", "frontend/testResults/organized/playwright/route--visualization-demo.png"], "83bf3345ad2d18df4732381788017981": ["frontend/src/components/admin/AICurationPanel/components/NotificationSnackbar.tsx", "frontend/src/components/auctions/BiddingInterface/components/NotificationSnackbar.tsx"]}, "similar_components": [["frontend/node_modules/@storybook/react/template/cli/ts-4-9/Header.tsx", "frontend/node_modules/@storybook/react/template/cli/ts-3-8/Header.tsx", 1.0], ["frontend/node_modules/@storybook/react/template/cli/ts-4-9/Button.tsx", "frontend/node_modules/@storybook/react/template/cli/ts-3-8/Button.tsx", 1.0], ["frontend/node_modules/react-dropzone/typings/tests/file-dialog.tsx", "frontend/node_modules/react-dropzone/typings/tests/refs.tsx", 0.875]], "unused_imports": {}, "redundant_configs": [{"type": "Jest configuration", "files": ["frontend/jest.config.js", "frontend/node_modules/react-date-range/jest.config.js", "frontend/node_modules/mochawesome-merge/jest.config.js"], "recommendation": "Consolidate Jest configuration files"}, {"type": "Vite configuration", "files": ["frontend/vite.config.ts", "frontend/vite.config.js", "frontend/vite.config.d.ts", "frontend/node_modules/psl/vite.config.js", "frontend/node_modules/@tanstack/react-query/build/query-codemods/vite.config.ts"], "recommendation": "Consolidate Vite configuration files"}, {"type": "TypeScript configuration", "files": ["frontend/tsconfig.app.json", "frontend/tsconfig.node.json", "frontend/tsconfig.json", "frontend/node_modules/es-errors/tsconfig.json", "frontend/node_modules/safe-array-concat/tsconfig.json", "frontend/node_modules/has-tostringtag/tsconfig.json", "frontend/node_modules/es-define-property/tsconfig.json", "frontend/node_modules/is-typed-array/tsconfig.json", "frontend/node_modules/is-date-object/tsconfig.json", "frontend/node_modules/is-map/tsconfig.json", "frontend/node_modules/typed-array-length/tsconfig.json", "frontend/node_modules/workbox-navigation-preload/tsconfig.json", "frontend/node_modules/possible-typed-array-names/tsconfig.json", "frontend/node_modules/call-bound/tsconfig.json", "frontend/node_modules/array-buffer-byte-length/tsconfig.json", "frontend/node_modules/typed-array-buffer/tsconfig.json", "frontend/node_modules/dunder-proto/tsconfig.json", "frontend/node_modules/stop-iteration-iterator/tsconfig.json", "frontend/node_modules/side-channel-weakmap/tsconfig.json", "frontend/node_modules/reusify/tsconfig.json", "frontend/node_modules/is-number-object/tsconfig.json", "frontend/node_modules/es-object-atoms/tsconfig.json", "frontend/node_modules/math-intrinsics/tsconfig.json", "frontend/node_modules/for-each/tsconfig.json", "frontend/node_modules/workbox-core/tsconfig.json", "frontend/node_modules/set-proto/tsconfig.json", "frontend/node_modules/has-bigints/tsconfig.json", "frontend/node_modules/is-finalizationregistry/tsconfig.json", "frontend/node_modules/async-function/tsconfig.json", "frontend/node_modules/i18next-browser-languagedetector/tsconfig.nonEsModuleInterop.json", "frontend/node_modules/set-function-length/tsconfig.json", "frontend/node_modules/which-boxed-primitive/tsconfig.json", "frontend/node_modules/has-symbols/tsconfig.json", "frontend/node_modules/which-builtin-type/tsconfig.json", "frontend/node_modules/workbox-broadcast-update/tsconfig.json", "frontend/node_modules/workbox-background-sync/tsconfig.json", "frontend/node_modules/data-view-buffer/tsconfig.json", "frontend/node_modules/is-data-view/tsconfig.json", "frontend/node_modules/es-shim-unscopables/tsconfig.json", "frontend/node_modules/side-channel-map/tsconfig.json", "frontend/node_modules/which-collection/tsconfig.json", "frontend/node_modules/is-symbol/tsconfig.json", "frontend/node_modules/hasown/tsconfig.json", "frontend/node_modules/express-rate-limit/tsconfig.json", "frontend/node_modules/which-typed-array/tsconfig.json", "frontend/node_modules/side-channel-list/tsconfig.json", "frontend/node_modules/workbox-recipes/tsconfig.json", "frontend/node_modules/is-regex/tsconfig.json", "frontend/node_modules/is-set/tsconfig.json", "frontend/node_modules/is-weakset/tsconfig.json", "frontend/node_modules/workbox-window/tsconfig.json", "frontend/node_modules/typed-array-byte-length/tsconfig.json", "frontend/node_modules/call-bind-apply-helpers/tsconfig.json", "frontend/node_modules/available-typed-arrays/tsconfig.json", "frontend/node_modules/is-boolean-object/tsconfig.json", "frontend/node_modules/is-array-buffer/tsconfig.json", "frontend/node_modules/is-string/tsconfig.json", "frontend/node_modules/es-set-tostringtag/tsconfig.json", "frontend/node_modules/workbox-range-requests/tsconfig.json", "frontend/node_modules/workbox-build/tsconfig.json", "frontend/node_modules/is-weakmap/tsconfig.json", "frontend/node_modules/gopd/tsconfig.json", "frontend/node_modules/is-bigint/tsconfig.json", "frontend/node_modules/workbox-strategies/tsconfig.json", "frontend/node_modules/own-keys/tsconfig.json", "frontend/node_modules/workbox-google-analytics/tsconfig.json", "frontend/node_modules/is-async-function/tsconfig.json", "frontend/node_modules/set-function-name/tsconfig.json", "frontend/node_modules/is-weakref/tsconfig.json", "frontend/node_modules/workbox-precaching/tsconfig.json", "frontend/node_modules/data-view-byte-offset/tsconfig.json", "frontend/node_modules/typed-array-byte-offset/tsconfig.json", "frontend/node_modules/data-view-byte-length/tsconfig.json", "frontend/node_modules/is-shared-array-buffer/tsconfig.json", "frontend/node_modules/has-proto/tsconfig.json", "frontend/node_modules/safe-push-apply/tsconfig.json", "frontend/node_modules/internal-slot/tsconfig.json", "frontend/node_modules/unbox-primitive/tsconfig.json", "frontend/node_modules/workbox-routing/tsconfig.json", "frontend/node_modules/side-channel/tsconfig.json", "frontend/node_modules/is-generator-function/tsconfig.json", "frontend/node_modules/es-to-primitive/tsconfig.json", "frontend/node_modules/is-arguments/tsconfig.json", "frontend/node_modules/workbox-expiration/tsconfig.json", "frontend/node_modules/get-symbol-description/tsconfig.json", "frontend/node_modules/workbox-cacheable-response/tsconfig.json", "frontend/node_modules/safe-regex-test/tsconfig.json", "frontend/node_modules/get-proto/tsconfig.json", "frontend/node_modules/define-data-property/tsconfig.json", "frontend/node_modules/rxjs/tsconfig.json", "frontend/node_modules/workbox-streams/tsconfig.json", "frontend/node_modules/@tanstack/react-query/build/query-codemods/tsconfig.json", "frontend/node_modules/fastq/test/tsconfig.json", "frontend/node_modules/psl/types/tsconfig.json", "frontend/node_modules/@testing-library/jest-dom/types/__tests__/jest-globals/tsconfig.json", "frontend/node_modules/@testing-library/jest-dom/types/__tests__/bun/tsconfig.json", "frontend/node_modules/@testing-library/jest-dom/types/__tests__/vitest/tsconfig.json", "frontend/node_modules/@testing-library/jest-dom/types/__tests__/jest/tsconfig.json", "frontend/node_modules/msw/src/tsconfig.src.json", "frontend/node_modules/msw/src/tsconfig.core.build.json", "frontend/node_modules/msw/src/tsconfig.node.json", "frontend/node_modules/msw/src/tsconfig.node.build.json", "frontend/node_modules/msw/src/browser/tsconfig.browser.json", "frontend/node_modules/msw/src/browser/tsconfig.browser.build.json", "frontend/node_modules/react-dropzone/typings/tests/tsconfig.json", "frontend/node_modules/@bcoe/v8-coverage/tsconfig.json", "frontend/node_modules/@bcoe/v8-coverage/dist/lib/tsconfig.json", "frontend/node_modules/tsconfig-paths/src/__tests__/tsconfig-named.json", "frontend/node_modules/rxjs/src/tsconfig.base.json", "frontend/node_modules/rxjs/src/tsconfig.types.spec.json", "frontend/node_modules/rxjs/src/tsconfig.cjs.spec.json", "frontend/node_modules/rxjs/src/tsconfig.esm5.rollup.json", "frontend/node_modules/rxjs/src/tsconfig.esm.json", "frontend/node_modules/rxjs/src/tsconfig.types.json", "frontend/node_modules/rxjs/src/tsconfig.cjs.json", "frontend/node_modules/rxjs/src/tsconfig.esm5.json"], "recommendation": "Consolidate TypeScript configuration files"}, {"type": "ESLint configuration", "files": ["frontend/eslint.config.js", "frontend/node_modules/chai/eslint.config.js", "frontend/node_modules/fast-uri/eslint.config.js", "frontend/node_modules/reusify/eslint.config.js", "frontend/node_modules/mochawesome-merge/eslint.config.mjs", "frontend/node_modules/@tanstack/react-query/build/query-codemods/eslint.config.js"], "recommendation": "Consolidate ESLint configuration files"}], "recommendations": ["🗑️ Remove 2 groups of duplicate files", "🔄 Refactor 3 pairs of similar components", "⚙️ Consolidate 4 types of redundant configuration files"]}