#!/bin/bash

# Colors for terminal output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Print header
print_header() {
    echo -e "\n${PURPLE}=========================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}=========================================${NC}\n"
}

# Print section
print_section() {
    echo -e "\n${BLUE}$1${NC}"
    echo -e "${BLUE}----------------------------------------${NC}\n"
}

# Run tests
run_tests() {
    print_section "Running $1 tests"
    
    if [ -n "$2" ]; then
        echo -e "${CYAN}Command: $2${NC}\n"
        eval $2
    else
        echo -e "${YELLOW}No command specified${NC}\n"
    fi
    
    if [ $? -eq 0 ]; then
        echo -e "\n${GREEN}$1 tests passed!${NC}\n"
        return 0
    else
        echo -e "\n${RED}$1 tests failed!${NC}\n"
        return 1
    fi
}

# Main function
main() {
    print_header "RentUp Test Runner"
    
    # Create test results directory
    mkdir -p testResults
    
    # Run unit tests
    run_tests "Unit" "python -m pytest tests/unit -v"
    UNIT_RESULT=$?
    
    # Run integration tests
    run_tests "Integration" "python -m pytest tests/integration -v"
    INTEGRATION_RESULT=$?
    
    # Run API tests
    run_tests "API" "python -m pytest tests/api -v"
    API_RESULT=$?
    
    # Run end-to-end tests
    run_tests "End-to-End" "python -m pytest tests/e2e -v"
    E2E_RESULT=$?
    
    # Run Phase 3 tests
    run_tests "Phase 3" "python -m pytest tests/integration/phase3 -v"
    PHASE3_RESULT=$?
    
    # Print summary
    print_header "Test Summary"
    
    if [ $UNIT_RESULT -eq 0 ]; then
        echo -e "${GREEN}Unit Tests: PASSED${NC}"
    else
        echo -e "${RED}Unit Tests: FAILED${NC}"
    fi
    
    if [ $INTEGRATION_RESULT -eq 0 ]; then
        echo -e "${GREEN}Integration Tests: PASSED${NC}"
    else
        echo -e "${RED}Integration Tests: FAILED${NC}"
    fi
    
    if [ $API_RESULT -eq 0 ]; then
        echo -e "${GREEN}API Tests: PASSED${NC}"
    else
        echo -e "${RED}API Tests: FAILED${NC}"
    fi
    
    if [ $E2E_RESULT -eq 0 ]; then
        echo -e "${GREEN}End-to-End Tests: PASSED${NC}"
    else
        echo -e "${RED}End-to-End Tests: FAILED${NC}"
    fi
    
    if [ $PHASE3_RESULT -eq 0 ]; then
        echo -e "${GREEN}Phase 3 Tests: PASSED${NC}"
    else
        echo -e "${RED}Phase 3 Tests: FAILED${NC}"
    fi
    
    # Create test report
    TIMESTAMP=$(date +"%Y-%m-%d_%H-%M-%S")
    REPORT_FILE="testResults/test_report_${TIMESTAMP}.md"
    
    echo "# RentUp Test Report" > $REPORT_FILE
    echo "" >> $REPORT_FILE
    echo "Date: $(date +"%Y-%m-%d %H:%M:%S")" >> $REPORT_FILE
    echo "" >> $REPORT_FILE
    echo "## Summary" >> $REPORT_FILE
    echo "" >> $REPORT_FILE
    
    if [ $UNIT_RESULT -eq 0 ]; then
        echo "- Unit Tests: PASSED" >> $REPORT_FILE
    else
        echo "- Unit Tests: FAILED" >> $REPORT_FILE
    fi
    
    if [ $INTEGRATION_RESULT -eq 0 ]; then
        echo "- Integration Tests: PASSED" >> $REPORT_FILE
    else
        echo "- Integration Tests: FAILED" >> $REPORT_FILE
    fi
    
    if [ $API_RESULT -eq 0 ]; then
        echo "- API Tests: PASSED" >> $REPORT_FILE
    else
        echo "- API Tests: FAILED" >> $REPORT_FILE
    fi
    
    if [ $E2E_RESULT -eq 0 ]; then
        echo "- End-to-End Tests: PASSED" >> $REPORT_FILE
    else
        echo "- End-to-End Tests: FAILED" >> $REPORT_FILE
    fi
    
    if [ $PHASE3_RESULT -eq 0 ]; then
        echo "- Phase 3 Tests: PASSED" >> $REPORT_FILE
    else
        echo "- Phase 3 Tests: FAILED" >> $REPORT_FILE
    fi
    
    echo "" >> $REPORT_FILE
    echo "## Details" >> $REPORT_FILE
    echo "" >> $REPORT_FILE
    echo "See console output for detailed test results." >> $REPORT_FILE
    
    echo -e "\n${GREEN}Test report saved to ${REPORT_FILE}${NC}"
    
    # Return overall result
    if [ $UNIT_RESULT -eq 0 ] && [ $INTEGRATION_RESULT -eq 0 ] && [ $API_RESULT -eq 0 ] && [ $E2E_RESULT -eq 0 ] && [ $PHASE3_RESULT -eq 0 ]; then
        print_header "All tests passed!"
        return 0
    else
        print_header "Some tests failed!"
        return 1
    fi
}

# Run main function
main
