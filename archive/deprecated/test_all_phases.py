#!/usr/bin/env python3
"""
Comprehensive Test Runner for RentUp Platform

This script runs all tests for the RentUp platform, including:
1. Phase 0: Project Setup
2. Phase 1: Frontend Scaffolding
3. Phase 2: Backend Integration
4. Phase 3: Advanced Features

Usage:
    python test_all_phases.py [--phase PHASE] [--component COMPONENT] [--verbose]

Options:
    --phase PHASE         Run tests for a specific phase (0, 1, 2, 3)
    --component COMPONENT Run tests for a specific component
    --verbose             Show detailed test output
"""

import os
import sys
import subprocess
import argparse
import time
from datetime import datetime
from pathlib import Path

# Define colors for terminal output
class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

# Define test categories by phase
PHASE0_TESTS = [
    "backend/app/tests/test_setup.py",
    "frontend/tests/testscripts/frontend/setup.test.js"
]

PHASE1_TESTS = [
    "frontend/tests/testscripts/frontend/home.test.js",
    "frontend/tests/testscripts/frontend/search.test.js",
    "frontend/tests/testscripts/frontend/item-details.test.js",
    "frontend/tests/testscripts/frontend/user-dashboard.test.js",
    "frontend/tests/testscripts/frontend/authentication.test.js"
]

PHASE2_TESTS = [
    "backend/app/tests/test_auth.py",
    "backend/app/tests/test_items.py",
    "backend/app/tests/test_users.py",
    "backend/app/tests/test_bookings.py",
    "backend/app/tests/test_search.py",
    "backend/app/tests/test_uploads.py"
]

PHASE3_TESTS = [
    "backend/app/tests/test_recommendations.py",
    "backend/app/tests/test_auctions.py",
    "backend/app/tests/test_agreements.py",
    "backend/app/tests/test_fraud_detection.py",
    "backend/app/tests/test_visualization.py",
    "frontend/tests/visualization.test.js",
    "frontend/tests/recommendations.test.js"
]

# Define component tests
COMPONENT_TESTS = {
    "auth": ["backend/app/tests/test_auth.py"],
    "items": ["backend/app/tests/test_items.py"],
    "users": ["backend/app/tests/test_users.py"],
    "bookings": ["backend/app/tests/test_bookings.py"],
    "search": ["backend/app/tests/test_search.py"],
    "recommendations": ["backend/app/tests/test_recommendations.py", "frontend/tests/recommendations.test.js"],
    "auctions": ["backend/app/tests/test_auctions.py"],
    "agreements": ["backend/app/tests/test_agreements.py"],
    "fraud": ["backend/app/tests/test_fraud_detection.py"],
    "visualization": ["backend/app/tests/test_visualization.py", "frontend/tests/visualization.test.js"]
}

def print_header(text):
    """Print a formatted header."""
    print(f"\n{Colors.HEADER}{Colors.BOLD}{'=' * 80}{Colors.ENDC}")
    print(f"{Colors.HEADER}{Colors.BOLD}{text.center(80)}{Colors.ENDC}")
    print(f"{Colors.HEADER}{Colors.BOLD}{'=' * 80}{Colors.ENDC}\n")

def print_subheader(text):
    """Print a formatted subheader."""
    print(f"\n{Colors.OKBLUE}{Colors.BOLD}{text}{Colors.ENDC}")
    print(f"{Colors.OKBLUE}{'-' * len(text)}{Colors.ENDC}\n")

def run_command(command, cwd=".", verbose=False):
    """Run a shell command and return success status."""
    print(f"Running command: {' '.join(command)}")

    try:
        if verbose:
            # Run with output displayed
            process = subprocess.Popen(
                command,
                cwd=cwd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1
            )

            # Print output in real-time
            for line in iter(process.stdout.readline, ''):
                print(line, end='')

            process.stdout.close()
            return_code = process.wait()
        else:
            # Run with output captured
            result = subprocess.run(
                command,
                cwd=cwd,
                capture_output=True,
                text=True
            )
            return_code = result.returncode

        if return_code == 0:
            print(f"{Colors.OKGREEN}Command completed successfully{Colors.ENDC}")
            return True
        else:
            print(f"{Colors.FAIL}Command failed with return code {return_code}{Colors.ENDC}")
            if not verbose:
                print(f"{Colors.WARNING}Run with --verbose to see detailed output{Colors.ENDC}")
            return False

    except Exception as e:
        print(f"{Colors.FAIL}Error running command: {e}{Colors.ENDC}")
        return False

def run_pytest(test_path, verbose=False):
    """Run pytest on a specific path."""
    command = ["python", "-m", "pytest", test_path]
    if verbose:
        command.append("-v")

    return run_command(command, verbose=verbose)

def run_frontend_test(test_path, verbose=False):
    """Run frontend test."""
    command = ["npm", "test", "--", test_path]
    return run_command(command, cwd="frontend", verbose=verbose)

def run_phase_tests(phase, verbose=False):
    """Run tests for a specific phase."""
    print_header(f"Running Phase {phase} Tests")

    test_paths = []
    if phase == 0:
        test_paths = PHASE0_TESTS
    elif phase == 1:
        test_paths = PHASE1_TESTS
    elif phase == 2:
        test_paths = PHASE2_TESTS
    elif phase == 3:
        test_paths = PHASE3_TESTS
    else:
        print(f"{Colors.FAIL}Invalid phase: {phase}{Colors.ENDC}")
        return False

    results = {}
    for test_path in test_paths:
        print_subheader(f"Running test: {test_path}")

        if test_path.endswith(".py"):
            success = run_pytest(test_path, verbose)
        elif test_path.endswith(".js"):
            success = run_frontend_test(test_path, verbose)
        else:
            print(f"{Colors.WARNING}Unknown test type: {test_path}{Colors.ENDC}")
            success = False

        results[test_path] = success

    # Print summary
    print_header(f"Phase {phase} Test Summary")
    all_passed = True
    for test_path, success in results.items():
        status = f"{Colors.OKGREEN}PASSED{Colors.ENDC}" if success else f"{Colors.FAIL}FAILED{Colors.ENDC}"
        print(f"{test_path}: {status}")
        if not success:
            all_passed = False

    return all_passed

def run_component_tests(component, verbose=False):
    """Run tests for a specific component."""
    print_header(f"Running {component.capitalize()} Tests")

    if component not in COMPONENT_TESTS:
        print(f"{Colors.FAIL}Invalid component: {component}{Colors.ENDC}")
        return False

    test_paths = COMPONENT_TESTS[component]
    results = {}

    for test_path in test_paths:
        print_subheader(f"Running test: {test_path}")

        if test_path.endswith(".py"):
            success = run_pytest(test_path, verbose)
        elif test_path.endswith(".js"):
            success = run_frontend_test(test_path, verbose)
        else:
            print(f"{Colors.WARNING}Unknown test type: {test_path}{Colors.ENDC}")
            success = False

        results[test_path] = success

    # Print summary
    print_header(f"{component.capitalize()} Test Summary")
    all_passed = True
    for test_path, success in results.items():
        status = f"{Colors.OKGREEN}PASSED{Colors.ENDC}" if success else f"{Colors.FAIL}FAILED{Colors.ENDC}"
        print(f"{test_path}: {status}")
        if not success:
            all_passed = False

    return all_passed

def run_all_tests(verbose=False):
    """Run all tests for all phases."""
    print_header("Running All Tests")

    results = {}

    # Run tests for each phase
    for phase in range(4):  # Phases 0-3
        phase_result = run_phase_tests(phase, verbose)
        results[f"Phase {phase}"] = phase_result

    # Print final summary
    print_header("All Tests Summary")
    all_passed = True
    for phase, success in results.items():
        status = f"{Colors.OKGREEN}PASSED{Colors.ENDC}" if success else f"{Colors.FAIL}FAILED{Colors.ENDC}"
        print(f"{phase}: {status}")
        if not success:
            all_passed = False

    return all_passed

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run comprehensive tests for RentUp platform")
    parser.add_argument("--phase", type=int, choices=[0, 1, 2, 3], help="Run tests for a specific phase")
    parser.add_argument("--component", type=str, choices=COMPONENT_TESTS.keys(), help="Run tests for a specific component")
    parser.add_argument("--verbose", action="store_true", help="Show detailed test output")
    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()

    print_header("RentUp Comprehensive Test Runner")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    success = False

    if args.phase is not None:
        success = run_phase_tests(args.phase, args.verbose)
    elif args.component:
        success = run_component_tests(args.component, args.verbose)
    else:
        success = run_all_tests(args.verbose)

    print_header("Test Run Complete")
    print(f"Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    if success:
        print(f"\n{Colors.OKGREEN}{Colors.BOLD}All tests passed!{Colors.ENDC}")
        sys.exit(0)
    else:
        print(f"\n{Colors.FAIL}{Colors.BOLD}Some tests failed!{Colors.ENDC}")
        sys.exit(1)

if __name__ == "__main__":
    main()
