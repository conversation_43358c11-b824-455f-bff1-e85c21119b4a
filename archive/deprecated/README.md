# Deprecated Test Infrastructure

**Date Deprecated**: 2025-05-26  
**Reason**: Consolidation and redundancy elimination

## Files Moved Here

### Test Runners (Replaced by `run_tests.py`)
- `test_all_phases.py` - Comprehensive test runner for all phases
- `run_all_tests.py` - Alternative test runner implementation  
- `run_comprehensive_tests.py` - Another comprehensive test runner

**Replacement**: All functionality has been consolidated into the main `run_tests.py` script in the project root.

## Why These Were Deprecated

1. **Redundancy**: Multiple scripts doing essentially the same thing
2. **Maintenance Burden**: Having to update multiple test runners
3. **Confusion**: Developers unsure which script to use
4. **Inconsistency**: Different scripts had different features and behaviors

## Migration Guide

### Old Usage
```bash
# Old way - multiple options
python archive/test_all_phases.py --phase 3
python archive/run_all_tests.py --backend
python archive/run_comprehensive_tests.py --component recommendations
```

### New Usage
```bash
# New way - single consolidated script
python run_tests.py --phase 3
python run_tests.py --backend
python run_tests.py --component recommendations
```

## Features Preserved

All features from the deprecated scripts have been preserved in `run_tests.py`:
- Phase-specific testing
- Component-specific testing
- Backend/frontend separation
- Verbose output options
- Test result reporting
- Error handling and logging

## Safe to Delete

These files are safe to delete after confirming that `run_tests.py` meets all testing needs. They are kept here temporarily for reference during the transition period.

**Recommended Action**: Delete this entire directory after 30 days (by 2025-06-26) if no issues are found with the consolidated test runner.
