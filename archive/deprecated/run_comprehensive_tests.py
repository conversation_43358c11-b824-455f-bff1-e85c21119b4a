#!/usr/bin/env python3
"""
Comprehensive Test Runner for RentUp Platform

This script runs all tests for the RentUp platform, including:
1. Unit tests for all components
2. Integration tests for all phases
3. API tests
4. End-to-end tests

Usage:
    python run_comprehensive_tests.py [--phase PHASE] [--component COMPONENT] [--verbose]

Options:
    --phase PHASE         Run tests for a specific phase (0, 1, 2, 3)
    --component COMPONENT Run tests for a specific component (analytics, recommendations, pricing, etc.)
    --verbose             Show detailed test output
"""

import os
import sys
import subprocess
import argparse
import time
from datetime import datetime
from pathlib import Path
import pytest

# Define colors for terminal output
class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

# Define test categories
COMPONENTS = {
    "analytics": ["tests/unit/analytics", "tests/api/test_analytics_endpoints.py"],
    "recommendations": ["tests/unit/recommendations", "tests/api/test_recommendation_endpoints.py"],
    "pricing": ["tests/unit/pricing", "tests/api/test_pricing_endpoints.py"],
    "user_behavior": ["tests/unit/user_behavior"],
    "auth": ["backend/app/tests/test_auth.py"],
    "items": ["backend/app/tests/test_items.py"],
    "users": ["backend/app/tests/test_users.py"],
    "bookings": ["backend/app/tests/test_bookings.py"],
    "search": ["backend/app/tests/test_search.py"],
    "uploads": ["backend/app/tests/test_uploads.py"],
    "payments": ["backend/app/tests/test_payments.py"]
}

PHASES = {
    0: ["tests/integration/phase0"],
    1: ["tests/integration/phase1"],
    2: ["tests/integration/phase2"],
    3: ["tests/integration/phase3"]
}

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run comprehensive tests for RentUp platform")
    parser.add_argument("--phase", type=int, choices=[0, 1, 2, 3], help="Run tests for a specific phase")
    parser.add_argument("--component", type=str, choices=COMPONENTS.keys(), help="Run tests for a specific component")
    parser.add_argument("--verbose", action="store_true", help="Show detailed test output")
    return parser.parse_args()

def print_header(message):
    """Print a formatted header message."""
    print(f"\n{Colors.HEADER}{Colors.BOLD}{'=' * 80}{Colors.ENDC}")
    print(f"{Colors.HEADER}{Colors.BOLD}{message.center(80)}{Colors.ENDC}")
    print(f"{Colors.HEADER}{Colors.BOLD}{'=' * 80}{Colors.ENDC}\n")

def print_subheader(message):
    """Print a formatted subheader message."""
    print(f"\n{Colors.OKBLUE}{Colors.BOLD}{message}{Colors.ENDC}")
    print(f"{Colors.OKBLUE}{'-' * len(message)}{Colors.ENDC}\n")

def run_pytest(test_paths, verbose=False):
    """Run pytest on specific paths."""
    print_subheader(f"Running tests in {', '.join(test_paths)}")
    
    # Build pytest command
    command = ["-v"] if verbose else []
    command.extend(test_paths)
    
    # Run pytest
    try:
        result = pytest.main(command)
        success = result == 0
        
        if success:
            print(f"\n{Colors.OKGREEN}Tests passed!{Colors.ENDC}\n")
        else:
            print(f"\n{Colors.FAIL}Tests failed!{Colors.ENDC}\n")
        
        return success
    except Exception as e:
        print(f"\n{Colors.FAIL}Error running tests: {e}{Colors.ENDC}\n")
        return False

def run_component_tests(component, verbose=False):
    """Run tests for a specific component."""
    print_header(f"Running Tests for {component.capitalize()}")
    
    if component not in COMPONENTS:
        print(f"{Colors.FAIL}Unknown component: {component}{Colors.ENDC}")
        return False
    
    test_paths = COMPONENTS[component]
    return run_pytest(test_paths, verbose)

def run_phase_tests(phase, verbose=False):
    """Run tests for a specific phase."""
    print_header(f"Running Tests for Phase {phase}")
    
    if phase not in PHASES:
        print(f"{Colors.FAIL}Unknown phase: {phase}{Colors.ENDC}")
        return False
    
    test_paths = PHASES[phase]
    return run_pytest(test_paths, verbose)

def run_all_tests(verbose=False):
    """Run all tests."""
    print_header("Running All Tests")
    
    # Run unit tests
    print_subheader("Running Unit Tests")
    unit_success = run_pytest(["tests/unit"], verbose)
    
    # Run API tests
    print_subheader("Running API Tests")
    api_success = run_pytest(["tests/api"], verbose)
    
    # Run integration tests
    print_subheader("Running Integration Tests")
    integration_success = run_pytest(["tests/integration"], verbose)
    
    # Run backend app tests
    print_subheader("Running Backend App Tests")
    backend_success = run_pytest(["backend/app/tests"], verbose)
    
    return unit_success and api_success and integration_success and backend_success

def create_test_report(results):
    """Create a test report file."""
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    report_dir = Path("testResults")
    report_dir.mkdir(exist_ok=True)
    
    report_path = report_dir / f"comprehensive_test_report_{timestamp}.md"
    
    with open(report_path, "w") as f:
        f.write(f"# RentUp Comprehensive Test Report\n\n")
        f.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## Summary\n\n")
        
        all_passed = all(results.values())
        overall_status = "PASSED" if all_passed else "FAILED"
        
        f.write(f"- Overall Status: {overall_status}\n\n")
        
        f.write("## Details\n\n")
        for test_type, success in results.items():
            status = "PASSED" if success else "FAILED"
            f.write(f"- {test_type}: {status}\n")
        
        f.write("\n## Notes\n\n")
        f.write("This report was generated by the comprehensive test runner.\n")
        f.write("See console output for detailed test results.\n")
    
    print(f"\nTest report saved to {report_path}")
    return report_path

def main():
    """Main function."""
    args = parse_args()
    
    print_header("RentUp Comprehensive Test Runner")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = {}
    
    # Run specific component tests
    if args.component:
        results[f"{args.component.capitalize()} Tests"] = run_component_tests(args.component, args.verbose)
    
    # Run specific phase tests
    elif args.phase is not None:
        results[f"Phase {args.phase} Tests"] = run_phase_tests(args.phase, args.verbose)
    
    # Run all tests
    else:
        # Run tests for each component
        for component in COMPONENTS:
            results[f"{component.capitalize()} Tests"] = run_component_tests(component, args.verbose)
        
        # Run tests for each phase
        for phase in PHASES:
            results[f"Phase {phase} Tests"] = run_phase_tests(phase, args.verbose)
    
    # Create test report
    report_path = create_test_report(results)
    
    # Print final summary
    print_header("Test Run Complete")
    print(f"Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    all_passed = all(results.values())
    if all_passed:
        print(f"\n{Colors.OKGREEN}{Colors.BOLD}All tests passed!{Colors.ENDC}")
        sys.exit(0)
    else:
        print(f"\n{Colors.FAIL}{Colors.BOLD}Some tests failed. See report for details.{Colors.ENDC}")
        sys.exit(1)

if __name__ == "__main__":
    main()
