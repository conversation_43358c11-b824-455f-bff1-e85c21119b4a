#!/bin/bash

# Activate virtual environment
source venv/bin/activate

# Colors for terminal output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Print header
print_header() {
    echo -e "\n${PURPLE}=========================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}=========================================${NC}\n"
}

# Print section
print_section() {
    echo -e "\n${BLUE}$1${NC}"
    echo -e "${BLUE}----------------------------------------${NC}\n"
}

# Run tests
run_tests() {
    print_section "Running $1 tests"

    if [ -n "$2" ]; then
        echo -e "${CYAN}Command: $2${NC}\n"
        eval $2
    else
        echo -e "${YELLOW}No command specified${NC}\n"
    fi

    if [ $? -eq 0 ]; then
        echo -e "\n${GREEN}$1 tests passed!${NC}\n"
        return 0
    else
        echo -e "\n${RED}$1 tests failed!${NC}\n"
        return 1
    fi
}

# Main function
main() {
    print_header "RentUp Phase 3 Test Runner"

    # Create test results directory
    mkdir -p testResults/phase3

    # Run Phase 3 unit tests
    run_tests "Phase 3 Analytics Unit" "python3 -m pytest tests/unit/analytics -v"
    ANALYTICS_RESULT=$?

    run_tests "Phase 3 Recommendations Unit" "python3 -m pytest tests/unit/recommendations -v"
    RECOMMENDATIONS_RESULT=$?

    run_tests "Phase 3 Pricing Unit" "python3 -m pytest tests/unit/pricing -v"
    PRICING_RESULT=$?

    run_tests "Phase 3 User Behavior Unit" "python3 -m pytest tests/unit/user_behavior -v"
    USER_BEHAVIOR_RESULT=$?

    # Run Phase 3 API tests
    run_tests "Phase 3 API" "python3 -m pytest tests/api -v"
    API_RESULT=$?

    # Run Phase 3 integration tests
    run_tests "Phase 3 Integration" "python3 -m pytest tests/integration/phase3 -v"
    INTEGRATION_RESULT=$?

    # Run cross-phase integration tests
    run_tests "Cross-Phase Integration" "python3 -m pytest backend/app/tests/test_recommendations.py -v"
    CROSS_PHASE_RESULT=$?

    # Run auction system tests
    run_tests "Auction System" "python3 -m pytest backend/app/tests/test_auction_system.py -v"
    AUCTION_RESULT=$?

    # Run agreement system tests
    run_tests "Agreement System" "python3 -m pytest backend/app/tests/test_agreement_system.py -v"
    AGREEMENT_RESULT=$?

    # Run fraud prevention system tests
    run_tests "Fraud Prevention System" "python3 -m pytest backend/app/tests/test_fraud_prevention_system.py -v"
    FRAUD_RESULT=$?

    # Print summary
    print_header "Test Summary"

    if [ $ANALYTICS_RESULT -eq 0 ]; then
        echo -e "${GREEN}Analytics Tests: PASSED${NC}"
    else
        echo -e "${RED}Analytics Tests: FAILED${NC}"
    fi

    if [ $RECOMMENDATIONS_RESULT -eq 0 ]; then
        echo -e "${GREEN}Recommendations Tests: PASSED${NC}"
    else
        echo -e "${RED}Recommendations Tests: FAILED${NC}"
    fi

    if [ $PRICING_RESULT -eq 0 ]; then
        echo -e "${GREEN}Pricing Tests: PASSED${NC}"
    else
        echo -e "${RED}Pricing Tests: FAILED${NC}"
    fi

    if [ $USER_BEHAVIOR_RESULT -eq 0 ]; then
        echo -e "${GREEN}User Behavior Tests: PASSED${NC}"
    else
        echo -e "${RED}User Behavior Tests: FAILED${NC}"
    fi

    if [ $API_RESULT -eq 0 ]; then
        echo -e "${GREEN}API Tests: PASSED${NC}"
    else
        echo -e "${RED}API Tests: FAILED${NC}"
    fi

    if [ $INTEGRATION_RESULT -eq 0 ]; then
        echo -e "${GREEN}Integration Tests: PASSED${NC}"
    else
        echo -e "${RED}Integration Tests: FAILED${NC}"
    fi

    if [ $CROSS_PHASE_RESULT -eq 0 ]; then
        echo -e "${GREEN}Cross-Phase Integration Tests: PASSED${NC}"
    else
        echo -e "${RED}Cross-Phase Integration Tests: FAILED${NC}"
    fi

    if [ $AUCTION_RESULT -eq 0 ]; then
        echo -e "${GREEN}Auction System Tests: PASSED${NC}"
    else
        echo -e "${RED}Auction System Tests: FAILED${NC}"
    fi

    if [ $AGREEMENT_RESULT -eq 0 ]; then
        echo -e "${GREEN}Agreement System Tests: PASSED${NC}"
    else
        echo -e "${RED}Agreement System Tests: FAILED${NC}"
    fi

    if [ $FRAUD_RESULT -eq 0 ]; then
        echo -e "${GREEN}Fraud Prevention System Tests: PASSED${NC}"
    else
        echo -e "${RED}Fraud Prevention System Tests: FAILED${NC}"
    fi

    # Create test report
    TIMESTAMP=$(date +"%Y-%m-%d_%H-%M-%S")
    REPORT_FILE="testResults/phase3/phase3_test_report_${TIMESTAMP}.md"

    echo "# Phase 3 Test Report" > $REPORT_FILE
    echo "" >> $REPORT_FILE
    echo "Date: $(date +"%Y-%m-%d %H:%M:%S")" >> $REPORT_FILE
    echo "" >> $REPORT_FILE
    echo "## Summary" >> $REPORT_FILE
    echo "" >> $REPORT_FILE

    if [ $ANALYTICS_RESULT -eq 0 ]; then
        echo "- Analytics Tests: PASSED" >> $REPORT_FILE
    else
        echo "- Analytics Tests: FAILED" >> $REPORT_FILE
    fi

    if [ $RECOMMENDATIONS_RESULT -eq 0 ]; then
        echo "- Recommendations Tests: PASSED" >> $REPORT_FILE
    else
        echo "- Recommendations Tests: FAILED" >> $REPORT_FILE
    fi

    if [ $PRICING_RESULT -eq 0 ]; then
        echo "- Pricing Tests: PASSED" >> $REPORT_FILE
    else
        echo "- Pricing Tests: FAILED" >> $REPORT_FILE
    fi

    if [ $USER_BEHAVIOR_RESULT -eq 0 ]; then
        echo "- User Behavior Tests: PASSED" >> $REPORT_FILE
    else
        echo "- User Behavior Tests: FAILED" >> $REPORT_FILE
    fi

    if [ $API_RESULT -eq 0 ]; then
        echo "- API Tests: PASSED" >> $REPORT_FILE
    else
        echo "- API Tests: FAILED" >> $REPORT_FILE
    fi

    if [ $INTEGRATION_RESULT -eq 0 ]; then
        echo "- Integration Tests: PASSED" >> $REPORT_FILE
    else
        echo "- Integration Tests: FAILED" >> $REPORT_FILE
    fi

    if [ $CROSS_PHASE_RESULT -eq 0 ]; then
        echo "- Cross-Phase Integration Tests: PASSED" >> $REPORT_FILE
    else
        echo "- Cross-Phase Integration Tests: FAILED" >> $REPORT_FILE
    fi

    if [ $AUCTION_RESULT -eq 0 ]; then
        echo "- Auction System Tests: PASSED" >> $REPORT_FILE
    else
        echo "- Auction System Tests: FAILED" >> $REPORT_FILE
    fi

    if [ $AGREEMENT_RESULT -eq 0 ]; then
        echo "- Agreement System Tests: PASSED" >> $REPORT_FILE
    else
        echo "- Agreement System Tests: FAILED" >> $REPORT_FILE
    fi

    if [ $FRAUD_RESULT -eq 0 ]; then
        echo "- Fraud Prevention System Tests: PASSED" >> $REPORT_FILE
    else
        echo "- Fraud Prevention System Tests: FAILED" >> $REPORT_FILE
    fi

    echo "" >> $REPORT_FILE
    echo "## Details" >> $REPORT_FILE
    echo "" >> $REPORT_FILE
    echo "See console output for detailed test results." >> $REPORT_FILE

    echo -e "\n${GREEN}Test report saved to ${REPORT_FILE}${NC}"

    # Return overall result
    if [ $ANALYTICS_RESULT -eq 0 ] && [ $RECOMMENDATIONS_RESULT -eq 0 ] && [ $PRICING_RESULT -eq 0 ] && [ $USER_BEHAVIOR_RESULT -eq 0 ] && [ $API_RESULT -eq 0 ] && [ $INTEGRATION_RESULT -eq 0 ] && [ $CROSS_PHASE_RESULT -eq 0 ] && [ $AUCTION_RESULT -eq 0 ] && [ $AGREEMENT_RESULT -eq 0 ] && [ $FRAUD_RESULT -eq 0 ]; then
        print_header "All tests passed!"
        return 0
    else
        print_header "Some tests failed!"
        return 1
    fi
}

# Run main function
main
