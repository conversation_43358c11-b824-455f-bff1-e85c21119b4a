# RentUp Database Schema

## Overview

This document outlines the database schema for the RentUp platform. We're using PostgreSQL as our primary relational database, containerized with Docker.

## Entity Relationship Diagram

[ERD will be added here]

## Tables

### Users

Stores user account information.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier for the user |
| email | VARCHAR(255) | UNIQUE, NOT NULL | User's email address |
| password_hash | VARCHAR(255) | NOT NULL | Hashed password |
| first_name | VARCHAR(100) | NOT NULL | User's first name |
| last_name | VARCHAR(100) | NOT NULL | User's last name |
| phone_number | VARCHAR(20) | UNIQUE | User's phone number |
| profile_image_url | VARCHAR(255) | | URL to profile image |
| bio | TEXT | | User's biography |
| date_of_birth | DATE | | User's date of birth |
| is_verified | BOOLEAN | DEFAULT FALSE | Whether user is verified |
| verification_level | INTEGER | DEFAULT 0 | Level of verification (0-3) |
| is_business | BOOLEAN | DEFAULT FALSE | Whether account is a business account |
| created_at | TIMESTAMP | DEFAULT NOW() | When the account was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When the account was last updated |
| last_login_at | TIMESTAMP | | When the user last logged in |
| status | VARCHAR(20) | DEFAULT 'active' | Account status (active, suspended, etc.) |

### UserVerifications

Stores verification information for users.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| user_id | UUID | FK(users.id), NOT NULL | Reference to user |
| verification_type | VARCHAR(50) | NOT NULL | Type of verification (id, address, etc.) |
| verification_data | JSONB | | Verification data |
| status | VARCHAR(20) | DEFAULT 'pending' | Status of verification |
| verified_at | TIMESTAMP | | When verification was completed |
| created_at | TIMESTAMP | DEFAULT NOW() | When record was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When record was last updated |

### Listings

Stores information about items available for rent.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier for the listing |
| owner_id | UUID | FK(users.id), NOT NULL | Reference to the owner |
| title | VARCHAR(255) | NOT NULL | Listing title |
| description | TEXT | NOT NULL | Detailed description |
| category_id | UUID | FK(categories.id), NOT NULL | Reference to category |
| subcategory_id | UUID | FK(subcategories.id) | Reference to subcategory |
| condition | VARCHAR(50) | NOT NULL | Condition of the item |
| hourly_rate | DECIMAL(10,2) | | Hourly rental rate |
| daily_rate | DECIMAL(10,2) | | Daily rental rate |
| weekly_rate | DECIMAL(10,2) | | Weekly rental rate |
| monthly_rate | DECIMAL(10,2) | | Monthly rental rate |
| security_deposit | DECIMAL(10,2) | | Required security deposit |
| currency | VARCHAR(3) | DEFAULT 'USD' | Currency for rates |
| location | GEOGRAPHY(POINT) | | Geographic location |
| address | JSONB | | Address details |
| is_available | BOOLEAN | DEFAULT TRUE | Whether item is available |
| is_featured | BOOLEAN | DEFAULT FALSE | Whether listing is featured |
| is_auction | BOOLEAN | DEFAULT FALSE | Whether listing is auction-style |
| min_rental_duration | INTEGER | | Minimum rental duration (hours) |
| max_rental_duration | INTEGER | | Maximum rental duration (hours) |
| created_at | TIMESTAMP | DEFAULT NOW() | When listing was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When listing was last updated |
| status | VARCHAR(20) | DEFAULT 'active' | Listing status |

### ListingImages

Stores images for listings.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| listing_id | UUID | FK(listings.id), NOT NULL | Reference to listing |
| image_url | VARCHAR(255) | NOT NULL | URL to image |
| is_primary | BOOLEAN | DEFAULT FALSE | Whether this is the primary image |
| display_order | INTEGER | DEFAULT 0 | Order for display |
| created_at | TIMESTAMP | DEFAULT NOW() | When record was created |

### Categories

Stores item categories.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| name | VARCHAR(100) | NOT NULL, UNIQUE | Category name |
| description | TEXT | | Category description |
| icon_url | VARCHAR(255) | | URL to category icon |
| display_order | INTEGER | DEFAULT 0 | Order for display |
| created_at | TIMESTAMP | DEFAULT NOW() | When category was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When category was last updated |

### Subcategories

Stores item subcategories.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| category_id | UUID | FK(categories.id), NOT NULL | Reference to parent category |
| name | VARCHAR(100) | NOT NULL | Subcategory name |
| description | TEXT | | Subcategory description |
| icon_url | VARCHAR(255) | | URL to subcategory icon |
| display_order | INTEGER | DEFAULT 0 | Order for display |
| created_at | TIMESTAMP | DEFAULT NOW() | When subcategory was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When subcategory was last updated |

### Availability

Stores availability for listings.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| listing_id | UUID | FK(listings.id), NOT NULL | Reference to listing |
| start_date | TIMESTAMP | NOT NULL | Start of availability period |
| end_date | TIMESTAMP | NOT NULL | End of availability period |
| created_at | TIMESTAMP | DEFAULT NOW() | When record was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When record was last updated |

### Bookings

Stores booking information.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| listing_id | UUID | FK(listings.id), NOT NULL | Reference to listing |
| renter_id | UUID | FK(users.id), NOT NULL | Reference to renter |
| start_date | TIMESTAMP | NOT NULL | Start of rental period |
| end_date | TIMESTAMP | NOT NULL | End of rental period |
| status | VARCHAR(20) | DEFAULT 'pending' | Booking status |
| total_amount | DECIMAL(10,2) | NOT NULL | Total rental amount |
| security_deposit | DECIMAL(10,2) | | Security deposit amount |
| currency | VARCHAR(3) | DEFAULT 'USD' | Currency code |
| payment_status | VARCHAR(20) | DEFAULT 'pending' | Payment status |
| cancellation_reason | TEXT | | Reason if canceled |
| created_at | TIMESTAMP | DEFAULT NOW() | When booking was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When booking was last updated |

### Payments

Stores payment information.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| booking_id | UUID | FK(bookings.id), NOT NULL | Reference to booking |
| payer_id | UUID | FK(users.id), NOT NULL | User making payment |
| recipient_id | UUID | FK(users.id), NOT NULL | User receiving payment |
| amount | DECIMAL(10,2) | NOT NULL | Payment amount |
| currency | VARCHAR(3) | DEFAULT 'USD' | Currency code |
| payment_method | VARCHAR(50) | | Payment method used |
| payment_provider_id | VARCHAR(100) | | ID from payment provider |
| status | VARCHAR(20) | DEFAULT 'pending' | Payment status |
| created_at | TIMESTAMP | DEFAULT NOW() | When payment was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When payment was last updated |

### Reviews

Stores user reviews.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| booking_id | UUID | FK(bookings.id), NOT NULL | Reference to booking |
| reviewer_id | UUID | FK(users.id), NOT NULL | User writing review |
| reviewee_id | UUID | FK(users.id), NOT NULL | User being reviewed |
| listing_id | UUID | FK(listings.id), NOT NULL | Reference to listing |
| rating | INTEGER | NOT NULL | Rating (1-5) |
| review_text | TEXT | | Review content |
| created_at | TIMESTAMP | DEFAULT NOW() | When review was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When review was last updated |

### Messages

Stores user messages.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| conversation_id | UUID | FK(conversations.id), NOT NULL | Reference to conversation |
| sender_id | UUID | FK(users.id), NOT NULL | User sending message |
| message_text | TEXT | NOT NULL | Message content |
| is_read | BOOLEAN | DEFAULT FALSE | Whether message is read |
| created_at | TIMESTAMP | DEFAULT NOW() | When message was sent |

### Conversations

Stores message conversations.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| listing_id | UUID | FK(listings.id) | Associated listing |
| booking_id | UUID | FK(bookings.id) | Associated booking |
| created_at | TIMESTAMP | DEFAULT NOW() | When conversation started |
| updated_at | TIMESTAMP | DEFAULT NOW() | When conversation was updated |

### ConversationParticipants

Links users to conversations.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| conversation_id | UUID | FK(conversations.id), NOT NULL | Reference to conversation |
| user_id | UUID | FK(users.id), NOT NULL | Reference to user |
| created_at | TIMESTAMP | DEFAULT NOW() | When user joined conversation |

### WantedListings

Stores "Looking to Rent" listings.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| user_id | UUID | FK(users.id), NOT NULL | Reference to user |
| title | VARCHAR(255) | NOT NULL | Listing title |
| description | TEXT | NOT NULL | Detailed description |
| category_id | UUID | FK(categories.id) | Reference to category |
| subcategory_id | UUID | FK(subcategories.id) | Reference to subcategory |
| max_budget | DECIMAL(10,2) | | Maximum budget |
| currency | VARCHAR(3) | DEFAULT 'USD' | Currency code |
| location | GEOGRAPHY(POINT) | | Geographic location |
| address | JSONB | | Address details |
| needed_from | TIMESTAMP | | When item is needed from |
| needed_until | TIMESTAMP | | When item is needed until |
| status | VARCHAR(20) | DEFAULT 'active' | Listing status |
| created_at | TIMESTAMP | DEFAULT NOW() | When listing was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When listing was last updated |

### Notifications

Stores user notifications.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| user_id | UUID | FK(users.id), NOT NULL | Reference to user |
| type | VARCHAR(50) | NOT NULL | Notification type |
| title | VARCHAR(255) | NOT NULL | Notification title |
| content | TEXT | NOT NULL | Notification content |
| data | JSONB | | Additional data |
| is_read | BOOLEAN | DEFAULT FALSE | Whether notification is read |
| created_at | TIMESTAMP | DEFAULT NOW() | When notification was created |

### UserPreferences

Stores user preferences.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| user_id | UUID | FK(users.id), NOT NULL | Reference to user |
| email_notifications | BOOLEAN | DEFAULT TRUE | Email notification preference |
| push_notifications | BOOLEAN | DEFAULT TRUE | Push notification preference |
| sms_notifications | BOOLEAN | DEFAULT FALSE | SMS notification preference |
| preferred_currency | VARCHAR(3) | DEFAULT 'USD' | Preferred currency |
| language | VARCHAR(10) | DEFAULT 'en' | Preferred language |
| created_at | TIMESTAMP | DEFAULT NOW() | When preferences were created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When preferences were last updated |

### Reports

Stores user reports.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| reporter_id | UUID | FK(users.id), NOT NULL | User making report |
| reported_user_id | UUID | FK(users.id) | Reported user |
| reported_listing_id | UUID | FK(listings.id) | Reported listing |
| reported_review_id | UUID | FK(reviews.id) | Reported review |
| reason | VARCHAR(100) | NOT NULL | Reason for report |
| details | TEXT | | Report details |
| status | VARCHAR(20) | DEFAULT 'pending' | Report status |
| created_at | TIMESTAMP | DEFAULT NOW() | When report was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When report was last updated |

### AuctionListings

Stores auction-specific information.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| listing_id | UUID | FK(listings.id), NOT NULL | Reference to listing |
| start_time | TIMESTAMP | NOT NULL | Auction start time |
| end_time | TIMESTAMP | NOT NULL | Auction end time |
| starting_price | DECIMAL(10,2) | NOT NULL | Starting bid price |
| reserve_price | DECIMAL(10,2) | | Minimum acceptable price |
| current_price | DECIMAL(10,2) | | Current highest bid |
| current_bidder_id | UUID | FK(users.id) | Current highest bidder |
| bid_increment | DECIMAL(10,2) | DEFAULT 1.00 | Minimum bid increment |
| status | VARCHAR(20) | DEFAULT 'scheduled' | Auction status |
| created_at | TIMESTAMP | DEFAULT NOW() | When auction was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When auction was last updated |

### AuctionBids

Stores bids for auctions.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| auction_id | UUID | FK(auction_listings.id), NOT NULL | Reference to auction |
| bidder_id | UUID | FK(users.id), NOT NULL | Reference to bidder |
| amount | DECIMAL(10,2) | NOT NULL | Bid amount |
| created_at | TIMESTAMP | DEFAULT NOW() | When bid was placed |
| status | VARCHAR(20) | DEFAULT 'active' | Bid status |

## Vector Database (Qdrant)

For AI-powered features, we use Qdrant with GPU acceleration and Nomic embeddings. The following collections will be created in Qdrant:

### ListingVectors

Stores vector embeddings for listings to power semantic search and recommendations.

| Field | Type | Description |
|-------|------|-------------|
| id | UUID | Unique identifier (matches listing_id in PostgreSQL) |
| vector | Vector(384) | Nomic embedding of listing title and description |
| payload | JSON | Additional metadata including: |
| - listing_id | UUID | Reference to listing in PostgreSQL |
| - title | String | Listing title |
| - description | String | Listing description |
| - category_id | UUID | Category identifier |
| - subcategory_id | UUID | Subcategory identifier |
| - price_range | Object | Min/max price information |
| - location | GeoPoint | Geographic coordinates |
| - created_at | Timestamp | When the vector was created |
| - updated_at | Timestamp | When the vector was last updated |

### UserPreferenceVectors

Stores vector embeddings of user preferences and behavior for personalized recommendations.

| Field | Type | Description |
|-------|------|-------------|
| id | UUID | Unique identifier (matches user_id in PostgreSQL) |
| vector | Vector(384) | Nomic embedding of user preferences and behavior |
| payload | JSON | Additional metadata including: |
| - user_id | UUID | Reference to user in PostgreSQL |
| - preferred_categories | Array[UUID] | Categories user has shown interest in |
| - search_history | Array[String] | Recent search terms |
| - viewed_listings | Array[UUID] | Recently viewed listings |
| - rental_history | Array[UUID] | Previously rented items |
| - location | GeoPoint | User's primary location |
| - price_sensitivity | Float | Derived price sensitivity score |
| - created_at | Timestamp | When the vector was created |
| - updated_at | Timestamp | When the vector was last updated |

### CategoryVectors

Stores vector embeddings for categories to enable semantic category matching.

| Field | Type | Description |
|-------|------|-------------|
| id | UUID | Unique identifier (matches category_id in PostgreSQL) |
| vector | Vector(384) | Nomic embedding of category name and description |
| payload | JSON | Additional metadata including: |
| - category_id | UUID | Reference to category in PostgreSQL |
| - name | String | Category name |
| - description | String | Category description |
| - parent_category_id | UUID | Parent category if applicable |
| - created_at | Timestamp | When the vector was created |
| - updated_at | Timestamp | When the vector was last updated |

### ReviewVectors

Stores vector embeddings of reviews for sentiment analysis and recommendation enhancement.

| Field | Type | Description |
|-------|------|-------------|
| id | UUID | Unique identifier (matches review_id in PostgreSQL) |
| vector | Vector(384) | Nomic embedding of review text |
| payload | JSON | Additional metadata including: |
| - review_id | UUID | Reference to review in PostgreSQL |
| - listing_id | UUID | Associated listing |
| - rating | Integer | Numerical rating (1-5) |
| - sentiment_score | Float | Calculated sentiment score |
| - key_phrases | Array[String] | Extracted key phrases |
| - created_at | Timestamp | When the vector was created |

## Qdrant Configuration

The Qdrant vector database will be configured with the following settings:

- **Container**: qdrant/qdrant:gpu-nvidia-latest
- **GPU Indexing**: Enabled (gpu-indexing=1)
- **Vector Size**: 384 dimensions (Nomic embed-text)
- **Distance Metric**: Cosine similarity
- **Indexing**: HNSW (Hierarchical Navigable Small World)
- **Payload Storage**: On-disk for large collections

## Use Cases for Vector Database

1. **Semantic Search**: Allow users to find listings using natural language queries that understand context and meaning beyond keywords
2. **Personalized Recommendations**: Generate tailored listing suggestions based on user preferences and behavior
3. **Similar Item Discovery**: Find items similar to ones a user has viewed or rented
4. **Category Exploration**: Help users discover related categories they might not have considered
5. **Review Analysis**: Extract insights from reviews to improve recommendations and highlight key features
6. **Fraud Detection**: Identify unusual patterns in listings or user behavior that might indicate fraud
7. **Dynamic Pricing Suggestions**: Analyze similar listings to suggest optimal pricing
8. **Trend Detection**: Identify emerging rental categories and interests

## Database Schema Additions

The current database schema is quite comprehensive, covering all the core functionality for RentUp. However, we could add a few more tables to enhance certain features:

## Additional Tables to Consider
## Additional Feedback Tables

### DetailedReviews

Extends the basic review with detailed rating categories.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| review_id | UUID | FK(reviews.id), NOT NULL | Reference to main review |
| accuracy_rating | INTEGER | | Rating for listing accuracy (1-5) |
| communication_rating | INTEGER | | Rating for communication (1-5) |
| cleanliness_rating | INTEGER | | Rating for item cleanliness (1-5) |
| value_rating | INTEGER | | Rating for value for money (1-5) |
| condition_rating | INTEGER | | Rating for item condition (1-5) |
| created_at | TIMESTAMP | DEFAULT NOW() | When detailed review was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When detailed review was updated |

### ReviewHelpfulness

Tracks whether users found reviews helpful.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| review_id | UUID | FK(reviews.id), NOT NULL | Reference to review |
| user_id | UUID | FK(users.id), NOT NULL | User who rated helpfulness |
| is_helpful | BOOLEAN | NOT NULL | Whether user found review helpful |
| created_at | TIMESTAMP | DEFAULT NOW() | When helpfulness was recorded |

### ReviewImages

Stores images attached to reviews.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| review_id | UUID | FK(reviews.id), NOT NULL | Reference to review |
| image_url | VARCHAR(255) | NOT NULL | URL to review image |
| caption | VARCHAR(255) | | Image caption |
| display_order | INTEGER | DEFAULT 0 | Order for display |
| created_at | TIMESTAMP | DEFAULT NOW() | When image was added |

### ReviewResponses

Stores responses to reviews from owners.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| review_id | UUID | FK(reviews.id), NOT NULL | Reference to review |
| responder_id | UUID | FK(users.id), NOT NULL | User responding to review |
| response_text | TEXT | NOT NULL | Response content |
| created_at | TIMESTAMP | DEFAULT NOW() | When response was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When response was updated |

### UserFeedback

Stores general platform feedback not tied to specific bookings.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| user_id | UUID | FK(users.id), NOT NULL | User providing feedback |
| feedback_type | VARCHAR(50) | NOT NULL | Type of feedback |
| feedback_text | TEXT | NOT NULL | Feedback content |
| satisfaction_rating | INTEGER | | Overall satisfaction (1-5) |
| feature_request | BOOLEAN | DEFAULT FALSE | Whether feedback is feature request |
| status | VARCHAR(20) | DEFAULT 'received' | Feedback status |
| created_at | TIMESTAMP | DEFAULT NOW() | When feedback was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When feedback was updated |

### InsuranceOptions

Stores insurance options for listings.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| name | VARCHAR(100) | NOT NULL | Insurance plan name |
| description | TEXT | NOT NULL | Plan description |
| provider | VARCHAR(100) | NOT NULL | Insurance provider |
| coverage_details | JSONB | NOT NULL | Coverage details |
| price_percentage | DECIMAL(5,2) | NOT NULL | Price as percentage of rental |
| min_price | DECIMAL(10,2) | NOT NULL | Minimum price |
| max_coverage | DECIMAL(10,2) | | Maximum coverage amount |
| is_active | BOOLEAN | DEFAULT TRUE | Whether plan is active |
| created_at | TIMESTAMP | DEFAULT NOW() | When plan was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When plan was last updated |

### BookingInsurance

Links bookings to insurance options.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| booking_id | UUID | FK(bookings.id), NOT NULL | Reference to booking |
| insurance_id | UUID | FK(insurance_options.id), NOT NULL | Reference to insurance plan |
| price | DECIMAL(10,2) | NOT NULL | Price paid for insurance |
| coverage_amount | DECIMAL(10,2) | NOT NULL | Coverage amount |
| policy_number | VARCHAR(100) | | Insurance policy number |
| status | VARCHAR(20) | DEFAULT 'active' | Insurance status |
| created_at | TIMESTAMP | DEFAULT NOW() | When record was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When record was last updated |

### InsuranceClaims

Stores insurance claims.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| booking_insurance_id | UUID | FK(booking_insurance.id), NOT NULL | Reference to booking insurance |
| claim_amount | DECIMAL(10,2) | NOT NULL | Claimed amount |
| description | TEXT | NOT NULL | Claim description |
| evidence_urls | JSONB | | URLs to evidence files |
| status | VARCHAR(20) | DEFAULT 'pending' | Claim status |
| resolution_notes | TEXT | | Notes on resolution |
| resolved_at | TIMESTAMP | | When claim was resolved |
| created_at | TIMESTAMP | DEFAULT NOW() | When claim was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When claim was last updated |

### Promotions

Stores promotional offers.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| code | VARCHAR(50) | UNIQUE, NOT NULL | Promotion code |
| title | VARCHAR(100) | NOT NULL | Promotion title |
| description | TEXT | | Promotion description |
| discount_type | VARCHAR(20) | NOT NULL | Type (percentage, fixed) |
| discount_value | DECIMAL(10,2) | NOT NULL | Discount amount |
| min_rental_amount | DECIMAL(10,2) | | Minimum rental amount |
| max_discount | DECIMAL(10,2) | | Maximum discount amount |
| start_date | TIMESTAMP | NOT NULL | When promotion starts |
| end_date | TIMESTAMP | NOT NULL | When promotion ends |
| usage_limit | INTEGER | | Maximum number of uses |
| usage_count | INTEGER | DEFAULT 0 | Current usage count |
| is_active | BOOLEAN | DEFAULT TRUE | Whether promotion is active |
| created_at | TIMESTAMP | DEFAULT NOW() | When promotion was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When promotion was last updated |

### BookingPromotions

Links bookings to promotions.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| booking_id | UUID | FK(bookings.id), NOT NULL | Reference to booking |
| promotion_id | UUID | FK(promotions.id), NOT NULL | Reference to promotion |
| discount_amount | DECIMAL(10,2) | NOT NULL | Discount amount applied |
| created_at | TIMESTAMP | DEFAULT NOW() | When record was created |

### UserReferrals

Stores user referral information.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| referrer_id | UUID | FK(users.id), NOT NULL | User who referred |
| referred_id | UUID | FK(users.id), NOT NULL | User who was referred |
| referral_code | VARCHAR(50) | | Referral code used |
| status | VARCHAR(20) | DEFAULT 'pending' | Referral status |
| reward_amount | DECIMAL(10,2) | | Reward amount |
| reward_status | VARCHAR(20) | DEFAULT 'pending' | Reward status |
| created_at | TIMESTAMP | DEFAULT NOW() | When referral was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When referral was updated |

### ListingAnalytics

Stores analytics data for listings.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| listing_id | UUID | FK(listings.id), NOT NULL | Reference to listing |
| views | INTEGER | DEFAULT 0 | Number of views |
| unique_views | INTEGER | DEFAULT 0 | Number of unique views |
| inquiries | INTEGER | DEFAULT 0 | Number of inquiries |
| bookings | INTEGER | DEFAULT 0 | Number of bookings |
| conversion_rate | DECIMAL(5,2) | | View to booking rate |
| avg_rental_duration | INTEGER | | Average rental duration (hours) |
| total_revenue | DECIMAL(10,2) | DEFAULT 0 | Total revenue generated |
| last_updated | TIMESTAMP | DEFAULT NOW() | When analytics were updated |

### MaintenanceRecords

Stores maintenance records for listings.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| listing_id | UUID | FK(listings.id), NOT NULL | Reference to listing |
| maintenance_type | VARCHAR(50) | NOT NULL | Type of maintenance |
| description | TEXT | NOT NULL | Maintenance description |
| cost | DECIMAL(10,2) | | Maintenance cost |
| performed_by | VARCHAR(100) | | Who performed maintenance |
| performed_at | TIMESTAMP | NOT NULL | When maintenance was performed |
| next_maintenance_due | TIMESTAMP | | When next maintenance is due |
| created_at | TIMESTAMP | DEFAULT NOW() | When record was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When record was updated |

## Database Relationships

[Detailed relationship diagram will be added here]

### Key Relationships

1. **Users to Listings**: One-to-many (owner_id)
2. **Users to Bookings**: One-to-many (renter_id)
3. **Listings to Bookings**: One-to-many
4. **Categories to Subcategories**: One-to-many
5. **Categories to Listings**: One-to-many
6. **Users to Reviews**: One-to-many (as both reviewer and reviewee)
7. **Listings to Reviews**: One-to-many
8. **Bookings to Reviews**: One-to-one
9. **Listings to AuctionListings**: One-to-one
10. **AuctionListings to AuctionBids**: One-to-many
11. **Users to WantedListings**: One-to-many
12. **Users to Conversations**: Many-to-many (through ConversationParticipants)
13. **Bookings to Insurance**: One-to-many
14. **Bookings to Promotions**: Many-to-many (through BookingPromotions)
15. **Users to Referrals**: One-to-many (as both referrer and referred)

## Indexing Strategy

### PostgreSQL Indexes

1. **B-tree indexes** on all primary keys and foreign keys
2. **GIN indexes** on JSONB fields (address, verification_data, etc.)
3. **GiST indexes** on geographic fields (location)
4. **Partial indexes** on filtered queries (e.g., active listings)
5. **Composite indexes** on frequently combined query fields

### Qdrant Indexes

1. **HNSW indexes** on all vector collections
2. **Scalar filters** on frequently filtered fields
3. **Geo-indexes** on location fields

## Data Migration and Versioning

The database schema will be versioned using migration scripts. Each migration will:

1. Be numbered sequentially
2. Include up and down migration paths
3. Be idempotent where possible
4. Include data transformation logic where needed

## Backup Strategy

1. **PostgreSQL**: Daily full backups, hourly incremental backups
2. **Qdrant**: Daily snapshots of collections
3. **Point-in-time recovery**: Transaction logs retained for 30 days
4. **Geographic redundancy**: Backups stored in multiple regions

## Security Considerations

1. **Encryption**: Data at rest and in transit
2. **Access control**: Role-based access to database resources
3. **Audit logging**: All data modifications logged
4. **Data masking**: Sensitive data masked for non-privileged users
5. **Row-level security**: Implemented for multi-tenant data

### UserRatings

Stores aggregate user ratings.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| user_id | UUID | FK(users.id), NOT NULL | Reference to user |
| avg_rating | DECIMAL(3,2) | DEFAULT 0 | Average overall rating |
| rating_count | INTEGER | DEFAULT 0 | Number of ratings received |
| as_owner_avg_rating | DECIMAL(3,2) | DEFAULT 0 | Average rating as owner |
| as_owner_count | INTEGER | DEFAULT 0 | Number of ratings as owner |
| as_renter_avg_rating | DECIMAL(3,2) | DEFAULT 0 | Average rating as renter |
| as_renter_count | INTEGER | DEFAULT 0 | Number of ratings as renter |
| communication_avg | DECIMAL(3,2) | DEFAULT 0 | Average communication rating |
| reliability_avg | DECIMAL(3,2) | DEFAULT 0 | Average reliability rating |
| last_updated | TIMESTAMP | DEFAULT NOW() | When ratings were last updated |

### ListingRatings

Stores aggregate listing ratings.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| listing_id | UUID | FK(listings.id), NOT NULL | Reference to listing |
| avg_rating | DECIMAL(3,2) | DEFAULT 0 | Average overall rating |
| rating_count | INTEGER | DEFAULT 0 | Number of ratings received |
| accuracy_avg | DECIMAL(3,2) | DEFAULT 0 | Average accuracy rating |
| cleanliness_avg | DECIMAL(3,2) | DEFAULT 0 | Average cleanliness rating |
| value_avg | DECIMAL(3,2) | DEFAULT 0 | Average value rating |
| condition_avg | DECIMAL(3,2) | DEFAULT 0 | Average condition rating |
| last_updated | TIMESTAMP | DEFAULT NOW() | When ratings were last updated |

### SustainabilityMetrics

Based on your sustainability-tracking.md wireframe:

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| listing_id | UUID | FK(listings.id), NOT NULL | Reference to listing |
| carbon_footprint_saved | DECIMAL(10,2) | DEFAULT 0 | Estimated carbon savings (kg) |
| resources_saved | JSONB | | Resources saved by renting vs buying |
| sustainability_score | INTEGER | | Score from 1-100 |
| certification_ids | UUID[] | | References to sustainability certifications |
| created_at | TIMESTAMP | DEFAULT NOW() | When record was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When record was updated |

### SustainabilityCertifications

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| name | VARCHAR(100) | NOT NULL | Certification name |
| issuing_organization | VARCHAR(100) | NOT NULL | Organization that issues certification |
| description | TEXT | | Description of certification |
| icon_url | VARCHAR(255) | | URL to certification icon |
| verification_required | BOOLEAN | DEFAULT TRUE | Whether verification is required |
| created_at | TIMESTAMP | DEFAULT NOW() | When record was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When record was updated |

### GamificationBadges

Based on your gamification-features.md wireframe:

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| name | VARCHAR(100) | NOT NULL | Badge name |
| description | TEXT | NOT NULL | Badge description |
| icon_url | VARCHAR(255) | NOT NULL | URL to badge icon |
| category | VARCHAR(50) | NOT NULL | Badge category |
| points_value | INTEGER | DEFAULT 0 | Points awarded for badge |
| requirements | JSONB | NOT NULL | Requirements to earn badge |
| is_active | BOOLEAN | DEFAULT TRUE | Whether badge is active |
| created_at | TIMESTAMP | DEFAULT NOW() | When badge was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When badge was updated |

### UserBadges

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| user_id | UUID | FK(users.id), NOT NULL | Reference to user |
| badge_id | UUID | FK(gamification_badges.id), NOT NULL | Reference to badge |
| earned_at | TIMESTAMP | DEFAULT NOW() | When badge was earned |
| is_displayed | BOOLEAN | DEFAULT TRUE | Whether badge is displayed on profile |

### UserPoints

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| user_id | UUID | FK(users.id), NOT NULL | Reference to user |
| total_points | INTEGER | DEFAULT 0 | Total points earned |
| available_points | INTEGER | DEFAULT 0 | Points available to spend |
| level | INTEGER | DEFAULT 1 | User's current level |
| points_history | JSONB | | History of point transactions |
| last_updated | TIMESTAMP | DEFAULT NOW() | When points were last updated |

### DeliveryOptions

Based on your delivery-tracking.md and third-party-delivery.md wireframes:

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| listing_id | UUID | FK(listings.id), NOT NULL | Reference to listing |
| delivery_available | BOOLEAN | DEFAULT FALSE | Whether delivery is available |
| delivery_fee | DECIMAL(10,2) | | Delivery fee |
| free_delivery_minimum | DECIMAL(10,2) | | Minimum order for free delivery |
| delivery_radius | INTEGER | | Maximum delivery radius (km) |
| delivery_instructions | TEXT | | Special delivery instructions |
| third_party_delivery | BOOLEAN | DEFAULT FALSE | Whether third-party delivery is available |
| third_party_provider | VARCHAR(100) | | Third-party delivery provider |
| created_at | TIMESTAMP | DEFAULT NOW() | When record was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When record was updated |

### PickupLocations

Based on your pickup-locations.md wireframe:

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| listing_id | UUID | FK(listings.id), NOT NULL | Reference to listing |
| name | VARCHAR(100) | NOT NULL | Location name |
| address | JSONB | NOT NULL | Address details |
| location | GEOGRAPHY(POINT) | NOT NULL | Geographic location |
| hours | JSONB | | Operating hours |
| instructions | TEXT | | Pickup instructions |
| contact_phone | VARCHAR(20) | | Contact phone number |
| is_primary | BOOLEAN | DEFAULT FALSE | Whether this is the primary pickup location |
| created_at | TIMESTAMP | DEFAULT NOW() | When record was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When record was updated |

### BookingDelivery

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PK, NOT NULL | Unique identifier |
| booking_id | UUID | FK(bookings.id), NOT NULL | Reference to booking |
| delivery_type | VARCHAR(50) | NOT NULL | Type of delivery (self, owner, third-party) |
| delivery_status | VARCHAR(50) | DEFAULT 'scheduled' | Delivery status |
| tracking_number | VARCHAR(100) | | Delivery tracking number |
| tracking_url | VARCHAR(255) | | URL to tracking information |
| delivery_address | JSONB | NOT NULL | Delivery address |
| delivery_instructions | TEXT | | Delivery instructions |
| scheduled_date | TIMESTAMP | NOT NULL | Scheduled delivery date |
| actual_date | TIMESTAMP | | Actual delivery date |
| delivery_fee | DECIMAL(10,2) | NOT NULL | Delivery fee charged |
| created_at | TIMESTAMP | DEFAULT NOW() | When record was created |
| updated_at | TIMESTAMP | DEFAULT NOW() | When record was updated |